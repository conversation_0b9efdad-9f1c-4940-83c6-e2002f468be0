{"editor.tabSize": 4, "editor.wordWrap": "on", "editor.formatOnPaste": false, "editor.formatOnSave": true, "editor.formatOnType": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.autoSaveDelay": 2000, "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue", "[json]": {"editor.quickSuggestions": {"strings": true}, "editor.suggest.insertMode": "replace", "gitlens.codeLens.scopes": ["document"]}, "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[vue]": {"editor.defaultFormatter": "Vue.volar"}, "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[sass]": {"editor.defaultFormatter": "vscode.css-language-features"}, "[scss]": {"editor.defaultFormatter": "vscode.css-language-features"}, "eslint.format.enable": true, "eslint.validate": ["html", "javascript", "javascriptreact", "vue"], "typescript.validate.enable": false, "javascript.format.semicolons": "insert", "javascript.format.insertSpaceBeforeFunctionParenthesis": true, "javascript.updateImportsOnFileMove.enabled": "always", "javascript.preferences.quoteStyle": "single", "prettier.tabWidth": 4, "prettier.requireConfig": true, "prettier.bracketSpacing": false, "prettier.jsxSingleQuote": true, "prettier.singleQuote": true, "prettier.printWidth": 150000, "prettier.semi": true, "prettier.insertPragma": true, "prettier.trailingComma": "none", "prettier.arrowParens": "avoid", "prettier.proseWrap": "preserve", "prettier.singleAttributePerLine": false, "volar.format.initialIndent": {"html": true}, "vue.inlayHints.optionsWrapper": false, "vue.autoInsert.dotValue": true, "vue.updateImportsOnFileMove.enabled": true, "vue.server.vitePress.supportMdFile": true, "vue.inlayHints.inlineHandlerLeading": false, "vue.splitEditors.icon": true, "css.format.spaceAroundSelectorSeparator": true, "css.lint.duplicateProperties": "warning", "less.format.spaceAroundSelectorSeparator": true, "less.lint.duplicateProperties": "warning", "scss.format.spaceAroundSelectorSeparator": true, "scss.lint.duplicateProperties": "warning", "json.format.keepLines": true}