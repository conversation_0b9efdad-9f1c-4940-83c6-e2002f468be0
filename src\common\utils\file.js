/**
 * @method loadScript 加载js文件
 *
 * @param { Options } options 资源加载相关配置
 * @param { Boolean } forceReload 是否强制重新加载
 */
export const loadScript = (options, forceReload) => {
    return new Promise((resolve, reject) => {
        const { filePath, type, symbolName } = options || {};
        if (!filePath) return reject(new Error('filePath Empty'));
        window.scriptLoading = window.scriptLoading || {};
        if (window.scriptLoading[filePath]) {
            const maxCount = 10000;
            let currentCount = 0;
            let intevalObj = setInterval(() => {
                // console.log(
                //     `%c ${filePath}`,
                //     'font-size:18px;color:red;font-weight:700;',
                //     window.scriptLoading,
                //     window.scriptLoading[filePath]
                // );
                if (
                    !window.scriptLoading[filePath] ||
                    currentCount > maxCount
                ) {
                    resolve();
                    clearInterval(intevalObj);
                    intevalObj = null;
                } else {
                    currentCount++;
                }
            }, 500);
            return;
        }
        window.scriptLoading[filePath] = true;
        let selectorStr = `[src="${filePath}"]`;
        if (typeof symbolName === 'string' && symbolName) {
            selectorStr = `[resource-symbol="${symbolName}"]`;
        }
        const matchedItem = selectorStr && document.querySelector(selectorStr);
        if (matchedItem) {
            if (!forceReload) {
                window.scriptLoading[filePath] = false;
                return resolve();
            } else {
                matchedItem.remove();
            }
        }
        const script = document.createElement('script');
        script.type =
            typeof type === 'string' && type ? type : 'text/javascript';
        if (typeof symbolName === 'string' && symbolName) {
            script.setAttribute('resource-symbol', symbolName);
        }
        script.onload = () => {
            window.scriptLoading[filePath] = false;
            resolve();
        };
        script.onerror = () => {
            window.scriptLoading[filePath] = false;
            reject(new Error('Script Load Error'));
        };
        script.src = filePath;
        document.querySelector('head').appendChild(script);
    });
};

/**
 * @method dataURL2File 将base64字符串数据转为file类型文件
 * @param base64Data<String> 需转换的base64数据
 * @param name 要生成的文件名字
 */
export const dataURL2File = function (base64Data, name) {
    if (!base64Data) return '';

    const arr = base64Data.split(',');
    const fileType = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8Arr = new Uint8Array(n);

    while (n--) {
        u8Arr[n] = bstr.charCodeAt(n);
    }

    let filename = name || `file_${Date.now()}`;

    filename && (filename = `${filename}.${fileType.split('/')[1]}`);

    return new File([u8Arr], filename, {
        type: fileType
    });
};

/**
 * @method dataURL2Blob base64字符串数据转为blob
 */
export const dataURL2Blob = function (base64Data) {
    const arr = base64Data.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], {
        type: mime
    });
};
