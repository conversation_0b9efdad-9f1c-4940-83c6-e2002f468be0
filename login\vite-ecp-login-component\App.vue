<template>
    <form-login v-bind="formProps" @configRetry="getLoginConfigGlobal" @onSecondLogin="onSecondLogin"
        @onLoginSuccess="onLoginSuccess" @onLoginError="onLoginError" @onModifyPwd="onModifyPwd">
        <!-- 插槽传递 - 登录表单前置内容 -->
        <template #loginFormBefore v-if="$slots.loginFormBefore">
            <slot name="loginFormBefore"></slot>
        </template>

        <!-- 插槽传递 - 登录配置获取失败展示内容 -->
        <template #configError v-if="$slots.configError">
            <slot name="configError"></slot>
        </template>

        <!-- 插槽传递 - 图标 - 账号 -->
        <template #usernameIcon v-if="$slots.usernameIcon">
            <slot name="usernameIcon"></slot>
        </template>
        <!-- 插槽传递 - 图标 - 密码 -->
        <template #passwordIcon v-if="$slots.passwordIcon">
            <slot name="passwordIcon"></slot>
        </template>

        <!-- 插槽传递 - 登录表单后置内容 -->
        <template #loginFormAfter v-if="$slots.loginFormAfter">
            <slot name="loginFormAfter"></slot>
        </template>

        <!-- 插槽传递 - 自定义二次登录方式展示 -->
        <template #secondLogin="bindData" v-if="$slots.secondLogin">
            <slot name="secondLogin" v-bind="bindData"></slot>
        </template>
    </form-login>
</template>

<script setup>
import { ref, computed, defineComponent, defineProps, provide, onMounted, getCurrentInstance } from 'vue';

import { LoginApi } from './api';
import { LoginWayDict, PasswordVerifyDict } from './constants.dict';
import FormLogin from './components/form-login.vue';

defineComponent({
    name: 'ecp-login-component'
});

const { proxy } = getCurrentInstance();

const props = defineProps({
    config: {
        type: Object,
        default: () => ({})
    },
    redirectUrl: {
        type: String,
        default: ''
    },
    isComplexNetwork: Boolean,
    safeRequest: Boolean,

    customSecondLogin: Boolean,
    customLoginSuccess: Boolean,
    customLoginError: Boolean,
    customModifyPwd: Boolean,

    placeholderConfig: {
        type: Object,
        default: () => ({})
    }
});

const configError = ref(false);
const globalLoginConfig = ref(null);
const loginWay = ref([]);
const passwordVerify = ref(undefined);

const emits = defineEmits([
    'onSecondLogin',
    'onLoginSuccess',
    'onLoginError',
    'onModifyPwd'
]);

const formProps = computed(() => {
    const compProps = {
        config: props.config,
        redirectUrl: props.redirectUrl,
        isComplexNetwork: props.isComplexNetwork,
        safeRequest: props.safeRequest,

        customSecondLogin: props.customSecondLogin,
        customLoginSuccess: props.customLoginSuccess,
        customLoginError: props.customLoginError,
        customModifyPwd: props.customModifyPwd
    };
    const compData = {
        configError: configError.value,
        loginWay: loginWay.value,
        passwordVerify: passwordVerify.value,
        pwdMinLength: (globalLoginConfig.value || {}).PasswordLength,
        secondLoginWayStr: (globalLoginConfig.value || {}).LoginFactors
    };
    const compAttrs = {
        ...(proxy?.$attrs || {})
    };
    return {
        ...compAttrs,
        ...compProps,
        ...compData
    };
});

const getLoginConfigGlobal = async () => {
    try {
        const data = await LoginApi.getLoginConfigGlobal();
        globalLoginConfig.value = data.Data;
        loginWay.value = data.Data.LoginWay.split(',')
            .map(item => {
                return LoginWayDict[item];
            })
            .filter(item => item);
        passwordVerify.value = data.Data.PasswordVerify.split(',')
            .map(item => {
                return PasswordVerifyDict[item];
            })
            .filter(item => !!item);
        configError.value = false;
    } catch (error) {
        console.log(
            '%c [ECP-LOGIN] getLoginConfigGlobal Caught error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
        configError.value = true;
    }
};

const onSecondLogin = (...args) => {
    emits('onSecondLogin', ...args);
};

const onLoginSuccess = (...args) => {
    emits('onLoginSuccess', ...args);
};

const onLoginError = (...args) => {
    emits('onLoginError', ...args);
};

const onModifyPwd = (...args) => {
    emits('onModifyPwd', ...args);
};

onMounted(() => {
    getLoginConfigGlobal();
});
</script>
