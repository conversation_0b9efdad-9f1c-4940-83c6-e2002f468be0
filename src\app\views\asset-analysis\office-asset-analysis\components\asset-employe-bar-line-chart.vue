<template>
    <app-business-panel class="asset-employe-bar-line-chart" title="资产数量vs员工数量分析" mode="card">
        <template #default>
            <div class="asset-employe-bar-line-chart__content" v-loading="loading">
                <template v-if="graphData?.source?.length">
                    <ecp-chart-bar-line v-bind="chartProps" :key="`${graphTimestamp}_bar_line`" />
                </template>
                <ecp-empty v-else />
            </div>
        </template>
    </app-business-panel>
</template>

<script setup>
import { OfficeAssetAnalysis } from '@api/asset-analysis/office-asset-analysis';
import { CHART_COLOR_LIST } from '@constants/enum-config';
import { formatXAxis } from '@utils/format';

const loading = ref(false);

const legend = ref({
    top: 20,
    icon: 'emptyCircle'
});

const graphData = ref({
    dimensions: [],
    source: []
});

const graphTimestamp = ref(Date.now());

const unit = ['个', '人'];

const defaultOptions = {
    color: CHART_COLOR_LIST,
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    grid: {
        top: 40,
        right: 0,
        bottom: 0
    },
    xAxis: {
        axisLabel: {
            textStyle: {
                color: 'rgba(0, 0, 0, 0.45)'
            },
            rotate: 60,
            formatter: (value) => formatXAxis(value,6),
        }
    },
    yAxis: [
        {
            nameTextStyle: {
                padding: [0, 0, 0, -20],
                align: 'left'
            }
        },
        {
            nameTextStyle: {
                padding: [0, -20, 0, 0],
                align: 'right'
            }
        }
    ],
    series: [
        {
            type: 'bar',
            itemStyle: {
                borderRadius: 10
            }
        },
        {
            type: 'line',
            yAxisIndex: 1,
            lineStyle: {
                width: 0
            },
            symbol: 'emptyCircle',
            symbolSize: 6
        }
    ],
    tooltip: {
        formatter: (params) => {
            const xName = params[0].name;
            let html = '';
            for (const k in params) {
                const { color, value, seriesName } = params[k];
                if (value[seriesName] !== null) {
                    html += `
                            <div class="ecp-chart-tooltip-item">
                               <span class="ecp-chart-tooltip-label" style="--color: ${color}">${seriesName}</span>
                               <span class="ecp-chart-tooltip-value">
                                   <i class="ecp-chart-tooltip-value-num">${value[seriesName]}</i>
                                   <i class="ecp-chart-tooltip-value-unit">${unit[k]}</i>
                               </span>
                            </div>
                        `;
                }
            }
            return `
        <div class="ecp-chart-tooltip-wrapper is-white">
            <div class="ecp-chart-tooltip-head"><span>${xName}</span></div>
            ${html}
        </div>
        `;
        }
    }
};

const chartProps = computed(() => {
    const defaultProps = {
        theme: 'whiteTheme',
        data: graphData.value,
        legend: legend.value,
        yName: ['资产数量(个)', '员工数量(人)'],
        unit
    };
    return {
        ...defaultProps,
        option: {
            ...defaultOptions,
            barWidth: 10
        }
    };
});

const getChartData = async () => {
    loading.value = true;
    graphData.value.source = [];

    try {
        const [assetsData, employeesData] = await Promise.all([
            OfficeAssetAnalysis.getNumberOfAssets(),
            OfficeAssetAnalysis.getNumberOfEmployees()
        ]);
        const dimensions = ['所属单位', '资产数量', '员工数量'];
        const source = (assetsData?.Data || []).map(item => {
            const match = (employeesData?.Data || []).find(e => e.DataName === item.DataName);
            return {
                所属单位: item.DataName,
                资产数量: item.DataCount,
                员工数量: match ? match.DataCount : null
            };
        });

        graphData.value = {
            dimensions,
            source
        };
    } catch (error) {
        console.log('%c getTransferStatDataTrend Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    }

    loading.value = false;
};

onMounted(() => {
    getChartData();
});

</script>

<style scoped lang="scss">
.asset-employe-bar-line-chart {
    overflow: hidden;

    &__content {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
}
</style>
