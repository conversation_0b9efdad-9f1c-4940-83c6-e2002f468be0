<script setup>
import StatisticCard from './statistic-card.vue';
import { reactive } from 'vue';
import icon1 from '../images/realEstateArea.png';
import icon2 from '../images/landArea.png';
import icon3 from '../images/estateServiceArea.png';
import icon4 from '../images/literatureAndSportsAdsQuantity.png';
import icon5 from '../images/operationAssets.png';
import icon6 from '../images/officeAssets.png';
import * as Api from '@api/index';

const loading = ref(false);
const getStatisticData = async () => {
    try {
        loading.value = true;
        const response = await Api.AssetPanorama.getBaseDetail();
        const statisticCount = response.Data || [];
        for (const statisticDataElement in statisticData) {
            statisticData[statisticDataElement].value = statisticCount[statisticDataElement];
            const key = `Last${statisticDataElement}Percent`;
            statisticData[statisticDataElement].monthlyChainRatio = statisticCount[key].replace('-', '') || '0%';
            statisticData[statisticDataElement].increase = !statisticCount[key].startsWith('-');
        }
    } catch (e) {
        console.log('%c getStatisticData error', 'color: red', e);
    } finally {
        loading.value = false;
    }
};

onMounted(getStatisticData);
const context = 'asset-panorama';
const statisticData = reactive({
    TotalArea: { icon: icon1, value: 0, FunTag: 'ASSET_FULL_VIEW_HOUSE', title: '累计房产面积', unit: '万平方', monthlyChainRatio: '0%', name: `${context}-house-asset` },
    TotalLandArea: { icon: icon2, value: 0, FunTag: 'ASSET_FULL_VIEW_LAND', title: '累计土地面积', unit: '亩', monthlyChainRatio: '0%', name: `${context}-land-asset` },
    TotalPropertyArea: { icon: icon3, value: 0, FunTag: 'ASSET_FULL_VIEW_PROPERTY', title: '累计物业面积', unit: '万平方', monthlyChainRatio: '0%', name: `${context}-property-asset` },
    TotalAdvert: { icon: icon4, value: 0, FunTag: 'ASSET_FULL_VIEW_ADVERTISEMENT', title: '累计文体广告数量', unit: '万个', monthlyChainRatio: '0%', name: `${context}-advertisement-asset` },
    TotalOperationAsset: { icon: icon5, value: 0, FunTag: 'ASSET_FULL_VIEW_OPERATING', title: '累计运营资产数量', unit: '万个', monthlyChainRatio: '0%', name: `${context}-operating-asset` },
    TotalWorkAsset: { icon: icon6, value: 0, FunTag: 'ASSET_FULL_VIEW_OFFICE', title: '累计办公资产数量', unit: '万个', monthlyChainRatio: '0%', name: `${context}-office-asset` }
});
</script>

<template>
    <div class="statistic-container">
        <statistic-card v-bind="{ ...item }" v-for="(item, index) in statisticData" :key="index"
            :split="index !== 'TotalArea'" :loading="loading" />
    </div>
</template>

<style scoped lang="scss">
.statistic-container {
    display: flex;
    justify-content: space-between;
}
</style>
