<template>
    <div class="asset-use" style="height: 100%;width: 100%;">
        <ecp-layout-pagination>
            <template #head>
                <div class="header-toolbar">
                    <el-date-picker v-model="_createDate" type="month" placeholder="选择年月"
                        :disabled-date="disabledFutureDates" style="width: 150px" :clearable="false" />
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading" @click="ExportAssetUseTable" />
                </div>
            </template>
            <template #content>
                <app-dynamic-table :loading="loading" :table-data="tableData" :table-config="tableConfig" />
            </template>
        </ecp-layout-pagination>
    </div>
</template>
<script setup>
import * as Api from '@api/index';
import dayjs from 'dayjs';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
import { ASSET_USAGE_TABLE_CONFIG } from '../constants';

const props = defineProps({
    disabledFutureDates: {
        type: Function,
        default: () => true
    }
});

const _createDate = ref(new Date());
const loading = ref(false);
const tableData = ref([]);
const timeSpan = computed(() => {
    const arr = [];
    const list = tableData.value.map(item => item.Source);
    const copy = new Set(list);
    copy.forEach((item) => {
        const count = list.filter(i => i === item).length;
        arr.push(...[count, ...Array.from({ length: count - 1 }, () => 0)]);
    });
    return arr;
});
const tableConfig = computed(() => ({
    ...ASSET_USAGE_TABLE_CONFIG,
    props: { spanMethod }
}));
const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
    const span = timeSpan.value;
    if (row.Source === row.AssetType && columnIndex <= 1) {
        return {
            rowspan: columnIndex,
            colspan: 2
        };
    }
    if (columnIndex === 0) {
        return {
            rowspan: span[rowIndex],
            colspan: 1
        };
    }
    return {
        rowspan: 1,
        colspan: 1
    };
};

const createDate = computed(() => dayjs(_createDate.value).format('YYYY-MM'));

const GetAssetUseTable = async () => {
    try {
        loading.value = true;
        const response = await Api.AssetAnalysis.GetAssetUseTable({ createDate: createDate.value });
        const other = response.Data.filter(item => item.Source === '其他').map(item => ({ ...item, Source: item.AssetType }));
        const total = response.Data.filter(item => item.Source === '总计');
        const mergedData = response.Data.filter(item => item.Source !== '其他' && item.Source !== '总计');
        tableData.value = mergedData.concat([...other, ...total]);
    } catch (e) {
        console.log('%c GetAssetUseTable', 'font-size:18px;color:green;font-weight:700;', e);
    } finally {
        loading.value = false;
    }
};

const ExportAssetUseTable = async () => {
    try {
        if (tableData.value.length > 0) {
            const response = await Api.AssetAnalysis.ExportAssetUseTable({ createDate: createDate.value });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (e) {
        console.log('%c ExportAssetUseTable', 'font-size:18px;color:green;font-weight:700;', e);
        ElMessage.error('导出失败');
    }
};

watch(_createDate, GetAssetUseTable, { immediate: true });
</script>
