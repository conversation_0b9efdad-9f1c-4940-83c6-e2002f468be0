<!-- 运营资产 -->
<template>
    <div class="stylistic-advertising" style="height: 100%;width: 100%;">
        <ecp-layout-pagination>
            <template #head>
                <div class="header-toolbar">
                    <div class="header-toolbar__selector">
                        <el-date-picker v-model="_createDate" type="month" placeholder="选择年月"
                            :disabled-date="disabledFutureDates" style="width: 150px" :clearable="false" />
                    </div>
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading" @click="ExportEvent" />
                </div>
            </template>
            <template #content>
                <!-- <ecp-tag v-model="type" :options="tagTypeOptions" @change="handleAssetData(type)" /> -->
                <el-tabs type="card" v-model="type" @tab-change="onTabsChange">
                    <el-tab-pane label="故障资产统计" name="failure"></el-tab-pane>
                    <el-tab-pane label="线路资产统计" name="line"></el-tab-pane>
                </el-tabs>
                <template v-if="type === 'failure'">
                    <div v-loading="loading" class="failure-table">
                        <app-dynamic-table :table-data="usageData" :table-config="useConditionConfig" />
                        <app-dynamic-table :table-data="faultData" :table-config="failureItemConfig" />
                    </div>
                </template>
                <app-dynamic-table class="line-table" :loading="loading" :table-data="tableData"
                    :table-config="tableConfig" v-else />
            </template>
        </ecp-layout-pagination>
    </div>
</template>

<script setup>
import * as Api from '@api/index';
import dayjs from 'dayjs';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
import { StatisticalReport } from '@api/asset-analysis/statistical-report';

const props = defineProps({
    disabledFutureDates: {
        type: Function,
        default: () => true
    }
});
const _createDate = ref(new Date());
const loading = ref(false);
const tableData = ref([]);
const lines = ref([]);
const createDate = computed(() => dayjs(_createDate.value).format('YYYY-MM'));

const selectValue = reactive({
});

const usageData = ref([{
    UseCondition: '数量',
    Usage: 0,
    Repair: 0,
    PendingUsage: 0,
    Scrap: 0,
    Total: 0
}]);

const faultData = ref([{
    UseCondition: '数量',
    VehicleSystem: 0,
    SignalSystem: 0,
    PowerSupplySystem: 0,
    TrackSystem: 0,
    PlatformScreenDoor: 0,
    EscalatorAndElevator: 0,
    TicketingAndInspection: 0,
    Total: 0
}]);

const generateUseConditionConfig = (vos) => {
    const useConditionCols = [
        { label: '使用状态', prop: 'UseCondition' }
    ];

    const useStatus = vos.filter(item => ['使用', '维修', '待利用', '报废'].includes(item.TypeOrProject));
    useStatus.forEach(item => {
        useConditionCols.push({
            label: item.TypeOrProject,
            prop: item.TypeOrProject,
            align: 'right'
        });
    });

    useConditionCols.push({ label: '合计', prop: 'Total', align: 'right' });

    return {
        props: {},
        cols: useConditionCols
    };
};

const generateFailureItemConfig = (vos) => {
    const failureItemCols = [
        { label: '故障项目', prop: 'UseCondition' }
    ];

    const failureItems = vos.filter(item => !['使用', '维修', '待利用', '报废'].includes(item.TypeOrProject));
    failureItems.forEach(item => {
        failureItemCols.push({
            label: item.TypeOrProject,
            prop: item.TypeOrProject,
            align: 'right'
        });
    });

    failureItemCols.push({ label: '合计', prop: 'Total', align: 'right' });
    return {
        props: {},
        cols: failureItemCols
    };
};

const useConditionConfig = ref({ cols: [] });
const failureItemConfig = ref({ cols: [] });

const GetAssetTable = async () => {
    try {
        const response = await StatisticalReport.ShowFaultEstateTable({ createDate: createDate.value });
        const { Vos } = response.Data;

        useConditionConfig.value = generateUseConditionConfig(Vos);
        failureItemConfig.value = generateFailureItemConfig(Vos);

        usageData.value = [{
            UseCondition: '数量',
            Total: response.Data.StatusTotal || 0
        }];

        faultData.value = [{
            UseCondition: '数量',
            Total: response.Data.ErrorProjectTotal || 0
        }];

        if (Array.isArray(Vos)) {
            Vos.forEach(item => {
                if (['使用', '维修', '待利用', '报废'].includes(item.TypeOrProject)) {
                    usageData.value[0][item.TypeOrProject] = item.Amount || 0;
                } else {
                    faultData.value[0][item.TypeOrProject] = item.Amount || 0;
                }
            });
        } else {
            console.error('Vos data is not an array', Vos);
        }
    } catch (e) {
        console.log('%c GetAssetTable', 'font-size:18px;color:green;font-weight:700;', e);
    }
};

watch(() => _createDate, GetAssetTable, { deep: true, immediate: true });

const type = ref('failure');
const tagTypeOptions = [
    { label: '故障资产统计', value: 'failure' },
    { label: '线路资产统计', value: 'line' }
];

const handleAssetData = (selectedType) => {
    type.value = selectedType;
};

const tableConfig = computed(() => ({
    cols: [
        { label: '使用状态', prop: 'status', width: '100px' },
        ...Array.from(lines.value, value => ({ label: value, prop: value, align: 'right', width: 'auto' })),
        { label: '合计', prop: 'amount', align: 'right', width: 'auto' }
    ]
}));

const ShowLineList = async () => {
    try {
        loading.value = true;
        const response = await StatisticalReport.ShowLineListTable({ createDate: createDate.value });
        const {
            LinesInUsingAmount,
            LinesRetireAmount,
            LinesStatusAmount,
            LinesStopUsingAmount,
            LinesUnUsingAmount,
            // LinesRepairAmount,
            List
        } = response.Data;
        lines.value = List.map(item => item.LineDesc);
        if (Array.isArray(List) && !List.length) {
            tableData.value = [];
            return;
        }
        const getValue = (key) => {
            const dict = {};
            List.forEach((item, index) => {
                dict[lines.value[index]] = item[key];
            });
            return dict;
        };
        const used = getValue('InUsingAmount');
        const stop = getValue('StopUsingAmount');
        const scrap = getValue('RetireAmount');
        const idle = getValue('UnUsingAmount');
        const status = getValue('StatusAmount');
        const repair = getValue('RepairAmount');
        tableData.value = [
            { status: '使用中', ...used, amount: LinesInUsingAmount },
            { status: '停用', ...stop, amount: LinesStopUsingAmount },
            // { status: '维修', ...repair, amount: LinesRepairAmount },
            { status: '报废', ...scrap, amount: LinesRetireAmount },
            { status: '闲置', ...idle, amount: LinesUnUsingAmount },
            { status: '合计', ...status, amount: LinesStatusAmount }
        ];
    } catch (e) {
        console.log('%c ShowLineList', 'font-size:18px;color:green;font-weight:700;', e);
    } finally {
        loading.value = false;
    }
};

const ExportEvent = async () => {
    try {
        if (type.value === 'failure') {
            const response = await StatisticalReport.ExportFaultEstateTable({ createDate: createDate.value });
            downloadBlobData(response);
        } else if (type.value === 'line') {
            const response = await StatisticalReport.ExportLineDeviceUseTable({ createDate: createDate.value });
            downloadBlobData(response);
        }
        ElMessage.success('导出成功');
    } catch (e) {
        console.log('%c ExportEvent', 'font-size:18px;color:green;font-weight:700;', e);
        ElMessage.error('导出失败');
    }
};

watch(() => _createDate, ShowLineList, { deep: true, immediate: true });

</script>

<style scoped lang="scss">
.stylistic-advertising {
    .failure-table {
        min-height: 300px;
    }
}
</style>
