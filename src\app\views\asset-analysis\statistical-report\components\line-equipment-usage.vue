<template>
    <div class="line-equipment-use" style="height: 100%;width: 100%;">
        <ecp-layout-pagination>
            <template #head>
                <div class="header-toolbar">
                    <el-date-picker v-model="_createDate" type="month" placeholder="选择年月"
                        :disabled-date="disabledFutureDates" style="width: 150px" :clearable="false" />
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading" @click="ExportEvent" />
                </div>
            </template>
            <template #content>
                <app-dynamic-table :loading="loading" :table-data="tableData" :table-config="tableConfig" />
            </template>
        </ecp-layout-pagination>
    </div>
</template>

<script setup>
import * as Api from '@api/index';
import dayjs from 'dayjs';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';

const props = defineProps({
    disabledFutureDates: {
        type: Function,
        default: () => true
    }
});

const _createDate = ref(new Date());
const loading = ref(false);
const lines = ref([]);
const tableData = ref([]);
const createDate = computed(() => dayjs(_createDate.value).format('YYYY-MM'));
const tableConfig = computed(() => ({
    cols: [
        { label: '使用状态', prop: 'status', width: '100px' },
        ...Array.from(lines.value, value => ({ label: value, prop: value, align: 'right', width: 'auto' })),
        { label: '合计', prop: 'amount', align: 'right', width: 'auto' }
    ]
}));

const ShowLineList = async () => {
    try {
        loading.value = true;
        const response = await Api.AssetAnalysis.ShowLineList({ createDate: createDate.value });
        const {
            LinesInUsingAmount,
            LinesRetireAmount,
            LinesStatusAmount,
            LinesStopUsingAmount,
            LinesUnUsingAmount,
            List
        } = response.Data;
        lines.value = List.map(item => item.LineDesc);
        if (Array.isArray(List) && !List.length) {
            tableData.value = [];
            return;
        }
        const getValue = (key) => {
            const dict = {};
            List.forEach((item, index) => {
                dict[lines.value[index]] = item[key];
            });
            return dict;
        };
        const used = getValue('InUsingAmount');
        const stop = getValue('StopUsingAmount');
        const scrap = getValue('RetireAmount');
        const idle = getValue('UnUsingAmount');
        const status = getValue('StatusAmount');
        tableData.value = [
            { status: '使用中', ...used, amount: LinesInUsingAmount },
            { status: '停用', ...stop, amount: LinesStopUsingAmount },
            { status: '报废', ...scrap, amount: LinesRetireAmount },
            { status: '闲置', ...idle, amount: LinesUnUsingAmount },
            { status: '合计', ...status, amount: LinesStatusAmount }
        ];
    } catch (e) {
        console.log('%c ShowLineList', 'font-size:18px;color:green;font-weight:700;', e);
    } finally {
        loading.value = false;
    }
};

const ExportEvent = async () => {
    try {
        if (tableData.value.length > 0) {
            const response = await Api.AssetAnalysis.ExportLineDeviceUseTable({ createDate: createDate.value });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (e) {
        console.log('%c ExportEvent', 'font-size:18px;color:green;font-weight:700;', e);
        ElMessage.error('导出失败');
    }
};

watch(_createDate, ShowLineList, { immediate: true });
</script>
<style lang="scss">
.line-equipment-use {
    position: relative;
    height: 100%;
}
</style>
