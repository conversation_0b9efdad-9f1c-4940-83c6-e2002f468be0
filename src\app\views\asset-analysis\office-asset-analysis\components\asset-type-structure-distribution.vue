<template>
    <div class="asset-type-structure-distribution">
        <div ref="chartContainer" class="chart-container"></div>
        <div class="legend-container">
            <div v-for="(row, rowIndex) in rows" :key="rowIndex" class="legend-row">
                <div v-for="(item, index) in row" :key="index" class="legend-item">
                    <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
                    <div class="legend-text">
                        <span class="legend-name" :title="item.name">{{ item.name }}</span>
                        <span class="legend-value">{{ item.value }}%</span>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
    chartData: {
        type: Array,
        required: true
    }
});

const chartContainer = ref(null);
let chartInstance = null; // 保存图表实例

const renderChart = () => {
    if (!chartContainer.value) return; // 确保容器存在

    if (!chartInstance) {
        chartInstance = echarts.init(chartContainer.value); // 初始化图表实例
    }

    const option = {
        tooltip: {
            trigger: 'item',
            textStyle: {
                fontSize: 10
            },
            extraCssText:
                'padding: 0; border-radius: 0.4em; border: 0; overflow: hidden;',
            backgroundColor: '#fff',
            formatter: (params) => {
                const color = params.color;
                const value = params?.value || '';
                const seriesName = params.seriesName;

                return `
                    <div class="ecp-chart-tooltip-wrapper is-white">
                        <div class="ecp-chart-tooltip-head">${seriesName}</div>
                        <div class="ecp-chart-tooltip-item">
                            <span class="ecp-chart-tooltip-label" style="--color: ${color}">占比</span>
                            <span class="ecp-chart-tooltip-value">
                                <i class="ecp-chart-tooltip-value-num">${value}</i>
                                <i class="ecp-chart-tooltip-value-unit">%</i>
                            </span>
                        </div>
                    </div>`;
            }
        },
        dataZoom: [
            {
                type: 'inside'
            }
        ],
        xAxis: {
            type: 'value',
            min: 0, // 设置最小值为0，避免数值过小被压缩
            axisLabel: { show: false },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false }
        },
        yAxis: {
            type: 'category',
            min: 0,
            axisLabel: { show: false },
            axisLine: { show: false },
            axisTick: { show: false }
        },
        series: props.chartData.map(item => ({
            name: item.name,
            type: 'bar',
            stack: 'total',
            data: [item.value],
            barMinHeight: 10, // 设置最小高度，确保即使值很小也能显示
            itemStyle: {
                color: item.color,
                borderColor: '#fff',
                borderWidth: 2
            },
            barWidth: '30%',
            label: {
                show: false
            }
        })),
        grid: {
            left: 0,
            right: 0,
            bottom: 0,
            top: 5
        },
        barGap: '0%',
        barCategoryGap: '40%'
    };

    chartInstance.setOption(option);
    window.addEventListener('resize', () => {
        chartInstance.resize();
    });
};

let resizeObserver = null;

onMounted(() => {
    nextTick(() => {
        renderChart();
    });

    resizeObserver = new ResizeObserver(() => {
        if (chartInstance) {
            chartInstance.resize();
        }
    });
    resizeObserver.observe(chartContainer.value);
});

watch(
    () => props.chartData,
    (newData) => {
        if (newData && newData.length > 0) {
            renderChart();
        }
    },
    { immediate: true, deep: true }
);

onBeforeUnmount(() => {
    if (chartInstance) {
        chartInstance.dispose();
    }
    if (resizeObserver) {
        resizeObserver.disconnect();
    }
});

const rows = computed(() => {
    const grouped = [];
    for (let i = 0; i < props.chartData.length; i += 2) {
        grouped.push(props.chartData.slice(i, i + 2)); // 每行两个元素
    }
    return grouped;
});

</script>

<style scoped lang="scss">
.asset-type-structure-distribution {
    width: 100%;
    height: 100%;

}

.chart-container {
    width: 100%;
    height: 120px;
}

.legend-container {
    height: calc(100% - 120px);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    margin: 0 2px;

    .legend-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid var(--border-color-light);
        padding: 14px 0;

        &:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        &:first-child {
            padding-top: 0;
        }

        .legend-item {
            display: flex;
            align-items: center;
            width: 45%;

            .legend-color {
                width: 12px;
                height: 12px;
                margin-right: var(--spacer)
            }

            .legend-text {
                display: flex;
                justify-content: space-between;
                flex: 1;
                width: calc(100% - 12px - var(--spacer));

                .legend-name {
                    color: var(--color-text-regular);
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                    max-width: 70%;
                }

                .legend-value {
                    color: var(--color-text-primary);
                    font-family: D-DIN;
                    flex-shrink: 0;
                }
            }
        }
    }
}

/*隐藏滚动条，保留滚动功能 */
.legend-container::-webkit-scrollbar {
    width: 0px;
}
</style>
