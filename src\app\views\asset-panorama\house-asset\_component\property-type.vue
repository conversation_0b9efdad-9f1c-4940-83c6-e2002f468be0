<script setup>
import * as Api from '@api/index';

const loading = ref(false);
const tooltipFrame = (html, title, theme = 'light') => {
    return `
        <div class="ecp-chart-tooltip-wrapper ${theme === 'light' ? 'is-white' : 'is-black'}">
             <div class="ecp-chart-tooltip-head">${title}</div>
             ${html}
        </div>
        `;
};
const tooltipItem = (color, name, value, unit) => {
    return `
        <div class="ecp-chart-tooltip-item">
            <span class="ecp-chart-tooltip-label" style="--color: ${color}">${name}</span>
            <span class="ecp-chart-tooltip-value">
                <i class="ecp-chart-tooltip-value-num">${value}</i>
                <i class="ecp-chart-tooltip-value-unit">${unit}</i>
            </span>
       </div>
   `;
};
const _color = ['#487690', '#11B062', '#FFD022', '#0367FC'];
const option = reactive({
    legend: {
        show: false
    },
    tooltip: {
        formatter: (params) => {
            let html = '';
            let title;
            for (const param of params) {
                const { color, value, name } = param;
                title = name;
                html += tooltipItem(color, '面积', value['面积'], '万平方');
            }
            return tooltipFrame(html, title);
        }
    },
    grid: {
        left: '-60px',
        right: '3%',
        top: '25px'
    },
    xAxis: {
        axisLabel: {
            textStyle: {
                color: 'rgba(0, 0, 0, 0.45)',
                fontFamily: 'D-DIN'
            }
        }
    },
    yAxis: {
        // data: ['还建房', '已征未拆', '租赁房', '住宅'],
        name: '单位：万平方',
        nameTextStyle: {
            padding: [0, -60, 0, 0]
        },
        axisLabel: {
            color: '#1D2137',
            fontSize: 16,
            align: 'left',
            verticalAlign: 'bottom',
            padding: [0, 0, 15, 0],
            formatter: '   {value}'
        }
    },
    series: [
        {
            type: 'bar',
            barWidth: 15,
            showBackground: true,
            backgroundStyle: {
                borderRadius: 10
            },
            labelLayout: () => ({
                x: '97%',
                align: 'right',
                verticalAlign: 'bottom'
            }),
            label: {
                show: true,
                position: 'top',
                formatter: (params) => params.value['面积'].toLocaleString(),
                textStyle: {
                    color: '#1D2137',
                    fontSize: 16
                }
            },
            itemStyle: {
                color: (params) => _color[params.dataIndex],
                borderRadius: 10
            }
        }
    ]
});
const data = reactive({
    dimensions: ['类目名称', '面积'],
    source: []
});

const RealEstateType = async () => {
    try {
        loading.value = true;
        const response = await Api.AssetPanorama.RealEstateType();
        for (const item of response.Data) {
            data.source.push({ 类目名称: item.PropertyTypeName, 面积: item.PropertyAreas });
        }
    } catch (error) {
        console.log('%c RealEstateType Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    } finally {
        loading.value = false;
    }
};

onMounted(RealEstateType);
</script>

<template>
    <app-business-panel mode="card" title="房产类型">
        <div class="property-type" v-loading="loading">
            <ecp-chart-base-bar :data="data" :option="option" theme="whiteTheme" horizontal />
        </div>
    </app-business-panel>
</template>

<style scoped lang="scss">
.property-type {
    padding-top: 40px;
    height: 100%;

    :deep {
        $bar-color: #0367FC, #FFD022, #11B062, #487690;

        .info-name {
            font-size: 16px !important;
            font-weight: normal !important;
            line-height: normal !important;
            color: #1D2137 !important;
        }

        .num {
            font-family: Source Han Sans SC !important;
            font-weight: normal !important;
            line-height: normal !important;
            font-size: 16px !important;
            color: #1D2137 !important;
        }
    }
}
</style>
