<script setup>
import * as echarts from 'echarts';
import { onMounted, onUnmounted } from 'vue';

defineComponent({
    name: 'app-chart-treemap'
});
const chartRef = ref(null);
let Chart = null;
const props = defineProps({
    theme: {
        type: String,
        default: 'light'
    },
    text: {
        type: String,
        default: ''
    },
    options: {
        type: Object,
        default: () => ({})
    },
    unit: {
        type: String,
        default: ''
    }
});

const timer = ref(null);

const onResize = () => {
    Chart.resize();
};

const tooltipFrame = (html, params) => {
    return `
        <div class="ecp-chart-tooltip-wrapper ${props.theme === 'light' ? 'is-white' : 'is-black'}">
             <div class="ecp-chart-tooltip-head">${props.text}</div>
             ${html}
        </div>
        `;
};

const tooltipItem = (params) => {
    return `
        <div class="ecp-chart-tooltip-item">
            <span class="ecp-chart-tooltip-label" style="--color: ${params.color}">${params.name}</span>
            <span class="ecp-chart-tooltip-value">
                <i class="ecp-chart-tooltip-value-num">${params.value}</i>
                <i class="ecp-chart-tooltip-value-unit">${props.unit}</i>
            </span>
        </div>
   `;
};

const init = () => {
    if (!chartRef.value) return;
    Chart = echarts.init(chartRef.value, props.theme);
    setOption();
};

const setOption = () => {
    const options = {
        color: ['#0ABFB9', '#0F63F2', '#2FC800', '#EED600', '#F89714', '#B6201B', '#D84B8A', '#A454B9', '#487690', '#487690', '#704A1B', '#91A3B0', '#CDD6DF'],
        tooltip: {
            textStyle: {
                fontSize: 10
            },
            extraCssText:
                    'padding: 0; border-radius: 0.4em; border: 0; overflow: hidden;',
            backgroundColor: '#fff',
            formatter (params) {
                return tooltipFrame(tooltipItem(params), params);
            }
        },
        ...props.options
    };
    options && Chart.setOption(options);
};

watch(() => props.options, () => setOption(), { deep: true });

onMounted(() => {
    init();
});

onUnmounted(() => {
    Chart.dispose();
});
</script>

<template>
    <div class="app-chart-treemap" ref="chartRef" v-resize="onResize"></div>
</template>

<style scoped lang="scss">
.app-chart-treemap {
    width: 100%;
    height: 100%;
}
</style>
