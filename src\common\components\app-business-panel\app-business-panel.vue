<!--
<docs lang="md">
# 带title的panel组件

## props
- title：业务面板的标题名称
- showHeader：是否显示业务面板头部
- mode：样式模式，可选值有card，为卡片样式，默认为空
- interval: 内容间距

## slot
- header：title内容填充
- header-append：title后面内容填充
- content：面板主体内容
</docs>
-->

<template>
    <div class="app-business-panel" :class="[panelMode, { 'no-header': !showHeader }]">
        <div class="app-business-panel-header" :class="{ 'interval-header': intervalHeader }" v-if="showHeader">
            <div class="app-business-panel-header-left">
                <slot name="header" v-if="$slots.header"></slot>
                <div class="app-business-panel-title" v-else>{{ title }}</div>
            </div>
            <div class="app-business-panel-header-right" v-if="$slots['header-append']">
                <slot name="header-append"></slot>
            </div>
        </div>
        <div class="app-business-panel-content" :class="{ interval }">
            <slot name="content" v-if="$slots.content"></slot>
            <slot v-else></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'app-business-panel',
    props: {
        title: {
            type: String,
            default: ''
        },
        showHeader: {
            type: Boolean,
            default: true
        },
        mode: {
            type: String,
            default: ''
        },
        interval: {
            type: Boolean,
            default: true
        },
        intervalHeader: {
            type: Boolean,
            default: true
        }
    },
    computed: {
        panelMode () {
            let result = '';
            switch (this.mode) {
                case 'card':
                    result = 'card-panel';
                    break;

                default:
                    result = 'normal-panel';
                    break;
            }
            return result;
        }
    }
};
</script>

<style lang="scss" scoped>
.app-business-panel {
    display: flex;
    flex-direction: column;

    .app-business-panel-header {
        display: flex;
        align-items: center;
        padding: var(--spacer-large-3) 0 var(--spacer-large);
        position: relative;

        &.interval-header {
            padding: var(--spacer-large-3) var(--spacer-large-3) var(--spacer-large);
        }

        &>.app-business-panel-header-left {
            display: flex;
            flex: 1 1 auto;
        }

        &>.app-business-panel-header-right {
            padding: 0 0 0 var(--spacer-large-3);
            flex: 0 0 auto;
        }

        :deep(.app-business-panel-title) {
            display: inline-flex;
            width: auto;
            // padding: 1px var(--spacer);
            font-size: var(--font-size-base);
            line-height: var(--font-line-height-primary);
            color: var(--color-text-primary);
            border-left: 2px solid var(--color-primary);
            background: linear-gradient(270deg,
                    var(--color-primary-opacity-0) 0%,
                    var(--color-primary-opacity-1) 100%);
        }
    }

    .app-business-panel-content {
        flex: 1 1 auto;

        &.interval {
            padding: 0 var(--spacer-large-3);
        }
    }

    &.normal-panel {
        &:not(.nested-panel):nth-last-child(1) {
            padding-bottom: var(--spacer-large-3);
        }

        .app-business-panel-title {
            min-width: calc(var(--spacer) * 20);
        }
    }

    &.card-panel {
        background-color: var(--background-color);
        border-radius: 8px;
        box-shadow: 0px 2px 6px 0px rgba(29, 33, 55, 0.12);
        overflow: hidden;
        padding: var(--spacer-large) var(--spacer-large-3) var(--spacer-large-3) var(--spacer-large-3);

        &:only-child {
            flex-grow: 1;
        }

        &>.app-business-panel-header {
            padding: 0;

            :deep(.app-business-panel-title) {
                line-height: var(--font-line-height-medium);
                font-size: var(--font-size-medium);
                color: var(--color-text-primary);
                border: none;
                background: none;
            }
        }

        &>.app-business-panel-content.interval {
            padding: 0;
        }

        &+.app-business-panel.card-panel {
            margin-top: var(--spacer-large);
        }

        &+.normal-panel {
            >.app-business-panel-header {
                padding-top: 0;
            }
        }
    }

}
</style>
