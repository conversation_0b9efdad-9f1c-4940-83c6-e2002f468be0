{"name": "asset-manage-web", "description": "资产分析决策引擎", "private": true, "version": "1.0.0", "imageName": "asset-manage-web", "imageHubName": "harbor.pcitech.com/rtbigdata/frontend/asset-manage-web", "harbor": {"user": "admin", "password": "suntek", "host": "harbor.pcitech.com"}, "scripts": {"dev": "vite --host --force", "dev:uni": "vite --mode=uni --host --force", "serve": "vite --host --force", "serve:uni": "vite --mode=uni --host --force", "-": "", "build": "vite build", "build:uni": "vite build --mode=uni", "preview": "vite preview", "preview:uni": "vite preview --mode=previewUni", "--": "", "lint": "eslint --fix ./", "---": "", "directory": "node ./directory-update.js"}, "dependencies": {"@ecp/ecp-login-component-vite": "^3.0.0", "@ecp/ecp-ui-plus": "^1.0.2", "@element-plus/icons-vue": "^2.3.1", "@turf/turf": "^7.1.0", "axios": "^1.3.5", "dayjs": "^1.11.7", "echarts": "5.5.0", "ecp-chart": "^2.0.4", "element-plus": "^2.8.0", "js-cookie": "^3.0.1", "pinia": "^2.0.34", "qs": "^6.11.1", "suntekmap": "^1.1.2", "vue": "^3.3.2", "vue-router": "^4.1.6", "vue-virtual-scroller": "2.0.0-beta.8"}, "devDependencies": {"@ecp/version-vite-plugin": "^0.1.5", "@types/chart.js": "^2.9.41", "@vitejs/plugin-legacy": "^4.0.2", "@vitejs/plugin-vue": "^4.1.0", "eslint": "^8.0.1", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-vue": "^9.10.0", "prettier": "^2.8.7", "sass": "^1.62.0", "unplugin-auto-import": "^0.15.2", "unplugin-icons": "^0.16.1", "unplugin-vue-components": "^0.24.1", "vite": "^4.5.0", "vite-plugin-legacy-qiankun": "^0.0.7", "vite-plugin-svg-icons": "^2.0.1"}, "engine": "14.18"}