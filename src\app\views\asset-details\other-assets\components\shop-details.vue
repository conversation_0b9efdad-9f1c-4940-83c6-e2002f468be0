<script setup>
import { STORE_DETAILS } from '../_constants/index';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
import * as Api from '@api/index';
const total = ref(0);
const loading = reactive({
    table: false,
    export: false
});
const tableData = reactive({
    total: 0,
    list: []
});
const searchForm = reactive({
    typeCode: null, // 商铺类型
    udLineCode: null // 所属线路
});
const pagination = reactive({
    pageSize: 10,
    pageNum: 1
});
// 获取表格数据
const getStoreDetailList = async () => {
    try {
        loading.table = true;
        const response = await Api.AssetDetails.getStoreDetailsList({ ...searchForm, ...pagination });
        const { Total, Data } = response;
        tableData.list = Data;
        tableData.total = Total;
    } catch (error) {
        console.log(error);
        tableData.list = [];
        tableData.total = 0;
    } finally {
        loading.table = false;
    }
};
const lineDescOptions = ref([]);
// 获取所属线路
const getLineDesc = async () => {
    try {
        const { Data } = await Api.AssetDetails.getLineDesc();
        lineDescOptions.value = Data;
    } catch (error) {
    }
};
const storeTypeOptions = ref([]);
// 获取商铺类型
const getStoreObjectType = async () => {
    try {
        const { Data } = await Api.AssetDetails.getStoreObjectType();
        storeTypeOptions.value = Object.entries(Data).map(([key, value]) => {
            return { storeTypeCode: key, storeTypeName: value };
        }
        );
        console.log(storeTypeOptions.value);
    } catch (error) {
        console.log(error);
    }
};
// 导出商铺表格数据
const ExportEvent = async () => {
    try {
        loading.export = true;
        if (tableData.total > 0) {
            const response = await Api.AssetDetails.exportStoreDetailList({
                pageSize: 10001,
                pageNum: 1,
                ...searchForm
            });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (error) {
        console.log(error);
    } finally {
        loading.export = false;
    }
};
const SearchEvent = () => {
    pagination.pageNum = 1;
    pagination.pageSize = 10;
    getStoreDetailList();
};
const ResetEvent = () => {
    searchForm.typeCode = null;
    searchForm.udLineCode = null;
    pagination.pageNum = 1;
    pagination.pageSize = 10;
    getStoreDetailList();
};
const ecpLayoutPaginationRef = ref();
onMounted(() => {
    getStoreDetailList();
    getStoreObjectType();
    getLineDesc();
    console.log(ecpLayoutPaginationRef.value);
});
</script>

<template>
    <ecp-layout-pagination content-scroll :total="tableData.total" v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize" :top-pagination="false" layout="prev, pager, next, sizes, jumper"
        @query="getStoreDetailList()">
        <!-- <template #head>
            <div class="header-toolbar">

            </div>
        </template> -->
        <template #content>
            <div style="display: flex; flex-wrap: nowrap;">
                <el-form inline v-model="searchForm" ref="formRef" style="display: flex; flex-wrap: nowrap;">
                    <el-form-item label="商铺类型" prop="typeCode">
                        <el-select v-model="searchForm.typeCode">
                            <el-option v-for="item in storeTypeOptions" :key="item.storeTypeCode"
                                :label="item.storeTypeName" :value="item.storeTypeCode" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="所属线路" prop="udLineCode">
                        <el-select v-model="searchForm.udLineCode">
                            <el-option v-for="item in lineDescOptions" :key="item.LineCode" :label="item.LineName"
                                :value="item.LineCode" />
                        </el-select>
                    </el-form-item>
                </el-form>
                <div class="button-group">
                    <ecp-button type="primary" text="查询" @click="SearchEvent" :loading="loading.table" />
                    <ecp-button text="重置" @click="ResetEvent" :loading="loading.table" />
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading.export" @click="ExportEvent" />
                </div>
            </div>
            <!-- <div style="height: 100%; width: 100%;"> -->
            <app-dynamic-table :loading="loading.table" :table-data="tableData.list" :table-config="STORE_DETAILS"
                style="height: calc(100vh - 310px);" />
            <!-- </div> -->
        </template>
    </ecp-layout-pagination>
</template>

<style scoped lang="scss">
/* 添加样式以使按钮组居右 */
.button-group {
    display: flex;
    flex-grow: 1;
    justify-content: flex-end;
}
</style>
