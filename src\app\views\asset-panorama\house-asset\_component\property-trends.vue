<script setup>
import { CaretBottom, CaretTop } from '@element-plus/icons-vue';
import { echarts } from 'ecp-chart';
import * as Api from '@api/index';

const loading = ref(false);
const total = ref(0);
const chainRatio = reactive({
    weeklyChainRatio: true,
    monthlyChainRatio: false
});
const chainRatioValue = reactive({
    MoM: 0,
    YoY: 0
});

const option = {
    color: ['#0367FC'],
    legend: {
        show: false
    },
    grid: {
        top: '25px'
    },
    xAxis: {
        axisLabel: {
            textStyle: {
                color: 'rgba(0, 0, 0, 0.45)',
                fontFamily: 'D-DIN'
            }
        }
    },
    yAxis: {
        nameTextStyle: {
            padding: [0, -40, 0, 0]
        }
    },
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    series: [
        {
            type: 'line',
            itemStyle: {
                borderColor: '#fff',
                borderWidth: 2
            },
            lineStyle: {
                width: 4
            },
            smooth: 0.5,
            symbolSize: 8,
            showSymbol: false,
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(3,103,252,0.2)' },
                    { offset: 1, color: 'rgba(3,103,252,0)' }
                ])
            }
        }
    ]
};

const data = reactive({
    dimensions: ['日期', '面积'],
    source: []
});

const RealEstateTrends = async () => {
    try {
        loading.value = true;
        const response = await Api.AssetPanorama.RealEstateTrends();
        const { MoM, YoY, EstateTendingVos } = response.Data;
        chainRatio.weeklyChainRatio = MoM > 0;
        chainRatio.monthlyChainRatio = YoY > 0;
        chainRatioValue.YoY = Math.abs(YoY);
        chainRatioValue.MoM = Math.abs(MoM);
        EstateTendingVos.reverse();
        EstateTendingVos.forEach(item => {
            data.source.push({
                日期: item.Date,
                面积: item.TotalUdBuArea
            });
            total.value += item.TotalUdBuArea;
        });
    } catch (error) {
        console.log('%c RealEstateTrends Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    } finally {
        loading.value = false;
    }
};

onMounted(RealEstateTrends);
</script>

<template>
    <app-business-panel mode="card" title="房产趋势">
        <template #content>
            <div class="property-trends" v-loading="loading">
                <div class="counter font-number-bold">{{ total.toLocaleString() }}</div>
                <div class="annulus-data">
                    <div class="week-on-week">
                        <span>月同比</span>
                        <span class="chain-ratio font-number"
                            :class="chainRatio.weeklyChainRatio ? 'increase' : 'decrease'">
                            <el-icon>
                                <CaretTop v-if="chainRatio.weeklyChainRatio" />
                                <CaretBottom v-else />
                            </el-icon>
                            {{ chainRatioValue.YoY }}%
                        </span>
                    </div>
                    <div class="month-on-month">
                        <span>月环比</span>
                        <span class="chain-ratio font-number"
                            :class="chainRatio.monthlyChainRatio ? 'increase' : 'decrease'">
                            <el-icon>
                                <CaretTop v-if="chainRatio.monthlyChainRatio" />
                                <CaretBottom v-else />
                            </el-icon>
                            {{ chainRatioValue.MoM }}%
                        </span>
                    </div>
                </div>
                <ecp-chart-base-line theme="whiteTheme" class="line-chart" :data="data" :option="option" yName="单位：万平方"
                    linear area smooth :lineWidth="4" />
            </div>
        </template>
    </app-business-panel>
</template>

<style scoped lang="scss">
.property-trends {

    .counter {
        margin-top: 12px;
        font-size: 30px;
        line-height: 30px;
        letter-spacing: 0.8px;
        color: var(--elp-text-color-primary);
    }

    .annulus-data {
        margin: 12px 0;
        display: flex;
        gap: 17px;

        .week-on-week,
        .month-on-month {
            span {
                font-size: 12px;
                line-height: 20px;
                color: var(--elp-text-color-primary);
            }

            .chain-ratio {
                margin-left: 6px;

                .elp-icon {
                    margin-right: 5px;
                }

                &.increase {
                    color: #E5484D;

                    i {
                        transform: translateY(1.5px);
                    }
                }

                &.decrease {
                    color: #30A46C;
                }
            }
        }
    }

    .line-chart {
        flex: 1;
    }
}
</style>
