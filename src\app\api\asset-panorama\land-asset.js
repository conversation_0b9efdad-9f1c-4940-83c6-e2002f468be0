import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

export const LandAssetApi = {
    /**
     * 土地
     * @method getLandAreaTrends 土地面积趋势
     */
    getLandAreaTrends () {
        return request.get(`${AssetsAnalysisService}/LandAsset/LandAreaTrends`).then(
            ({ data }) => data?.Data
        );
    },
    /**
     * 土地
     * @method getLandProportion 土地大类比例
     */
    getLandProportion () {
        return request.get(`${AssetsAnalysisService}/LandAsset/LandProportion`).then(
            ({ data }) => data?.Data
        );
    },
    /**
     * 土地
     * @method getQualityProportion 按性质占比分析
     */
    getQualityProportion () {
        return request.get(`${AssetsAnalysisService}/LandAsset/QualityProportion`).then(
            ({ data }) => data?.Data
        );
    },
    /**
     * 土地
     * @method getReserveAreaToBeSold 储备土地资源面积-待出让面积
     */
    getReserveAreaToBeSold () {
        return request.get(`${AssetsAnalysisService}/LandAsset/ReserveAreaToBeSold`).then(
            ({ data }) => data?.Data
        );
    },
    /**
     * 土地
     * @method getReserveAreaTransferred 储备土地资源面积-已出让面积
     */
    getReserveAreaTransferred () {
        return request.get(`${AssetsAnalysisService}/LandAsset/ReserveAreaTransferred`).then(
            ({ data }) => data?.Data
        );
    },
    /**
     * 土地
     * @method getSporadicArea 零星用地资源面积
     */
    getSporadicArea () {
        return request.get(`${AssetsAnalysisService}/LandAsset/SporadicArea`).then(
            ({ data }) => data?.Data
        );
    },
    /**
     * 土地
     * @method getStockLand 存量土地分析
     */
    getStockLand () {
        return request.get(`${AssetsAnalysisService}/LandAsset/StockLand`).then(
            ({ data }) => data?.Data
        );
    }

};
