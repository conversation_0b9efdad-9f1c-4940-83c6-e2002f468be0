// 全局过渡动画

/* fade */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--custom-transition-duration, 200ms);
}

.fade-enter,
.fade-enter-from,
.fade-leave-active {
  opacity: 0;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all var(--custom-transition-duration, 200ms);
}
// .fade-transform-leave-active {
//   position: absolute;
// }

.fade-transform-enter,
.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* fade-transform-vertical */
.fade-transform-vertical {
  transition: all var(--custom-transition-duration, 200ms);
}

.fade-transform-vertical-enter,
.fade-transform-vertical-enter-from,
.fade-transform-vertical-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

.fade-transform-vertical-leave-active {
  position: absolute;
}

/* fade-stage */
.fade-stage-leave-active,
.fade-stage-enter-active {
  transition: all var(--custom-transition-duration, 200ms);
}

.fade-stage-enter,
.fade-stage-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-stage-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* breadcrumb transition */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all var(--custom-transition-duration, 200ms);
}

.breadcrumb-enter,
.breadcrumb-enter-from,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all var(--custom-transition-duration, 200ms);
}

.breadcrumb-leave-active {
  position: absolute;
}
