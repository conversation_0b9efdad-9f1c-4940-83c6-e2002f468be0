<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <!-- <template #head-right>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template> -->
        <!-- <template #content> -->
        <el-card class="card">
            <ecp-layout-pagination content-scroll :total="total" v-model:current-page="pagination.pageNum"
                v-model:page-size="pagination.pageSize" layout="prev, pager, next, sizes, jumper"
                @current-change="SearchEvent" @size-change="SearchSize">
                <template #head>

                </template>
                <template #content>
                    <div class="header-toolbar">
                        <el-form inline v-model="searchForm" ref="formRef" style="display: flex;flex-wrap: nowrap;">
                            <el-form-item label="所属线路" prop="LineDesc">
                                <el-select v-model="searchForm.line" clearable>
                                    <el-option v-for="(item, index) in lineList" :key="index" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="不动产权证号" prop="RealEstateCertificateNumber">
                                <el-input v-model="searchForm.realEstateCertificateNumber" placeholder="请输入不动产权证号" />
                            </el-form-item>
                        </el-form>
                        <div class="button_three">
                            <ecp-button type="primary" text="查询" @click="fetchData" />
                            <ecp-button text="重置" @click="ResetEvent" />
                            <ecp-button icon="ecp-icon-export" text="导出" :loading="loading.export"
                                @click="ExportEvent" />
                        </div>
                    </div>
                    <app-dynamic-table :loading="loading.table" :table-data="tableData"
                        :table-config="CERTIFICATE_PROCESSING_TABLE" style="height: calc(100vh - 220px);" />
                </template>
            </ecp-layout-pagination>
        </el-card>
        <!-- </template> -->
    </app-form-page>
</template>

<script setup>
import dayjs from 'dayjs';
import { CERTIFICATE_PROCESSING_TABLE } from '../_constants/index';
import * as Api from '@api/index';
import { ElMessage } from 'element-plus';
import { downloadBlobData } from '@utils/download';
const name = 'asset-details-certificate-processing';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '权证办理'
        }
    ];
});

onMounted(() => {
    fetchData();
});
const fetchData = async () => {
    loading.table = true;
    try {
        const { Data, Total } = await Api.AssetDetails.getCertificateOfRights({
            ...searchForm,
            ...pagination
        });
        total.value = Total;
        tableData.value = Data;
        // 所属路线下拉框
        const lineData = await Api.AssetDetails.getLineDesc1();
        // console.log(lineData.Data);
        // lineList.splice(0, lineList.length, ...Object.entries(lineData.Data).map(([key, value]) => ({
        //     value: key,
        //     label: value
        // })));
        // console.log();
        const entries = Object.entries(lineData.Data);
        const endItem = entries.find(([key]) => key === '00');
        const sortedEntries = entries
            .filter(([key]) => key !== '00')
            .sort(([a], [b]) => a.localeCompare(b, undefined, { numeric: true }));
        const finalEntries = endItem ? [...sortedEntries, endItem] : sortedEntries;
        lineList.splice(0, lineList.length, ...finalEntries.map(([key, value]) => ({
            value: key,
            label: value
        })));
    } catch (err) {
        console.log(err, 'err');
    }
    loading.table = false;
};

const total = ref(0);
const formRef = ref(null);
const loading = reactive({
    table: false,
    export: false
});
const searchForm = reactive({
    line: '', // 线路名称
    lineCode: '', // 所属路线
    queryTime: '', // 查询日期
    realEstateCertificateNumber: '' // 不动产权证号
});

const lineList = reactive([]);

const pagination = reactive({
    pageSize: 10,
    pageNum: 1
});
const tableData = ref([]);

// 分页
const SearchEvent = async (newPageNum) => {
    pagination.pageNum = newPageNum;
    fetchData();
};

// const SearchSize = async () => {
//     pagination.pageNum = 1;
//     fetchData();
// }
watch(() => pagination.pageSize, () => {
    fetchData();
});

const ResetEvent = async () => {
    searchForm.lineCode = '';
    searchForm.realEstateCertificateNumber = '';
    fetchData();
};

const ExportEvent = async () => {
    try {
        if (total.value > 0) {
            const response = await Api.AssetDetails.exportCertificateOfRightsTable({ ...searchForm, ...pagination });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (error) {
        console.log('error', error);
        ElMessage.error('导出失败');
    }
};
</script>

<style lang="scss" scoped>
$page-name: asset-details-certificate-processing;

.#{$page-name} {
    :deep(.elp-select) {
        width: 120px;
    }

    .card {
        flex: 1 1 auto;
        display: flex;
        overflow: auto;
        height: 1vh;

        :deep(.elp-card__body) {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            padding-top: 0;
            width: 100%;
        }

        .header-toolbar {
            display: flex;

            .button_three {
                display: flex;
                flex-grow: 1;
                justify-content: flex-end;
            }
        }

    }
}
</style>
