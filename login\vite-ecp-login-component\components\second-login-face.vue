<template>
    <el-dialog v-model="dialogVisible" :close-on-click-modal="false" :show-close="false" append-to-body
        class="ecpp-login-component dialog-second-login-face"
        modal-class="ecpp-login-component dialog-second-login-face-modal" @open="init" @close="handleClose">
        <template #title>
            <el-icon class="back-icon" @click="handleClose">
                <Back />
            </el-icon>
            <div class="title-text">人脸认证</div>
            <div class="title-decoration"></div>
        </template>
        <div class="second-login-face">
            <div class="picture-area" v-loading="loading">
                <div class="picture-area-circle">
                    <div class="picture-area-canvas">
                        <video id="video" ref="videoRef" v-show="!hasPicture" width="100%" height="100%"></video>
                        <canvas id="canvas" ref="canvasRef" v-show="hasPicture"></canvas>
                    </div>
                </div>
            </div>
            <div class="button-group">
                <el-button class="capture-btn" :class="{ 'full-width': !hasPicture }" :disabled="!!cannotGetUserMedia"
                    @click="captureImage">{{ cannotGetUserMedia ? '不支持访问用户媒体' : (hasPicture ? '重新拍照' : '拍照')
                    }}</el-button>
                <el-button class="confirm-btn" @click="submitImage" v-show="hasPicture">认证</el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script setup>
import { ref, computed, defineComponent, defineProps, provide, onMounted, getCurrentInstance, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { Back } from '@element-plus/icons-vue';

import { LoginApi } from '../api';
import * as Utils from '../utils';

defineComponent({
    name: 'ecp-second-login-face'
});

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    userCode: {
        type: String,
        default: ''
    },
    service: {
        type: String,
        default: ''
    },
    isComplexNetwork: Boolean
});

const emits = defineEmits(['update:visible']);

const loading = ref(false);
const hasPicture = ref(false);
const cannotGetUserMedia = ref(false);

const canvasRef = ref();
const videoRef = ref();

const dialogVisible = computed({
    get () {
        return props.visible;
    },
    set (val) {
        emits('update:visible', val);
    }
});
const originUrl = computed(() => {
    return Utils.getOriginUrl(props.isComplexNetwork);
});

const setCanvasSize = async () => {
    await nextTick();
    const width = document.body.clientWidth;
    const canvas = canvasRef.value;
    if (width <= 1280) {
        canvas.width = 139;
        canvas.height = 139;
    } else if (width <= 1440) {
        canvas.width = 182;
        canvas.height = 182;
    } else {
        canvas.width = 311;
        canvas.height = 311;
    }
};
const handleClose = () => {
    cannotGetUserMedia.value = false;
    hasPicture.value = false;
    dialogVisible.value = false;
    window.onresize = null;
};
const captureImage = () => {
    if (cannotGetUserMedia.value) {
        return;
    }
    if (!hasPicture.value) {
        const canvas = canvasRef.value;
        const video = videoRef.value;
        const context = canvas.getContext('2d');
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        hasPicture.value = true;
    } else {
        hasPicture.value = false;
    }
};
const submitImage = async () => {
    const canvas = canvasRef.value;
    const imageDataUrl = canvas.toDataURL('image/jpeg');
    const isNativeClient = window?._env?.isNativeClient; // 是否托盘客户端
    try {
        const result = await LoginApi.faceLogin(
            {
                ImageBase64: imageDataUrl,
                UserCode: props.userCode,
                Service: props.service
            },
            {
                headers: {
                    x_op_client: isNativeClient ? 2 : 1
                }
            }
        );
        if (result.OpCode === 0 && result.Data) {
            ElMessage.success('认证成功！');
            const redirectUrl = Utils.getRedirectUrl(
                result.Data.redirectUrl || originUrl.value,
                props.isComplexNetwork
            );
            window.location.href = redirectUrl;
            window.localStorage &&
                window.localStorage.setItem('loginStatus', 'logined');
        } else {
            ElMessage.error(result.OpDesc || '认证失败！');
        }
    } catch (error) {
        console.log(
            '%c [ECP-LOGIN] submitImage Caught error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    }
};
const getUserMedia = (constraints) => {
    loading.value = true;
    function success (stream) {
        const video = videoRef.value;
        // 兼容webkit核心浏览器
        // let CompatibleURL = window.URL || window.webkitURL;
        // 将视频流设置为video元素的源
        console.log(stream);

        // video.src = CompatibleURL.createObjectURL(stream);
        video.srcObject = stream;
        video.play();
        loading.value = false;
    }

    function error (error) {
        ElMessage.error(
            `访问用户媒体设备失败${error.name}, ${error.message}`
        );
        cannotGetUserMedia.value = true;
        loading.value = false;
    }

    try {
        if (navigator.mediaDevices.getUserMedia) {
            // 最新的标准API
            navigator.mediaDevices
                .getUserMedia(constraints)
                .then(success)
                .catch(error);
        } else if (navigator.webkitGetUserMedia) {
            // webkit核心浏览器
            navigator.webkitGetUserMedia(constraints, success, error);
        } else if (navigator.mozGetUserMedia) {
            // firfox浏览器
            navigator.mozGetUserMedia(constraints, success, error);
        } else if (navigator.getUserMedia) {
            // 旧版API
            navigator.getUserMedia(constraints, success, error);
        }
    } catch (error) {
        console.log(
            '%c [ECP-LOGIN] getUserMedia Caught error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
        loading.value = false;
    }
};

const init = () => {
    setCanvasSize();
    window.onresize = () => {
        // console.log('onresize');
        setCanvasSize();
    };
    nextTick(() => {
        const video = videoRef.value;
        if (
            (navigator.mediaDevices &&
                navigator.mediaDevices.getUserMedia) ||
            navigator.getUserMedia ||
            navigator.webkitGetUserMedia ||
            navigator.mozGetUserMedia
        ) {
            // 调用用户媒体设备, 访问摄像头
            getUserMedia({
                video: {
                    width: video.offsetWidth,
                    height: video.offsetHeight
                }
            });
        } else {
            cannotGetUserMedia.value = true;
            ElMessage.error('不支持访问用户媒体');
        }
    });
};
</script>
