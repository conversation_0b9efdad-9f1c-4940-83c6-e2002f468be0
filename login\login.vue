<template>
    <el-config-provider v-bind="ElementDefaultConfig">
        <div class="login-page" id="login">
            <img class="login-page-logo" src="/login/login-logo.png" alt="" />
            <div class="login-page-form">
                <div class="login-page-form-header">
                    <div class="login-page-form-header-text">{{ title }}</div>
                </div>
                <ecp-login-form class="login-page-form-content" is-complex-network :config="config"></ecp-login-form>
                <div class="login-page-form-footer">
                    <div class="login-page-form-footer-text"> 请使用谷歌79及以上版本浏览器，如版本不满足请前往下载</div>
                </div>
            </div>
        </div>
    </el-config-provider>
</template>

<script setup>
import useGlobalStore from '@store/global-config';
import { ElementDefaultConfig } from '@ecp/ecp-ui-plus';
import { EcpLoginComponent, EcpLoginForm, LoginUtils } from './vite-ecp-login-component/main.js';

top.LoginUtils = LoginUtils;

const globalStore = useGlobalStore();

const title = computed(
    () => globalStore.globalConfigs?.IMPORT_CONFIGS?.title || '资产分析决策引擎'
);
const config = {
    formSize: 'medium'
};

onMounted(() => {
    document.title = title.value;
});
</script>

<style lang="scss">
html,
body,
#login {
    width: 100%;
    height: 100%;
    display: flex;
}

.login-page {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-image: url('/login/login-bg.png');
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    overflow: hidden;

    &-logo {
        position: absolute;
        top: 48px;
        left: 64px;
        width: 204px;
        height: 48px;
        object-fit: contain;
        z-index: 0;
    }

    &-form {
        width: 448px;
        min-height: 470px;
        padding: 56px 64px;
        display: flex;
        flex-direction: column;
        background: #FFFFFF;
        border-radius: 6px;
        backdrop-filter: blur(16.32px);
        box-shadow: 0px 10px 40px 0px rgba(1, 26, 118, 0.09);
        position: absolute;
        top: 50%;
        right: 12.5%;
        transform: translateY(-50%);
        overflow: hidden;

        &-header {
            position: relative;

            &-text {
                font-size: 24px;
                line-height: 36px;
                font-weight: 500;
                color: var(--color-text-light);
                display: flex;
                justify-content: center;
                flex-wrap: nowrap;
                white-space: pre-wrap;
                word-break: break-all;
            }

            &::after {
                content: '';
                position: absolute;
                bottom: -10px;
                left: 50%;
                transform: translateX(-50%);
                height: 4px;
                width: 40px;
                border-radius: 2px;
                background: var(--color-primary);
            }
        }

        &-content {
            --ecpp-login-background-base: transparent;

            --ecpp-login-form-width: 100%;
            --ecpp-login-form-padding-top: 58px;
            --ecpp-login-form-padding-left: 0;
            --ecpp-login-form-padding-right: 0;

            --ecpp-login-form-pki-button-height: 40px;
        }

        &-footer {
            position: absolute;
            bottom: 16px;
            font-size: 12px;
            line-height: 20px;
            text-align: center;
            color: rgba(0, 0, 0, 25%);
        }
    }
}
</style>
