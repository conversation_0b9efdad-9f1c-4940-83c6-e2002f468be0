<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <template #head-right>
            <!-- <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span> -->
        </template>
        <template #content>
            <el-card class="container">

                <!-- <el-radio-group v-model="current">
                    <el-radio-button label="线路运营资产明细" value="tab1" />
                    <el-radio-button label="办公资产明细" value="tab2" />
                </el-radio-group> -->
                <div class="tab-container">
                    <el-tabs class="app-detail-tabs tabs-only" v-model="current" style="margin-bottom: 20px;">
                        <el-tab-pane label="线路运营资产明细" name="tab1"></el-tab-pane>
                        <el-tab-pane label="办公资产明细" name="tab2"></el-tab-pane>
                    </el-tabs>
                </div>
                <div class="content-area">
                    <transition name="fade-transform" mode="out-in">
                        <component :is="_components"></component>
                    </transition>
                </div>
            </el-card>
        </template>
    </app-form-page>
</template>

<script setup>
import dayjs from 'dayjs';
import LineOperatingAssetDetails from './components/line-operating-asset-details.vue';
import OfficeAssetDetails from './components/office-asset-details.vue';
import * as Api from '@api/index';
const name = 'asset-details-operations-office-assets';
defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '运营与办公资产'
        }
    ];
});

const current = ref('tab1');
const _components = computed(() => {
    return {
        tab1: LineOperatingAssetDetails,
        tab2: OfficeAssetDetails
    }[current.value];
});

</script>

<style lang="scss" scoped>
$page-name: asset-details-operations-office-assets;

.#{$page-name} {
    :deep(.elp-select) {
        width: 120px;
    }

    .container {
        flex: 1 1 auto;
        display: flex;
        overflow: auto;
        height: 10vh;

        :deep {

            .elp-tabs__nav-prev,
            .elp-tabs__nav-next {
                height: 100%;
                display: flex;

                i {
                    margin: auto;
                }
            }
        }

        :deep(.elp-card__body) {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            padding-top: 0;
            width: 100%;
        }

        .content-area {
            flex: 1 1 auto;
            overflow: hidden;

            :deep {
                .header-toolbar {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;

                    &__selector {
                        display: flex;
                        gap: 8px;
                    }
                }
            }
        }
    }
}
</style>
