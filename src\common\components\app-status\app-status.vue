<!--
<docs>
# 缺省状态

## props
- status String 状态，可选值 empty, success, 默认 success
- imageUrl String 自定义图片
- title String  主文字
- subtitle String 副文字

## slots
- img 图片区域插槽，参数如下
    - currImage 当前自定义图片或状态图片
- text 文字区域插槽
- actions 操作区域插槽

</docs>
-->

<template>
    <ecp-empty class="app-status" :class="[status]" :empty-title="title" :empty-text="subtitle" :size="ultimateSize">
        <template #icon v-if="$slots.img || imageUrl || statusMap[status]">
            <slot name="img" v-if="$slots.img"></slot>
            <div class="app-status-wrapper img-wrapper" :class="[status]" v-else-if="imageUrl || statusMap[status]">
                <img class="app-status-img" :class="{ 'default-image': !imageUrl }" :src="imageUrl || statusMap[status]" />
            </div>
        </template>
        <template #title>
            <slot name="title" v-if="$slots.title"></slot>
            <div class="app-status-text" v-else-if="title">{{ title }}</div>
        </template>
        <template #text>
            <slot name="subtitle" v-if="$slots.subtitle"></slot>
            <div class="app-status-text subtitle" v-else-if="subtitle">{{ subtitle }}</div>
        </template>
        <template #footer v-if="$slots.actions">
            <slot name="actions"></slot>
        </template>
    </ecp-empty>
</template>

<script>
// import DEFAULT_EMPTY_IMAGE from '@assets/img_status_simple_empty.png';
import DEFAULT_SUCCESS_IMAGE from '@assets/img_status_simple_success.png';
import DEFAULT_FAIL_IMAGE from '@assets/img_status_simple_fail.png';
import DEFAULT_NO_IMAGE_IMAGE from '@assets/img_status_no_image.png';
export default {
    name: 'app-status',
    components: {},
    props: {
        status: {
            type: String,
            default: 'empty'
        },
        imageUrl: {
            type: String,
            default: ''
        },
        title: {
            type: String,
            default: ''
        },
        size: {
            type: String,
            default: 'small'
        },
        subtitle: {
            type: String,
            default: ''
        }
    },
    data () {
        return {
            statusMap: {
                // empty: DEFAULT_EMPTY_IMAGE,
                success: DEFAULT_SUCCESS_IMAGE,
                fail: DEFAULT_FAIL_IMAGE,
                failed: DEFAULT_FAIL_IMAGE,
                error: DEFAULT_FAIL_IMAGE,
                danger: DEFAULT_FAIL_IMAGE,
                'no-image': DEFAULT_NO_IMAGE_IMAGE
            }
        };
    },
    computed: {
        ultimateSize () {
            const sizeMap = {
                small: '112px',
                medium: '168px',
                large: '224px'
            };
            const noImageSizeMap = {
                small: '64px',
                medium: '80px',
                large: '120px'
            };
            return (this.status === 'no-image' ? noImageSizeMap[this.size] : sizeMap[this.size]) || this.size;
        }
    },
    methods: {}
};
</script>

<style scoped lang="scss">
.app-status {
    width: auto;
    height: auto;
    padding: 16px;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    &.center {
        justify-content: center;
    }

    .app-status-text {
        font-size: var(--font-size-medium);
        color: var(--color-text-primary);
        line-height: var(--font-line-height-medium);
        text-align: center;

        &.subtitle {
            font-size: var(--font-size-base);
            color: var(--color-text-regular);
            line-height: var(--font-line-height-primary);
        }

        & + .app-status-text {
            margin: 8px 0;
        }
    }

    .app-status-img {
        flex: 0 0 auto;
        // width: 100%;
        width: var(--ecpp-empty-icon-size, 100%);

        &.default-image {
            pointer-events: none;
            user-select: none;
        }
    }

    .app-status-wrapper {
        flex: 0 0 auto;

        &.img-wrapper {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
        }

        &.text-wrapper {}

        & + .actions-wrapper {
            margin-top: 24px;
        }
    }

    &.no-image {

        > .img-wrapper + .app-status-text {
            margin-top: var(--spacer-large);
            color: var(--color-text-placeholder);
        }
    }
}
</style>
