<template>
    <div class="chart-component">
        <ecp-chart-bar-line v-if="chartData?.source?.length" :theme="theme" :unit="chartData.unit"
            :lineDimensions="props.chartData.lineDimensions" yAxisTick :area="false" :data="chartData"
            :option="chartData.option" :grid="chartData.option.grid" />
        <ecp-empty v-else />
    </div>
</template>

<script setup>
import { reactive, watch, computed } from 'vue';

const props = defineProps({
    chartData: {
        type: Object,
        default: () => ({
            dimensions: [],
            source: []
        })
    },
    unit: {
        type: String,
        required: true
    }
});

const theme = ref('whiteTheme');

const _color = ['#0367FC', '#11b062', '#487690'];
const chartData = reactive({
    dimensions: props.chartData.dimensions,
    source: props.chartData.source,
    color: _color,
    unit: ['个', '%'], // 默认单位
    option: {
        tooltip: {
            trigger: 'axis',
            extraCssText:
                    'padding: 0; border-radius: 0.4em; border: 0; overflow: hidden;',
            backgroundColor: '#fff',
            formatter: (params) => {
                let tooltipHtml = '<div class="ecp-chart-tooltip-wrapper is-white">';
                tooltipHtml += `<div class="ecp-chart-tooltip-head">${params[0].axisValue}</div>`;

                params.forEach((item) => {
                    const color = item.color;
                    let unit = '';
                    if (item.seriesName.includes('数量')) {
                        unit = '个';
                    } else if (item.seriesName.includes('面积')) {
                        unit = '平方米';
                    } else if (item.seriesName.includes('出租率')) {
                        unit = '%';
                    }

                    tooltipHtml += `
                <div class="ecp-chart-tooltip-item">
                    <span class="ecp-chart-tooltip-label" style="--color: ${color}">${item.seriesName}</span>
                         <span class="ecp-chart-tooltip-value">
                            <i class="ecp-chart-tooltip-value-num">${item.value}</i>
                            <i class="ecp-chart-tooltip-value-unit">${unit}</i>
                        </span>
                </div>
            `;
                });
                tooltipHtml += '</div>';
                return tooltipHtml;
            }
        },

        color: _color,
        grid: {
            top: 40
        },

        legend: {
            top: 10,
            data: props.chartData.dimensions.slice(1) // 动态获取 legend 数据
        },
        xAxis: {
            type: 'category',
            data: props.chartData.source.map(item => item[props.chartData.dimensions[0]]), // 动态获取 X 轴数据
            axisLabel: {
                rotate: 60,
                color: 'rgba(29, 33, 55, 0.5)'
            }
        },
        yAxis: [
            {
                name: `单位：${props.unit}`,
                nameTextStyle: {
                    padding: [0, -36, 0, 0]
                },
                axisLabel: {
                    color: 'rgba(29, 33, 55, 0.5)'
                }
            },
            {
                name: '出租率',
                nameTextStyle: {
                    padding: [0, -20, 0, 0]
                },
                axisLabel: {
                    formatter: '{value}%',
                    color: 'rgba(29, 33, 55, 0.5)'
                }
            }
        ],
        series: props.chartData.dimensions.slice(1).map((dimension, index) => ({
            name: dimension,
            type: index === props.chartData.dimensions.length - 2 ? 'line' : 'bar', // 最后一个维度使用折线图，其余用柱状图
            data: props.chartData.source.map(item => item[dimension]),
            yAxisIndex: index === props.chartData.dimensions.length - 2 ? 1 : 0, // 折线图使用右侧 Y 轴
            barWidth: index === props.chartData.dimensions.length - 2 ? undefined : '10', // 柱状图宽度
            itemStyle: {
                borderRadius: 20,
                color: _color[index % _color.length] // 动态分配颜色
            },
            lineStyle: index === props.chartData.dimensions.length - 2
                ? {
                    type: 'dashed'
                }
                : undefined,
            smooth: index === props.chartData.dimensions.length - 2 ? 0.5 : undefined, // 折线平滑
            symbolSize: 6 // 折线图点大小
        }))
    }
});

// 监听 chartData.source 和 dimensions 变化
watch(
    () => props.chartData,
    (newData) => {
        chartData.dimensions = newData.dimensions;
        chartData.source = newData.source;

        // 更新 legend 数据
        chartData.option.legend.data = newData.dimensions.slice(1);

        chartData.option.xAxis.data = newData.source.map(item => item[newData.dimensions[0]]);

        // 更新 series 数据
        chartData.option.series = newData.dimensions.slice(1).map((dimension, index) => ({
            name: dimension,
            type: index === newData.dimensions.length - 2 ? 'line' : 'bar',
            data: newData.source.map(item => item[dimension]),
            yAxisIndex: index === newData.dimensions.length - 2 ? 1 : 0,
            barWidth: index === newData.dimensions.length - 2 ? undefined : '10',
            itemStyle: {
                borderRadius: 20,
                color: _color[index % _color.length]
            },
            lineStyle: index === newData.dimensions.length - 2
                ? {
                    type: 'dashed'
                }
                : undefined,
            smooth: index === newData.dimensions.length - 2 ? 0.5 : undefined
        }));
    },
    { deep: true }
);

// 监听 unit 变化，更新 Y 轴单位和 tooltip 的单位
watch(
    () => props.unit,
    (newUnit) => {
        chartData.option.yAxis[0].name = `单位：${newUnit}`;
        chartData.unit[0] = newUnit; // 修改第一个单位，用于 tooltip 显示
    }
);
</script>

<style scoped>
.chart-component {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}
</style>
