import { generateUUID } from '@ecp/ecp-ui-plus';

export function download (template, filename = '未命名', fileType) {
    let blob;
    if (template instanceof Blob) {
        blob = template;
    } else {
        blob = new Blob([template], { type: fileType });
    }
    downloadByLink(URL.createObjectURL(blob), filename);
}

export function downloadByLink (url, filename = '') {
    let link = document.createElement('a');
    link.download = filename;
    link.href = url;
    link.click();
    link = null;
}
// 下载.xlsx表格（使用 H5 新特性 Blob 下载）
export function downloadBlobData (response) {
    const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' });
    const fileName = decodeURIComponent(response.headers['content-disposition'].split(';')[1].split('=')[1]).replace('utf-8\'\'', '');
    if ('download' in document.createElement('a')) { // 非IE下载
        const elink = document.createElement('a');
        elink.download = fileName;
        elink.style.display = 'none';
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        URL.revokeObjectURL(elink.href); // 释放URL 对象
        document.body.removeChild(elink);
    } else { // IE10+下载
        navigator.msSaveBlob(blob, fileName);
    }
};
/**
 * 以form表单形式下载文件
 * @param {String} url
 * @param {Object} params
 */
export function downloadByForm (url, params) {
    const id = generateUUID();
    const iframe = document.createElement('iframe');
    iframe.setAttribute('id', id);
    iframe.setAttribute('name', id);
    iframe.setAttribute('style', 'display:none;');

    const form = document.createElement('form');
    form.setAttribute('style', 'display:none;');
    form.setAttribute('target', id);
    form.setAttribute('method', 'post');
    form.setAttribute('action', url);

    for (const key in params) {
        const input = document.createElement('input');
        input.setAttribute('name', key);
        input.setAttribute('value', params[key]);
        form.appendChild(input);
    }

    const body = document.querySelector('body');
    iframe.appendChild(form);
    body.appendChild(iframe);
    form.submit();
}
