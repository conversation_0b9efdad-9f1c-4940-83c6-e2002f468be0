<template>
    <el-card class="app-card-tips" :class="{ free }" v-show="warningShow" shadow='never'
        :style="{ '--app-card-tips-width': width }">
        <div class='app-card-tips__content'>
            <ecp-icon class="app-card-tips-icon app-card-tips-icon-info" icon="#ecp-icon-circle-info" />
            <span v-if="content">{{ content }}</span>
            <span v-else>
                <slot></slot>
            </span>
            <ecp-icon v-if="showClose" class="app-card-tips-icon app-card-tips-icon-close" icon="#ecp-icon-close"
                @click="warningShow = !warningShow" />
        </div>
    </el-card>
</template>

<script setup>
defineComponent({
    name: 'app-card-tips'
});

const props = defineProps({
    icon: {
        type: String,
        default: '#ecp-icon-circle-info'
    },
    free: Boolean,
    // 是否可关闭
    showClose: {
        type: Boolean,
        default: false
    },
    content: {
        type: String,
        default: ''
    },
    width: {
        type: String,
        default: ''
    }
});

const warningShow = ref(true);

</script>

<style lang="scss" scoped>
.app-card-tips {
    width: var(--app-card-tips-width, 100%);
    color: var(--color-text-regular);
    padding: var(--spacer);

    &.free {
        padding: 0;
        border: none;
    }

    :deep(.elp-card__body) {
        padding: 0;
    }

    &-icon {
        width: var(--font-size-base);
        height: var(--font-size-base);
        color: var(--color-text-secondary);

        &-close {
            cursor: pointer;
            float: right;
            margin-top: 5px;
        }

        &-info {
            margin-right: var(--spacer);
            position: relative;
            top: var(--spacer-extra-small);
        }
    }
}
</style>
