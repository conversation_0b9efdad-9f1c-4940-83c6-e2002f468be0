import {
    AssetOverview,
    HouseAsset,
    LandAsset,
    PropertyAsset,
    AdvertisementAsset,
    OperatingAsset,
    OfficeAsset
} from './index';

export const AssetPanoramaRoutes = () => {
    const context = 'asset-panorama';
    return [
        {
            path: `/${context}/overview`,
            component: AssetOverview,
            label: '概览',
            name: `${context}-overview`
        },
        {
            path: `/${context}/house-asset`,
            component: HouseAsset,
            label: '房产',
            name: `${context}-house-asset`
        },
        {
            path: `/${context}/land-asset`,
            component: LandAsset,
            label: '土地',
            name: `${context}-land-asset`
        },
        {
            path: `/${context}/property-asset`,
            component: PropertyAsset,
            label: '物业',
            name: `${context}-property-asset`
        },
        {
            path: `/${context}/advertisement-asset`,
            component: AdvertisementAsset,
            label: '文体广告',
            name: `${context}-advertisement-asset`
        },
        {
            path: `/${context}/operating-asset`,
            component: OperatingAsset,
            label: '运营资产',
            name: `${context}-operating-asset`
        },
        {
            path: `/${context}/office-asset`,
            component: OfficeAsset,
            label: '办公资产',
            name: `${context}-office-asset`
        }
    ];
};
