<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <template #head-right>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template>
        <template #content>
            <div class="inventory-analysis-top">
                <app-business-panel title="盘盈盘亏趋势" mode="card">
                    <inventory-profit-loss-trend></inventory-profit-loss-trend>
                </app-business-panel>
                <app-business-panel title="盘盈盘亏资产分布" mode="card">
                    <template #header-append>
                        <el-select v-model="queryForm.groupType" placeholder="资产分类">
                            <el-option v-for="item in groupTypeArray" :key="item.Code" :label="item.Name" :value="item.Code" />
                        </el-select>
                    </template>
                    <inventory-profit-loss-asset-distribution :data="queryForm" />
                </app-business-panel>
            </div>
            <div class="inventory-analysis-bottom">
                <app-business-panel class="asset-inventory-details" title="资产盘点明细" mode="card">
                    <inventory-details></inventory-details>
                </app-business-panel>
            </div>
        </template>
    </app-form-page>
</template>

<script setup>
import InventoryDetails from '@views/asset-analysis/inventory-analysis/_component/inventory-details.vue';
import InventoryProfitLossTrend from '@views/asset-analysis/inventory-analysis/_component/inventory-profit-loss-trend.vue';
import InventoryProfitLossAssetDistribution from '@views/asset-analysis/inventory-analysis/_component/inventory-profit-loss-asset-distribution.vue';
import dayjs from 'dayjs';
import * as Api from '@api/index';

const name = 'asset-analysis-inventory-analysis';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '盘点分析'
        }
    ];
});

onUpdated(() => {
    getCheckDataType();
});

onMounted(() => {
    getCheckDataType();
});

const groupTypeArray = ref([]);
const queryForm = reactive({
    groupType: ''
});

const getCheckDataType = async () => {
    try {
        const response = await Api.AssetAnalysis.getCheckDataType();
        groupTypeArray.value = response.Data;
        if (groupTypeArray.value.length > 0) {
            queryForm.groupType = groupTypeArray.value[0].Code;
        }
    } catch (error) {
        console.log('%c getCheckDataType error', 'color: red', error);
    }
};

</script>

<style lang="scss" scoped>
$page-name: asset-analysis-inventory-analysis;

.#{$page-name} {
    :deep(.app-panel-header) {
        .date-picker {
            width: 144px;

            .elp-input__wrapper {
                flex-direction: row-reverse
            }
        }
    }

    :deep(.app-form-page-content) {
        gap: 16px;
    }

    .inventory-analysis-top {
        display: grid;
        gap: 16px;
        grid-template-columns: 1fr 1fr;
        height: 41vh;
        max-height: 448px;

        .elp-select {
            width: 96px;
        }
    }

    .inventory-analysis-bottom {
        flex: 1 1 auto;
        display: flex;
        max-height: 536px;

        .asset-inventory-details {
            display: flex;

            :deep(.app-business-panel-content) {
                flex: 1 1 auto;
                display: flex;
                max-height: 95%;

                .elp-table {
                    height: 100%;

                    .elp-table__body-wrapper {
                        overflow: auto;
                    }
                }
            }
        }
    }
}
</style>
