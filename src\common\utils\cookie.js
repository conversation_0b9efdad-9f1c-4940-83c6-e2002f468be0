import Cookies from 'js-cookie';

const inFifteenMinutes = new Date(new Date().getTime() + 12 * 60 * 60 * 1000); // cookie失效时间

const TokenKey = 'x_auth_token';

let locationContext = (window.location.pathname || '').split('/')[1] || '';
locationContext = locationContext ? `/${locationContext}` : '/';

export function getToken () {
    return Cookies.get(TokenKey);
}

export function setToken (token) {
    Cookies.set(TokenKey, token, { expires: inFifteenMinutes, path: '/' });
    return Cookies.set(TokenKey, token, { expires: inFifteenMinutes, path: locationContext });
}

export function removeToken () {
    Cookies.remove(TokenKey, { path: '/' });
    return Cookies.remove(Token<PERSON>ey, { path: locationContext });
}

export function saveLocalUserCode (userCode) {
    localStorage.setItem('userCode', userCode);
}

export function getLocalUserCode () {
    return localStorage.getItem('userCode');
}
