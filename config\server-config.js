// uni-server 服务配置项
const SERVER_CONFIG = {
    APP_IP: '',
    APP_PORT: 30768, // 端口
    APP_NAME: 'asset-manage-web', // 应用名
    APP_ALIAS: '', // 别名，网关访问路径
    PRODUCTION: false // 是否生产环境，生产环境会禁用 /config、/hosts、/sysHost 等接口，默认false
};

// nacos 配置项
const NACOS_CONFIG = {
    enabled: true, // 是否关联nacos
    serviceList: true, // 是否获取所有前端服务列表
    registerService: true, // 注册当前应用
    address: `${process.env.NACOS_SERVER || 'nacos-center.v-base:30848'}`, // 服务域名:端口
    namespace: 'a85a37ef-5bec-478c-a60f-0b11f10b3da4',
    items: [
        // 前端配置 (其它前端配置都放这里)
        {
            dataId: 'settings-frontend',
            group: 'prophet'
        },
        // 公共配置，系统应用相关配置放application里面 (代理ip、port等)
        {
            dataId: 'applications',
            group: 'prophet'
        }
    ],
    // 服务注册到nacos时，获取ip的规则，请在config.ini或者nacos上配置
    ipRules: {
        preferredNetworks: [], // 首选
        ignoredInterfaces: [] // 忽略
    }
};

// 门户需要使用的子模块，在前端服务订阅和子模块监控中，根据此列表获取
const APPS_ENTRY = [
    // 除了name，其余配置都会按 local代理配置、nacos代理配置、nacos服务订阅 的顺序更新
    /**
     * 如果ip、端口、需要修改，请在var.ini中配置变量，不要直接修改此文件，如：
     * metadata-frontend.ip=*************
     * metadata-frontend.port=30013
     */
    {
        name: SERVER_CONFIG.APP_NAME
    },
    // 公服前端
    {
        name: 'common-frontend'
    }
];

module.exports = {
    SERVER_CONFIG,
    NACOS_CONFIG,
    APPS_ENTRY
};
