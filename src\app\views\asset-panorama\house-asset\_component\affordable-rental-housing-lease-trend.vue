<script setup>
import * as Api from '@api/index';
import * as echarts from 'echarts';

const loading = ref(false);
const tooltipFrame = (html, title, theme = 'light') => {
    return `
        <div class="ecp-chart-tooltip-wrapper ${theme === 'light' ? 'is-white' : 'is-black'}">
             <div class="ecp-chart-tooltip-head">${title}</div>
             ${html}
        </div>
        `;
};
const tooltipItem = (color, name, value, unit) => {
    return `
        <div class="ecp-chart-tooltip-item">
            <span class="ecp-chart-tooltip-label" style="--color: ${color}">${name}</span>
            <span class="ecp-chart-tooltip-value">
                <i class="ecp-chart-tooltip-value-num">${value}</i>
                <i class="ecp-chart-tooltip-value-unit">${unit}</i>
            </span>
       </div>
   `;
};
const heatmapChartsRef = ref(null);
let heatmapCharts = null;
const months = [['3月', '6月', '9月', '12月'], ['2月', '5月', '8月', '11月'], ['1月', '4月', '7月', '10月']];
const _months = [['3月', '6月', '9月', '12月'], ['2月', '5月', '8月', '11月'], ['1月', '4月', '7月', '10月']];
const _data = reactive([[0, 0, 0], [0, 1, 0], [0, 2, 0], [0, 3, 0], [1, 0, 0], [1, 1, 0], [1, 2, 0], [1, 3, 0], [2, 0, 0], [2, 1, 0], [2, 2, 0], [2, 3, 0]]);
const option = reactive({
    tooltip: {
        textStyle: {
            fontSize: 10
        },
        extraCssText:
                    'padding: 0; border-radius: 0.4em; border: 0; overflow: hidden;',
        backgroundColor: '#fff',
        position: (point, params, dom, rect, size) => point[0],
        formatter: (params) => {
            const { color, value, name } = params;
            const [x, y, _value] = value;
            const month = _months[y][x];
            const html = tooltipItem(color, month.substring(0, 4) + '年' + month.substring(4, 6) + '月', _value, '%');
            return tooltipFrame(html, name);
        }
    },
    grid: {
        top: '10%',
        left: '65px',
        right: '0%',
        bottom: '20px'
    },
    xAxis: {
        type: 'category',
        data: ['第一季度', '第二季度', '第三季度', '第四季度'],
        axisTick: {
            length: 100,
            lineStyle: {
                color: 'rgba(29,33,55, 0.1)'
            }
        },
        axisLabel: {
            fontFamily: 'SourceHanSansSC-Regular, SourceHanSansSC',
            fontWeight: 400,
            color: 'rgba(0,0,0, 0.45)',
            overflow: 'break',
            fontSize: 10
        },
        splitLine: {
            show: true
        },
        axisLine: {
            show: false
        }
    },
    yAxis: {
        type: 'category',
        data: ['3/6/9/12月', '2/5/8/11月', '1/4/7/10月'],
        name: '单位：百分比',
        nameTextStyle: {
            color: 'rgba(0,0,0, 0.45)',
            padding: [0, 5, -10, -70],
            fontSize: 10,
            fontFamily: 'SourceHanSansSC-Regular, SourceHanSansSC',
            fontWeight: 400,
            align: 'right'
        },
        axisLabel: {
            fontFamily: 'D-DIN, D',
            fontWeight: 'normal',
            color: 'rgba(0,0,0, 0.45)',
            overflow: 'break'
        },
        axisTick: {
            length: 100,
            lineStyle: {
                color: 'rgba(29,33,55, 0.1)'
            }
        },
        splitLine: {
            show: true
        },
        axisLine: {
            show: false
        }
    },
    itemStyle: {
        borderWidth: '10px'
    },
    visualMap: {
        min: 0,
        max: 100,
        color: ['rgba(3,103,252, 1)', 'rgba(3,103,252, 0.1)'],
        show: false
    },
    series: [
        {
            name: '保障性租赁用房租赁趋势',
            type: 'heatmap',
            data: _data.map(item => [item[1], item[0], item[2] || 0]),
            label: {
                show: true,
                color: '#000000',
                fontSize: 16,
                rich: {
                    black: {
                        color: '#051526'
                    },
                    white: {
                        color: '#ffffff'
                    }
                },
                formatter: (params) => {
                    const [x, y, value] = params.data;
                    const month = months[y][x];
                    const text = `${month}：${value}`;
                    return value < 50 ? `{black|${text}}` : `{white|${text}}`;
                }
            },
            itemStyle: {
                borderWidth: 2,
                borderColor: '#fff',
                borderRadius: 5
            }
        }
    ]
});

const rate = ref(0);

const RentalTrends = async () => {
    try {
        loading.value = true;
        const response = await Api.AssetPanorama.RentalTrends();
        // 月份 - 数据位置映射
        const indexMap = { 1: 8, 2: 4, 3: 0, 4: 9, 5: 5, 6: 1, 7: 10, 8: 6, 9: 2, 10: 11, 11: 7, 12: 3 };
        let i = 0;
        const { Data } = response;
        for (; i < Data.length; i++) {
            const { RentalRate, Date } = Data[i];
            const month = Date.slice(-2);
            rate.value += RentalRate;
            const _index = indexMap[Number.parseInt(month)];
            const [x, y, value] = _data[_index];
            _months[x][y] = Date;
            _data[_index][2] = Math.round(RentalRate * 100);
        }
        rate.value = ((rate.value / i) * 100).toFixed(2);
        option.series[0].data = _data.map(item => [item[1], item[0], item[2] || 0]);
        renderCharts();
    } catch (error) {
        console.log('%c RentalTrends Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    } finally {
        loading.value = false;
    }
};

const renderCharts = () => {
    if (heatmapChartsRef.value && !heatmapCharts) {
        heatmapCharts = echarts.init(heatmapChartsRef.value);
    }
    heatmapCharts.setOption(option);
};

const handleResizeEvent = () => {
    heatmapCharts && heatmapCharts.resize();
};

onMounted(RentalTrends);
</script>

<template>
    <app-business-panel mode="card" title="保障性租赁用房租赁趋势">
        <template #content>
            <div class="affordable-rental-housing-lease-trend" v-loading="loading" v-resize="handleResizeEvent">
                <div class="trend-data">
                    <!-- <div class="trend-data-item">
                        <div class="trend-data-item__label">已租</div>
                        <div class="trend-data-item__value font-number-bold">{{ value.rented.toLocaleString() }}</div>
                    </div>
                    <div class="trend-data-item">
                        <div class="trend-data-item__label">待租</div>
                        <div class="trend-data-item__value font-number-bold">{{ value.unRented.toLocaleString() }}</div>
                    </div> -->
                    <div class="trend-data-item">
                        <!-- <div class="trend-data-item__label">出租率</div> -->
                        <div class="trend-data-item__value font-number-bold">{{ `${rate}%` }}</div>
                    </div>
                </div>
                <div class="heatmap-charts" ref="heatmapChartsRef"></div>
            </div>
        </template>
    </app-business-panel>
</template>

<style scoped lang="scss">
.affordable-rental-housing-lease-trend {
    .trend-data {
        display: flex;
        // justify-content: space-evenly;
        margin-top: 25px;

        &-item {

            &__label {
                margin-bottom: 5px;
                font-size: 12px;
                font-weight: normal;
                line-height: normal;
                color: var(--color-text-secondary);
            }

            &__value {
                font-size: 30px;
                line-height: 30px;
                letter-spacing: 0.8px;
                color: var(--elp-text-color-primary);
            }
        }
    }

    .heatmap-charts {
        flex: 1;
    }
}
</style>
