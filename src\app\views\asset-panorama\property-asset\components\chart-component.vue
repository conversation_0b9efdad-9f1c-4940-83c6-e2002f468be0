<template>
    <div class="chart-component">
        <ecp-chart-bar-line v-if="chartData?.source?.length" :theme="props.theme"
            :lineDimensions="props.chartData.lineDimensions" yAxisTick :unit="props.chartData.unit" :area="false"
            :data="props.chartData" class="demo-container" :option="props.options.option" />
        <ecp-empty v-else />
    </div>
</template>

<script setup>
import { ref, reactive, defineProps } from 'vue';

const props = defineProps({
    chartData: {
        type: Object,
        default: () => ({
            dimensions: [],
            source: []
            // yName: {}
        })
    },
    options: {
        type: Object,
        default: () => ({
            lineDimensions: [],
            yAxis: {},
            xAxis: [],
            series: []
        })
    },
    theme: {
        type: String,
        default: 'whiteTheme'
    }
});

</script>

<style lang="scss" scoped>
.chart-component {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 12px;
}

.demo-container {
    width: 100%;
    height: 100%;
}

.chart {
    width: 100%;
    height: 100%;
}
</style>
