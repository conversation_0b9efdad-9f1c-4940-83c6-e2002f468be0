@use "@ecp/ecp-ui-plus/theme-chalk/src/mixins/config.scss" as *;

// @font-face {
//     font-family: 'SourceHanSansSC';
//     src: url('/font/SourceHanSansSC.otf') format('truetype');
// }
// @font-face {
//     font-family: 'SourceHanSansSC-Regular';
//     src: url('/font/SourceHanSansSC-Regular.otf') format('truetype');
// }
@font-face {
    font-family: 'SourceHanSansSC-Medium';
    src: url('/font/SourceHanSansSC-Medium.otf') format('truetype');
}

@font-face {
    font-family: 'SourceHanSansSC-Bold';
    src: url('/font/SourceHanSansSC-Bold.otf') format('truetype');
}

@font-face {
    font-family: 'D-DIN-Bold';
    src: url('/font/D-DIN-Bold.ttf') format('truetype');
}

@font-face {
    font-family: 'D-DIN';
    src: url('/font/D-DIN.ttf') format('truetype');
}

@import "@ecp/ecp-ui-plus/theme-chalk/src/theme/default/reset-style.scss";

// 通用样式
body.asset-manage-web,
div.asset-manage-web {

    // @import "@ecp/ecp-ui-plus/theme-chalk/src/theme/default/reset-style.scss";

    /**
      * !!!!!!!! 警告 !!!!!!!!
      * !!!!!!!! 警告 !!!!!!!!
      * !!!!!!!! 警告 !!!!!!!!
      * ↓↓↓↓↓↓↓↓ 其它要写在这个文件里的样式必须放这里面 ↓↓↓↓↓↓↓↓
      */
    // .font-source {
    //     font-family: SourceHanSansSC;
    // }
    // .font-source-regular {
    //     font-family: SourceHanSansSC-Regular;
    // }
    .font-source-medium {
        font-family: SourceHanSansSC-Medium;
    }

    .font-source-bold {
        font-family: SourceHanSansSC-Bold;
    }

    .font-number-bold {
        font-family: D-DIN-Bold;
    }

    .font-number {
        font-family: D-DIN;
    }

    .app-business-panel.card-panel + .app-business-panel.card-panel {
        margin-top: unset;
    }

    .elp-tabs {
        &.elp-tabs--card {
            .elp-tabs__header .elp-tabs__item {
                background-color: var(--background-color-dialog);
            }
        }

        &.elp-tabs--top {
            &.tabs-only {
                & > .elp-tabs__header {
                    margin-bottom: 0;
                }

                &:not(.elp-tabs--card) {
                    // padding: 0 var(--spacer-large);

                    .elp-tabs__nav {
                        row-gap: var(--spacer-large-3);

                        &-scroll {
                            padding: 0 var(--spacer-large);
                        }
                    }

                    & > .elp-tabs__header {
                        margin-bottom: 0;

                        .elp-tabs__item,
                        .elp-tabs__item:nth-child(2),
                        .elp-tabs__item:last-child {
                            padding: 0 var(--spacer-large);
                        }
                    }

                    .elp-tabs__active-bar {
                        &::before {
                            content: '';
                            height: var(--spacer-extra-small);
                            width: calc(100% + var(--spacer-large-4));
                            background: var(--color-primary);
                            transform: translateX(-16px);
                            position: absolute;
                            top: 0;
                        }
                    }
                }
            }

            &.app-detail-tabs {
                --elp-tabs-header-height: 64px;

                .elp-tabs__nav-wrap::after {
                    display: block;
                    height: 1px;
                }
            }
        }
    }

    .app-business-panel-content {
        height: 100%;
    }

    .leaflet {
        &-container {
            background-color: transparent;
            overflow: hidden;
        }

        &-tooltip {
            background-color: var(--background-color-page);
            border-color: var(--background-color-page);
            color: var(--color-text-regular);
            filter: drop-shadow(0 0 2px var(--elp-border-color-lighter));
            backdrop-filter: blur(4px);

            &-top::before {
                border-top-color: var(--background-color-page);
            }

            &-bottom::before {
                border-bottom-color: var(--background-color-page);
            }

            &-left::before {
                border-left-color: var(--background-color-page);
            }

            &-right::before {
                border-right-color: var(--background-color-page);
            }
        }
    }

    .app-metro-map {

        &-line {
            filter: drop-shadow(1px 1px 0px var(--color-text-mark)) drop-shadow(-1px -1px 0px var(--color-text-mark)) drop-shadow(-1px 1px 0px var(--color-text-mark)) drop-shadow(1px -1px 0px var(--color-text-mark));
        }

        &-marker {
            display: flex;

            &.circle {
                width: 10px;
                height: 10px;
            }

            &.transfer {
                width: 20px;
                height: 20px;
            }
        }

        &-station {
            min-width: 100%;
            min-height: 100%;
            display: flex;
            flex-direction: column;
            gap: 2px;
            align-items: center;
            text-align: center;

            &.anchor {
                &-bottom {
                    flex-direction: column;
                    gap: 0;
                }

                &-top {
                    flex-direction: column-reverse;
                    gap: 0;
                    margin-top: calc(var(--font-line-height-small) * -1 + 2px);
                }

                &-left {
                    flex-direction: row-reverse;
                }

                &-right {
                    flex-direction: row;
                }

                &-rightbottom,
                &-bottomright {
                    flex-direction: row;

                    .app-metro-map-station-label {
                        align-self: flex-end;
                        transform: translateY(75%);
                    }
                }

                &-leftbottom,
                &-bottomleft {
                    flex-direction: row-reverse;

                    .app-metro-map-station-label {
                        align-self: flex-end;
                        transform: translateY(75%);
                    }
                }

                &-righttop,
                &-topright {
                    flex-direction: row;

                    .app-metro-map-station-label {
                        align-self: flex-start;
                        transform: translateY(-75%);
                    }
                }

                &-lefttop,
                &-topleft {
                    flex-direction: row-reverse;

                    .app-metro-map-station-label {
                        align-self: flex-start;
                        transform: translateY(-75%);
                    }
                }
            }

            &-icon {
                width: 6px;
                height: 6px;
                flex: 0 0 auto;
                border-radius: var(--border-radius-capsule);
                transform: scale(var(--station-marker-zoom, 1));

                &.circle {
                    background-color: var(--icon-color);
                }

                &.transfer {
                    width: 12px;
                    height: 12px;
                    background-image: url('@assets/map/transfer-station.png');
                    background-size: contain;
                    // margin-top: -4px;
                }
            }

            &-label {
                margin: 0;
                background-color: transparent;
                border: none;
                box-shadow: none;
                font-size: 8px;
                line-height: var(--font-line-height-extra-small);
                color: var(--color-text-primary);
                text-shadow: 1px 1px 0px var(--color-text-primary-reverse), 1px -1px 0px var(--color-text-primary-reverse), -1px -1px 0px var(--color-text-primary-reverse), -1px 1px 0px var(--color-text-primary-reverse);
                white-space: nowrap;
                zoom: var(--station-marker-zoom, 1);

                &::before {
                    display: none;
                }
            }


            &-green {
                --station-color: #82B436;
            }

            &-yellow {
                --station-color: #E79E42;
            }

            &-red {
                --station-color: #C52A29;
            }
        }

        &-tooltip {
            &__wrapper {
                display: flex;
                flex-direction: column;
                gap: var(--spacer-small);
            }

            &--item {
                display: flex;
                align-items: center;
                gap: var(--spacer);
            }

            &--index {
                min-width: 20px;
                margin-right: var(--spacer-small);
                display: inline-block;
                font-size: var(--font-size-small);
                line-height: 20px;
                color: var(--color-text-secondary);
                text-align: center;
                align-self: flex-start;
                background-color: var(--border-color-lighter);
                border-radius: var(--border-radius-capsule);
            }

            &--text {
                font-size: var(--font-size-base);
                line-height: var(--font-line-height-primary);
                color: var(--color-text-regular);

                &.title {
                    color: var(--color-text-primary);
                }

                &.emphasis {
                    font-size: var(--font-size-medium);
                    line-height: var(--font-line-height-medium);
                }

                &.subtitle {
                    color: var(--color-text-secondary);

                    &.unit {
                        font-size: var(--font-size-small);
                        line-height: var(--font-line-height-small);
                    }
                }
            }
        }

        &-station-mark {
            color: white;
            white-space: nowrap;
            border-radius: 2px;
            zoom: var(--station-marker-zoom);
            font-size: 10px;

            &__name {
                text-align: center;
            }
        }

        &-text-mark {
            color: var(--color-text-secondary);
            white-space: nowrap;
            display: flex;
            flex-direction: column;
            align-items: center;
            zoom: var(--text-marker-zoom);

            &__name {
                font-size: 28px;
                line-height: 1em;
                letter-spacing: 4px;
            }

            &__subname {
                font-size: 20px;
                line-height: 1em;
                font-family: D-DIN;
                letter-spacing: -1px;
            }
        }
    }

    .header-date {
        line-height: var(--font-line-height-medium);
        font-size: var(--font-size-medium);
        color: var(--color-text-primary);
        border: none;
        background: none;
    }

    /**
      * ↑↑↑↑↑↑↑↑ 其它要写在这个文件里的样式必须放这里面 ↑↑↑↑↑↑↑↑
      * !!!!!!!! 警告 !!!!!!!!
      * !!!!!!!! 警告 !!!!!!!!
      * !!!!!!!! 警告 !!!!!!!!
      */
}