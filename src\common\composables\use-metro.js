import L from 'suntekmap';
import * as turf from '@turf/turf';

import { isEmptyValue } from '@utils/helper';

import { MapServerApi } from '@api/map-server';

const lineGeoJSON = ref(null);
const lineSimpleGeoJSON = ref(null);

const stationGeoJSON = ref(null);
const stationSimpleGeoJSON = ref(null);

const reqQueue = {
    lineReqQueue: null,
    lineSimpleReqQueue: null,
    stationReqQueue: null,
    stationSimpleReqQueue: null
};

const DEFAULT_ROUTE_OFFSET = 0;
const DEFAULT_PIXEL_SIZE = 8;
const DEFAULT_BLANK_SIZE = 3;

export const DEFAULT_PATTERN = {
    offset: DEFAULT_ROUTE_OFFSET,
    repeat: DEFAULT_PIXEL_SIZE * DEFAULT_BLANK_SIZE,
    symbolOptions: {
        pixelSize: DEFAULT_PIXEL_SIZE,
        polygon: false,
        pathOptions: {
            stroke: true,
            weight: 2,
            color: '#f00'
        }
    }
};

export const ROUTE_COLORS = [
    'red', 'blue', 'green', 'yellow', 'orange', 'purple', 'pink', 'brown', 'cyan', 'magenta', 'lime', 'maroon',
    'navy', 'olive', 'teal', 'aqua', 'fuchsia', 'gold', 'silver', 'coral', 'crimson', 'indigo', 'khaki', 'lavender',
    'plum', 'salmon', 'sienna', 'tan', 'tomato', 'turquoise', 'violet', 'wheat', 'azure', 'beige', 'bisque', 'chocolate',
    'firebrick', 'gainsboro', 'honeydew', 'ivory', 'lavenderblush', 'lemonchiffon', 'lightblue', 'lightcoral', 'lightcyan',
    'lightgoldenrodyellow', 'lightgreen', 'lightpink', 'lightsalmon', 'lightseagreen', 'lightskyblue', 'lightslategray',
    'lightsteelblue', 'lightyellow', 'limegreen', 'mediumaquamarine', 'mediumblue', 'mediumorchid', 'mediumpurple',
    'mediumseagreen', 'mediumslateblue', 'mediumspringgreen', 'mediumturquoise', 'mediumvioletred'
];

export const validLineString = (lineStringArray) => {
    if (!Array.isArray(lineStringArray)) {
        return false;
    }
    let validLineCoors = lineStringArray.length >= 2 && lineStringArray.every(coor => {
        // Array.isArray(coor) && coor.length === 2
        if (Array.isArray(coor)) {
            return coor.length === 2;
        }
        const { longitude, latitude, lng, lat } = coor || {};

        return (
            !isEmptyValue(longitude) && !Number.isNaN(+longitude) &&
            !isEmptyValue(latitude) && !Number.isNaN(+latitude)
        ) ||
        (
            !isEmptyValue(lng) && !Number.isNaN(+lng) &&
            !isEmptyValue(lat) && !Number.isNaN(+lat)
        );
    });

    if (lineStringArray.length === 2) {
        validLineCoors = validLineCoors && !turf.booleanEqual(turf.point(lineStringArray[0]), turf.point(lineStringArray[1]));
    }

    return validLineCoors;
};

export const useMetro = () => {
    // 站点名称匹配
    const isSameStation = (stationNameJSON, stationNameOdLine) => {
        const stationNameJSONParsed = (stationNameJSON || '').replace(/站+$/, '');
        const stationNameOdLineParsed = (stationNameOdLine || '').replace(/站+$/, '');

        return stationNameJSONParsed === stationNameOdLineParsed;
    };

    // 获取地铁线路 GeoJSON
    const loadMetroLine = async (options) => {
        const { force, simple } = options || {};
        if (!force) {
            if (simple && lineSimpleGeoJSON?.value?.features?.length) {
                return lineSimpleGeoJSON?.value;
            } else if (!simple && lineGeoJSON?.value?.features?.length) {
                return lineSimpleGeoJSON?.value;
            }
        }
        const targetQueue = simple ? 'lineSimpleReqQueue' : 'lineReqQueue';
        if (reqQueue[targetQueue] && Array.isArray(reqQueue[targetQueue])) {
            return new Promise((resolve, reject) => {
                reqQueue[targetQueue].push({ resolve, reject });
            });
        } else {
            let result = null;
            try {
                reqQueue[targetQueue] = [];
                if (simple) {
                    result = await MapServerApi.getLayerMetroLineSimple();
                    console.log('%c getLayerMetroLineSimple', 'font-size:18px;color:green;font-weight:700;', result);
                    lineSimpleGeoJSON.value = result;
                } else {
                    result = await MapServerApi.getLayerMetroLine();
                    console.log('%c getLayerMetroLine', 'font-size:18px;color:green;font-weight:700;', result);
                    lineGeoJSON.value = result;
                }
                reqQueue[targetQueue].forEach(({ resolve }) => {
                    resolve(result);
                });
            } catch (error) {
                console.log('%c getLayerMetroLine Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
                reqQueue[targetQueue].forEach(({ reject }) => {
                    reject(error);
                });
            }
            reqQueue[targetQueue] = null;
            return result;
        }
    };
    // 地图绘制地铁线路 GeoJSON
    const drawMetroLine = async (options) => {
        const { mapInstance, type = 'leaflet', force, simple, style } = options;

        if (!mapInstance) {
            console.log('%c mapInstance is required', 'font-size:18px;color:red;font-weight:700;');
            return;
        }

        let layerMetroLine = null;
        await loadMetroLine({
            force,
            simple
        });
        if (type === 'leaflet') {
            const targetLineGeoJSON = simple ? lineSimpleGeoJSON.value : lineGeoJSON.value;
            layerMetroLine = L.geoJSON(targetLineGeoJSON, {
                className: 'app-metro-map-line',
                style (feature) {
                    return {
                        weight: 1,
                        color: feature.properties.color,
                        ...(style || {})
                    };
                }
            }).bindTooltip((layer) => {
                return layer.feature.properties.name;
            }, {
                sticky: true,
                opacity: 0.85
            }).addTo(mapInstance);
        }
        return layerMetroLine;
    };

    // 获取地铁线路站点 GeoJSON
    const loadMetroStation = async (options) => {
        const { force, simple } = options || {};
        const targetQueue = simple ? 'stationSimpleReqQueue' : 'stationReqQueue';
        if (!force) {
            if (simple && stationSimpleGeoJSON?.value?.features?.length) {
                return stationSimpleGeoJSON?.value;
            } else if (!simple && stationGeoJSON?.value?.features?.length) {
                return stationGeoJSON?.value;
            }
        }
        if (reqQueue[targetQueue] && Array.isArray(reqQueue[targetQueue])) {
            return new Promise((resolve, reject) => {
                reqQueue[targetQueue].push({ resolve, reject });
            });
        } else {
            let result = null;
            try {
                reqQueue[targetQueue] = [];
                if (simple) {
                    result = await MapServerApi.getLayerMetroStationSimple();
                    console.log('%c getLayerMetroStationSimple', 'font-size:18px;color:green;font-weight:700;', result);
                    stationSimpleGeoJSON.value = result;
                } else {
                    result = await MapServerApi.getLayerMetroStation();
                    console.log('%c getLayerMetroStation', 'font-size:18px;color:green;font-weight:700;', result);
                    stationGeoJSON.value = result;
                }
                reqQueue[targetQueue].forEach(({ resolve }) => {
                    resolve(result);
                });
            } catch (error) {
                console.log('%c getLayerMetroLine Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
                reqQueue[targetQueue].forEach(({ reject }) => {
                    reject(error);
                });
            }
            reqQueue[targetQueue] = null;
            return result;
        }
    };
    const getMarkerType = (transFlag) => {
        const isTransformStation = +transFlag === 1;
        return !isTransformStation ? 'circle' : 'transfer';
    };
    const getStationMarkerLayout = (feature, options) => {
        const { getColor, customClass } = options;
        const markerType = getMarkerType(+feature.properties.trans_flag);
        const anchor = feature.properties.anchor || 'bottom';

        const fillColor = typeof getColor === 'function' ? getColor(feature) : feature.properties.fill_color;
        const externalClass = typeof customClass === 'function' ? customClass(feature, options, markerType) : customClass;

        const color = ['--icon-color', fillColor || ''].join(':');
        const content = `
            <div class="app-metro-map-station anchor-${anchor} ${externalClass || ''}" style="${[color].join(';')}">
                <div class="app-metro-map-station-icon ${markerType}"></div>
                <div class="app-metro-map-station-label">${feature.properties.name}</div>
            </div>`;
        return content;
    };
    // 绘制地铁站点
    const drawMetroStation = async (options) => {
        const { mapInstance, type = 'leaflet', force, simple, getColor, customClass } = options;

        if (!mapInstance) {
            console.log('%c mapInstance is required', 'font-size:18px;color:red;font-weight:700;');
            return;
        }

        let layerMetroStation = null;
        const targetLineGeoJSON = await loadMetroStation({
            force,
            simple
        });
        if (type === 'leaflet') {
            // const targetLineGeoJSON = simple ? lineSimpleGeoJSON.value : lineGeoJSON.value;
            layerMetroStation = L.geoJSON(targetLineGeoJSON, {
                style (feature) {
                    const isTransformStation = +feature.properties.trans_flag === 1;

                    const fillColor = typeof getColor === 'function' ? getColor(feature) : feature.properties.fill_color;

                    return !isTransformStation
                        ? {
                            stroke: false,
                            fillColor: fillColor || '',
                            fillOpacity: 1,
                            opacity: 1
                        }
                        : { };
                },
                pointToLayer (feature, latlng) {
                    const markerType = getMarkerType(+feature.properties.trans_flag);

                    const content = getStationMarkerLayout(feature, options);
                    const layer = L.marker(latlng, {
                        radius: 3,
                        icon: L.divIcon({
                            className: `app-metro-map-marker ${markerType}`,
                            iconSize: [6, 6],
                            html: content
                        })
                    });

                    return layer;
                }
            })
                .addTo(mapInstance);

            // 监听地图的 zoom 事件，设置站点 label 的缩放比例
            mapInstance.on('zoomend', () => {
                const container = mapInstance.getContainer();
                let currentZoom = mapInstance.getZoom();

                console.log('%c currentZoom', 'font-size:18px;color:red;font-weight:700;', currentZoom);

                if (!simple) {
                    currentZoom -= 12;
                }

                // 设置站点 icon 和 label 的缩放比例
                let cox = 1;
                if (currentZoom < 0) {
                    cox = currentZoom === -1 ? 0.85 : Math.pow(Math.abs(currentZoom) / 1.25, -1);
                }
                // console.log('%c currentZoom', 'font-size:18px;color:indianred;font-weight:700;', currentZoom, cox);
                container && container.style.setProperty('--station-marker-zoom', cox);
            });
        }
        return layerMetroStation;
    };
    // 绘制线路标志
    const drawMetroLineMark = async (options) => {
        const { mapInstance, type = 'leaflet', force, simple, getColor, customClass } = options;

        if (!mapInstance) {
            console.log('%c mapInstance is required', 'font-size:18px;color:red;font-weight:700;');
            return;
        }
        let layerMetroStation;
        const targetStationGeoJSON = await loadMetroStation({
            force,
            simple
        });
        const StartEndStation = [
            { name: '金台', iconAnchor: [-20, 15] },
            { name: '后湖大道', iconAnchor: [-20, 20] },
            { name: '汉口北', iconAnchor: [-20, 20] },
            { name: '径河', iconAnchor: [0, 20] },
            { name: '黄陂广场', iconAnchor: [20, 20] },
            { name: '青龙山地铁小镇', iconAnchor: [20, -10] },
            { name: '天河机场', iconAnchor: [20, -5] },
            { name: '佛祖岭', iconAnchor: [-20, 15] },
            { name: '新城十一路', iconAnchor: [20, -10] },
            { name: '东风公司', iconAnchor: [-20, 15] },
            { name: '金潭路', iconAnchor: [20, 40] },
            { name: '军运村', iconAnchor: [10, -10] },
            { name: '武汉站东广场', iconAnchor: [-20, -5] },
            { name: '红霞', iconAnchor: [25, -10] },
            { name: '国博中心南', iconAnchor: [0, -20] },
            { name: '通航机场', iconAnchor: [20, -20] },
            { name: '武汉火车站', iconAnchor: [25, 20] },
            { name: '柏林', iconAnchor: [20, -5] },
            { name: '沌阳大道', iconAnchor: [35, 5] },
            { name: '武汉站西广场', iconAnchor: [25, -10] },
            { name: '新月溪公园', iconAnchor: [25, -20] },
            { name: '葛店南站', iconAnchor: [-20, 15] },
            { name: '武汉东站', iconAnchor: [-25, -20] }
        ];
        const features = [];
        targetStationGeoJSON.features.forEach(item => {
            const stations = StartEndStation.find(station => station.name === item.properties.name);
            if (!stations) return;
            features.push({ ...item, ...stations });
        });
        const newGeoJson = {
            type: 'FeatureCollection',
            features
        };
        if (type === 'leaflet') {
            layerMetroStation = L.geoJSON(newGeoJson, {
                pointToLayer (feature, latlng) {
                    const { iconAnchor, properties } = feature;
                    const name = properties.lineName.slice(4);
                    const html = `
                        <div class="app-metro-map-station-mark" style="background-color: ${properties.fill_color}; width:${name.length}em">
                            <div class="app-metro-map-station-mark__name">${name}</div>
                        </div>`;
                    return L.marker(latlng, {
                        icon: L.divIcon({
                            html,
                            iconAnchor,
                            className: 'app-metro-map-line-marker'
                        })
                    });
                }
            }).addTo(mapInstance);

            // 监听地图的 zoom 事件，设置站点 label 的缩放比例
            mapInstance.on('zoomend', () => {
                const container = mapInstance.getContainer();
                let currentZoom = mapInstance.getZoom();

                console.log('%c currentZoom', 'font-size:18px;color:red;font-weight:700;', currentZoom);

                if (!simple) {
                    currentZoom -= 12;
                }

                // 设置站点 icon 和 label 的缩放比例
                let cox = 1;
                if (currentZoom < 0) {
                    cox = currentZoom === -1 ? 0.85 : Math.pow(Math.abs(currentZoom) / 1.25, -1);
                }
                // console.log('%c currentZoom', 'font-size:18px;color:indianred;font-weight:700;', currentZoom, cox);
                container && container.style.setProperty('--station-marker-zoom', cox);
            });
        }
        return layerMetroStation;
    };

    // 绘制武汉地图文字标记
    const drawMetroTextMark = async (options) => {
        const { mapInstance, type = 'leaflet', force, simple, getColor, customClass } = options;

        if (!mapInstance) {
            console.error('mapInstance is required');
            return;
        }

        const targetStationGeoJSON = await loadMetroStation({ force, simple });
        const startEndStations = [
            { name: '径河', iconAnchor: [20, 80] },
            { name: '红霞', iconAnchor: [0, -60] },
            { name: '柏林', iconAnchor: [20, -60] }
        ];
        const textMap = {
            径河: {
                name: '汉口',
                subname: 'hankou'
            },
            红霞: {
                name: '武昌',
                subname: 'wuchang'
            },
            柏林: {
                name: '汉阳',
                subname: 'hanyang'
            }
        };

        const features = targetStationGeoJSON.features
            .filter(item => startEndStations.some(station => station.name === item.properties.name))
            .map(item => {
                const station = startEndStations.find(station => station.name === item.properties.name);
                return { ...item, ...station };
            });

        const newGeoJson = {
            type: 'FeatureCollection',
            features
        };

        if (type === 'leaflet') {
            const createMarker = (feature, latlng) => {
                const { iconAnchor, properties } = feature;
                const name = textMap[properties.stationName].name;
                const subname = textMap[properties.stationName].subname;
                const html = `
                    <div class="app-metro-map-text-mark">
                        <div class="app-metro-map-text-mark__name">${name}</div>
                        <div class="app-metro-map-text-mark__subname">${subname}</div>
                    </div>`;
                return L.marker(latlng, {
                    icon: L.divIcon({
                        html,
                        iconAnchor,
                        className: 'app-metro-map-line-marker'
                    })
                });
            };

            const metroTextMark = L.geoJSON(newGeoJson, {
                pointToLayer: createMarker
            }).addTo(mapInstance);

            mapInstance.on('zoomend', () => {
                const container = mapInstance.getContainer();
                let currentZoom = mapInstance.getZoom();

                if (!simple) {
                    currentZoom -= 12;
                }

                let scale = 1;
                if (currentZoom < 0) {
                    scale = currentZoom === -1 ? 0.85 : Math.pow(Math.abs(currentZoom) / 1.25, -1);
                }
                container && container.style.setProperty('--text-marker-zoom', scale);
            });

            return metroTextMark;
        }
    };

    return {
        lineGeoJSON,
        lineSimpleGeoJSON,
        stationGeoJSON,
        stationSimpleGeoJSON,

        isSameStation,

        loadMetroLine,
        drawMetroLine,

        loadMetroStation,
        getMarkerType,
        getStationMarkerLayout,
        drawMetroStation,
        drawMetroLineMark,
        drawMetroTextMark
    };
};
