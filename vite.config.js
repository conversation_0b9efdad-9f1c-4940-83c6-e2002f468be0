// Vite 项配置插件
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';

import fs from 'fs';
import path, { resolve } from 'path';

// 组件库相关
import AutoImport from 'unplugin-auto-import/vite';
// // 不用这两个，用了之后重置的样式会被覆盖
// import Components from 'unplugin-vue-components/vite';
// import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';

// svg 图标处理
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';

// 杂项
import pkg from './package.json';
import getProxy from './vite.proxy.config';

// 版本文件生成
import EcpVersionVitePlugin from '@ecp/version-vite-plugin';

/**
 * 是否需要作为主应用
 */
const usePortal = true; // 如果需要作为主应用，请设为 true

// 工程端口
const SERVER_PORT = 30768;

// // 插件bug 无法支持破折号 注意！！！
// const packageName = pkg.name.replace(/-/g, '_');
const packageName = pkg.name;

export default ({ command, mode }) => {
    const useUniMode = mode === 'uni' || mode === 'previewUni';

    const proxy = getProxy(useUniMode, usePortal);

    // 生成主入口 html
    const htmlIndex = resolve(__dirname, useUniMode && usePortal ? 'template-portal.html' : 'template-index.html');
    fs.copyFileSync(htmlIndex, resolve(__dirname, 'index.html'));

    // 生成子入口 html
    const htmlSub = resolve(__dirname, 'template-index.html');
    fs.copyFileSync(htmlSub, resolve(__dirname, 'sub.html'));

    const pages = {
        index: resolve(__dirname, 'index.html'),
        sub: resolve(__dirname, 'sub.html'),
        login: resolve(__dirname, 'login/login.html')
    };

    return defineConfig({
        base: command === 'serve' && usePortal && useUniMode ? '/' : `/${packageName}/`,
        define: {
            USE_PORTAL: usePortal,
            PACKAGE_NAME: JSON.stringify(packageName)
        },
        plugins: [
            vue(),
            AutoImport({
                imports: ['vue', 'vue-router', 'pinia', {
                    axios: [
                        ['default', 'axios']
                    ]
                }],
                // vueTemplate: true,
                // resolvers: [ElementPlusResolver({
                //     importStyle: 'sass'
                // })],
                eslintrc: {
                    enabled: false, // 生成eslint extend文件，防止eslint报错 第一次打开生成文件即可,后续关闭，防止每次都生成
                    filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
                    globalsPropValue: true // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
                },
                dts: 'src/auto-imports.d.ts'
            }),
            // Components({
            //     resolvers: [
            //         ElementPlusResolver({
            //             importStyle: 'sass'
            //         })
            //     ]
            // }),
            createSvgIconsPlugin({
                iconDirs: [path.resolve(__dirname, 'src/assets/icons/svg')],
                // Specify symbolId format
                symbolId: 'icon-[dir]-[name]'
            }),
            new EcpVersionVitePlugin()
        ],
        server: {
            open: false,
            host: '0.0.0.0',
            port: SERVER_PORT,
            strictPort: true,
            https: false,
            proxy,
            rollupOptions: {
                input: pages,
                output: {
                    chunkFileNames: 'static/js/[name]-[hash].js',
                    entryFileNames: 'static/js/[name]-[hash].js',
                    assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
                    manualChunks: {
                        'ecp-ui-plus': ['@ecp/ecp-ui-plus']
                    }
                }
            }
        },
        preview: {
            host: '0.0.0.0',
            port: 9999,
            strictPort: true,
            https: false,
            proxy,
            rollupOptions: {
                input: pages,
                output: {
                    chunkFileNames: 'static/js/[name]-[hash].js',
                    entryFileNames: 'static/js/[name]-[hash].js',
                    assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
                    manualChunks: {
                        'ecp-ui-plus': ['@ecp/ecp-ui-plus']
                    }
                }
            }
        },
        build: {
            outDir: 'dist',
            rollupOptions: {
                input: pages,
                output: {
                    chunkFileNames: 'static/js/[name]-[hash].js',
                    entryFileNames: 'static/js/[name]-[hash].js',
                    assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
                    manualChunks: {
                        'ecp-ui-plus': ['@ecp/ecp-ui-plus']
                    }
                },
                treeshake: false // 必须设为false，否则仅import但未直接调用的模块不会打包进去
            }
        },
        css: {
            preprocessorOptions: {
                scss: {
                    additionalData: '@use "@styles/global/index.scss" as *;'
                }
            }
        },
        resolve: {
            alias: {
                '@': resolve('src'),
                '@api': resolve('src', 'app/api'),
                '@router': resolve('src', 'app/router'),
                '@store': resolve('src', 'app/store'),
                '@views': resolve('src', 'app/views'),
                '@assets': resolve('src', 'assets'),
                '@constants': resolve('src', 'constants'),
                '@common': resolve('src', 'common'),
                '@components': resolve('src', 'common/components'),
                '@composables': resolve('src', 'common/composables'),
                '@utils': resolve('src', 'common/utils'),
                '@styles': resolve('src', 'styles')
            }
        }
    });
};
