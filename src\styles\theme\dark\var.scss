@use "sass:map";

@use "@ecp/ecp-ui-plus/theme-chalk/src/tools/theme-config.scss" as *;

@use "./mapper.scss" as *;

.dark,
html.dark,
[data-theme^="dark"],
[data-theme$="dark"] {
    @each $type in ($types) {
        $color: map.get($theme-colors, $type, "base");
        @include set-color-variable($type, $color, false, true);
    }

    @include set-mapper-variable("color-text", $text-color);

    @include set-mapper-variable("background-color", $bg-color);
    // @include set-mapper-variable-namespace("bg-color", $bg-color);
    @include set-mapper-variable("fill-color", $fill-color);

    .elp-table {
        @include set-mapper-variable("table", $table);
        @include set-mapper-variable-namespace("table", $table);
    }
}