<template>
    <el-dialog custom-class="app-dialog actual-height" v-bind="$attrs" :title="title" :width="width" destroy-on-close
        append-to-body :close-on-click-modal="false" v-model:visible="dialogVisible" :before-close="close">
        <slot :exportType="exportType">
            <el-radio-group v-model="exportType">
                <el-radio v-for="(item, index) in exportTypes" :key="index" :label="index">{{ item.title }}</el-radio>
            </el-radio-group>
        </slot>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="close">{{ cancelText }}</el-button>
                <el-button type="primary" :loading="exportLoading" @click="exportExcel">{{ confirmText }}</el-button>
            </span>
        </template>
    </el-dialog>
</template>
<script setup>
import { downloadByLink } from '@utils/download';
defineComponent({
    name: 'app-file-export'
});

const props = defineProps({
    exportParams: {
        type: Object,
        default: () => ({})
    },
    exportTypes: {
        type: Array,
        default: () => []
    },
    beforeExport: {
        type: Function
    },
    width: {
        type: String,
        default: '600px'
    },
    title: {
        type: String,
        default: '导出'
    },
    confirmText: {
        type: String,
        default: '下 载'
    },
    cancelText: {
        type: String,
        default: '取 消'
    }
});

const dialogVisible = ref(false);
const exportType = ref(0);
const exportLoading = ref(false);

const emits = defineEmits(['close']);

// 提供给父组件调用打开弹窗
const open = () => {
    dialogVisible.value = true;
};

// 导出
const exportExcel = async () => {
    exportLoading.value = true;
    let _url = props.exportTypes?.[exportType.value]?.url;
    let _exportParams = props.exportParams;
    if (typeof props.beforeExport === 'function') {
        const res = props.beforeExport(
            exportType.value,
            props.exportParams,
            props.exportTypes
        );
        if (!res) return;
        _url = res.url || _url;
        _exportParams = res.exportParams || _exportParams;
    }

    const _queryStr = Object.entries(_exportParams)
        .map(([key, val]) => `${key}=${encodeURIComponent(val)}`)
        .join('&');
    const hasQuerySign = _url[_url.length - 1] === '?' ? '' : '?';
    _url += hasQuerySign + _queryStr;

    try {
        console.log(`[download]： ${_url}`);
        await downloadByLink(_url);
    } catch (err) {
        console.log('error', err);
    } finally {
        exportLoading.value = false;
        close();
    }
};
const close = () => {
    dialogVisible.value = false;
    emits('close');
};
</script>

<style lang="scss" scoped>
.app-dialog {
    .elp-radio {
        margin-bottom: 4px;
    }

    .elp-dialog__body {
        height: 50px;
        max-height: 60vh;
    }
}
</style>
