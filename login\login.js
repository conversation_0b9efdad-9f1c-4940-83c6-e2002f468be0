import { createApp } from 'vue';
import ElementPlus, {
    useGlobalConfig
} from 'element-plus';

import EcpUIPlus, { ElementDefaultConfig } from '@ecp/ecp-ui-plus';

import '@styles/index';
import './vite-ecp-login-component/styles/index.scss';

import Login from './login.vue';
import store from '@store';

import * as InitialUtils from '@/common/utils/initial-utils';

const ElGlobalConfig = useGlobalConfig();
ElGlobalConfig.value = ElementDefaultConfig;

const initApp = async () => {
    try {
        await InitialUtils.systemInitial({
            configOnly: true
        });
    } catch (error) {
        console.debug(
            '%c initApp Caught Error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    }
};

const renderApp = async (props = {}) => {
    const VueApp = createApp(Login);

    VueApp.use(store);

    VueApp.use(ElementPlus);
    VueApp.use(EcpUIPlus);

    await initApp();
    VueApp.mount('#login');
    return VueApp;
};

renderApp();
