lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  '@ecp/ecp-login-component-vite':
    specifier: ^3.0.0
    version: 3.0.0(@element-plus/icons-vue@2.3.1)(element-plus@2.8.0)(vue@3.4.15)
  '@ecp/ecp-ui-plus':
    specifier: ^1.0.2
    version: 1.0.2(element-plus@2.8.0)(vue@3.4.15)
  '@element-plus/icons-vue':
    specifier: ^2.3.1
    version: 2.3.1(vue@3.4.15)
  '@turf/turf':
    specifier: ^7.1.0
    version: 7.1.0
  axios:
    specifier: ^1.3.5
    version: 1.3.6
  dayjs:
    specifier: ^1.11.7
    version: 1.11.7
  echarts:
    specifier: 5.5.0
    version: 5.5.0
  ecp-chart:
    specifier: ^2.0.4
    version: 2.0.4(vue@3.4.15)
  element-plus:
    specifier: ^2.8.0
    version: 2.8.0(vue@3.4.15)
  js-cookie:
    specifier: ^3.0.1
    version: 3.0.1
  pinia:
    specifier: ^2.0.34
    version: 2.0.34(vue@3.4.15)
  qs:
    specifier: ^6.11.1
    version: 6.11.1
  suntekmap:
    specifier: ^1.1.2
    version: 1.1.2(webpack@4.47.0)
  vue:
    specifier: ^3.3.2
    version: 3.4.15
  vue-router:
    specifier: ^4.1.6
    version: 4.1.6(vue@3.4.15)
  vue-virtual-scroller:
    specifier: 2.0.0-beta.8
    version: 2.0.0-beta.8(vue@3.4.15)

devDependencies:
  '@ecp/version-vite-plugin':
    specifier: ^0.1.5
    version: 0.1.5
  '@types/chart.js':
    specifier: ^2.9.41
    version: 2.9.41
  '@vitejs/plugin-legacy':
    specifier: ^4.0.2
    version: 4.0.2(terser@5.30.3)(vite@4.5.0)
  '@vitejs/plugin-vue':
    specifier: ^4.1.0
    version: 4.1.0(vite@4.5.0)(vue@3.4.15)
  eslint:
    specifier: ^8.0.1
    version: 8.38.0
  eslint-config-standard:
    specifier: ^17.0.0
    version: 17.0.0(eslint-plugin-import@2.27.5)(eslint-plugin-n@15.7.0)(eslint-plugin-promise@6.1.1)(eslint@8.38.0)
  eslint-plugin-import:
    specifier: ^2.25.2
    version: 2.27.5(eslint@8.38.0)
  eslint-plugin-n:
    specifier: ^15.0.0
    version: 15.7.0(eslint@8.38.0)
  eslint-plugin-promise:
    specifier: ^6.0.0
    version: 6.1.1(eslint@8.38.0)
  eslint-plugin-vue:
    specifier: ^9.10.0
    version: 9.11.0(eslint@8.38.0)
  prettier:
    specifier: ^2.8.7
    version: 2.8.7
  sass:
    specifier: ^1.62.0
    version: 1.62.0
  unplugin-auto-import:
    specifier: ^0.15.2
    version: 0.15.3
  unplugin-icons:
    specifier: ^0.16.1
    version: 0.16.1
  unplugin-vue-components:
    specifier: ^0.24.1
    version: 0.24.1(vue@3.4.15)
  vite:
    specifier: ^4.5.0
    version: 4.5.0(sass@1.62.0)(terser@5.30.3)
  vite-plugin-legacy-qiankun:
    specifier: ^0.0.7
    version: 0.0.7
  vite-plugin-svg-icons:
    specifier: ^2.0.1
    version: 2.0.1(vite@4.5.0)

packages:

  /@ampproject/remapping@2.2.1:
    resolution: {integrity: sha512-lFMjJTrFL3j7L9yBxwYfCq2k6qqwHyzuUl/XBnif78PWTJYyL/dfowQHWE3sp6U6ZzqWiiIZnpTMO96zhkjwtg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
    dev: true

  /@antfu/install-pkg@0.1.1:
    resolution: {integrity: sha512-LyB/8+bSfa0DFGC06zpCEfs89/XoWZwws5ygEa5D+Xsm3OfI+aXQ86VgVG7Acyef+rSZ5HE7J8rrxzrQeM3PjQ==}
    dependencies:
      execa: 5.1.1
      find-up: 5.0.0
    dev: true

  /@antfu/utils@0.7.2:
    resolution: {integrity: sha512-vy9fM3pIxZmX07dL+VX1aZe7ynZ+YyB0jY+jE6r3hOK6GNY2t6W8rzpFC4tgpbXUYABkFQwgJq2XYXlxbXAI0g==}
    dev: true

  /@babel/code-frame@7.21.4:
    resolution: {integrity: sha512-LYvhNKfwWSPpocw8GI7gpK2nq3HSDuEPC/uSYaALSJu9xjsalaaYFOq0Pwt5KmVqwEbZlDu81aLXwBOmD/Fv9g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.18.6
    dev: true

  /@babel/compat-data@7.21.4:
    resolution: {integrity: sha512-/DYyDpeCfaVinT40FPGdkkb+lYSKvsVuMjDAG7jPOWWiM1ibOaB9CXJAlc4d1QpP/U2q2P9jbrSlClKSErd55g==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/core@7.21.4:
    resolution: {integrity: sha512-qt/YV149Jman/6AfmlxJ04LMIu8bMoyl3RB91yTFrxQmgbrSvQMy7cI8Q62FHx1t8wJ8B5fu0UDoLwHAhUo1QA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.2.1
      '@babel/code-frame': 7.21.4
      '@babel/generator': 7.21.4
      '@babel/helper-compilation-targets': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-module-transforms': 7.21.2
      '@babel/helpers': 7.21.0
      '@babel/parser': 7.23.6
      '@babel/template': 7.20.7
      '@babel/traverse': 7.21.4
      '@babel/types': 7.21.4
      convert-source-map: 1.9.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/generator@7.21.4:
    resolution: {integrity: sha512-NieM3pVIYW2SwGzKoqfPrQsf4xGs9M9AIG3ThppsSRmO+m7eQhmI6amajKMUeIO37wFfsvnvcxQFx6x6iqxDnA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.21.4
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 2.5.2
    dev: true

  /@babel/helper-annotate-as-pure@7.18.6:
    resolution: {integrity: sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.21.4
    dev: true

  /@babel/helper-builder-binary-assignment-operator-visitor@7.18.9:
    resolution: {integrity: sha512-yFQ0YCHoIqarl8BCRwBL8ulYUaZpz3bNsA7oFepAzee+8/+ImtADXNOmO5vJvsPff3qi+hvpkY/NYBTrBQgdNw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-explode-assignable-expression': 7.18.6
      '@babel/types': 7.21.4
    dev: true

  /@babel/helper-compilation-targets@7.21.4(@babel/core@7.21.4):
    resolution: {integrity: sha512-Fa0tTuOXZ1iL8IeDFUWCzjZcn+sJGd9RZdH9esYVjEejGmzf+FFYQpMi/kZUk2kPy/q1H3/GPw7np8qar/stfg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/compat-data': 7.21.4
      '@babel/core': 7.21.4
      '@babel/helper-validator-option': 7.21.0
      browserslist: 4.21.5
      lru-cache: 5.1.1
      semver: 6.3.0
    dev: true

  /@babel/helper-create-class-features-plugin@7.21.4(@babel/core@7.21.4):
    resolution: {integrity: sha512-46QrX2CQlaFRF4TkwfTt6nJD7IHq8539cCL7SDpqWSDeJKY1xylKKY5F/33mJhLZ3mFvKv2gGrVS6NkyF6qs+Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.21.0
      '@babel/helper-member-expression-to-functions': 7.21.0
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-replace-supers': 7.20.7
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0
      '@babel/helper-split-export-declaration': 7.18.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-create-regexp-features-plugin@7.21.4(@babel/core@7.21.4):
    resolution: {integrity: sha512-M00OuhU+0GyZ5iBBN9czjugzWrEq2vDpf/zCYHxxf93ul/Q5rv+a5h+/+0WnI1AebHNVtl5bFV0qsJoH23DbfA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      regexpu-core: 5.3.2
    dev: true

  /@babel/helper-define-polyfill-provider@0.3.3(@babel/core@7.21.4):
    resolution: {integrity: sha512-z5aQKU4IzbqCC1XH0nAqfsFLMVSo22SBKUc0BxGrLkolTdPTructy0ToNnlO2zA4j9Q/7pjMZf0DSY+DSTYzww==}
    peerDependencies:
      '@babel/core': ^7.4.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-compilation-targets': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
      debug: 4.3.4
      lodash.debounce: 4.0.8
      resolve: 1.22.3
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-environment-visitor@7.18.9:
    resolution: {integrity: sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-explode-assignable-expression@7.18.6:
    resolution: {integrity: sha512-eyAYAsQmB80jNfg4baAtLeWAQHfHFiR483rzFK+BhETlGZaQC9bsfrugfXDCbRHLQbIA7U5NxhhOxN7p/dWIcg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.21.4
    dev: true

  /@babel/helper-function-name@7.21.0:
    resolution: {integrity: sha512-HfK1aMRanKHpxemaY2gqBmL04iAPOPRj7DxtNbiDOrJK+gdwkiNRVpCpUJYbUT+aZyemKN8brqTOxzCaG6ExRg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.20.7
      '@babel/types': 7.21.4
    dev: true

  /@babel/helper-hoist-variables@7.18.6:
    resolution: {integrity: sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.21.4
    dev: true

  /@babel/helper-member-expression-to-functions@7.21.0:
    resolution: {integrity: sha512-Muu8cdZwNN6mRRNG6lAYErJ5X3bRevgYR2O8wN0yn7jJSnGDu6eG59RfT29JHxGUovyfrh6Pj0XzmR7drNVL3Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.21.4
    dev: true

  /@babel/helper-module-imports@7.21.4:
    resolution: {integrity: sha512-orajc5T2PsRYUN3ZryCEFeMDYwyw09c/pZeaQEZPH0MpKzSvn3e0uXsDBu3k03VI+9DBiRo+l22BfKTpKwa/Wg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.21.4
    dev: true

  /@babel/helper-module-transforms@7.21.2:
    resolution: {integrity: sha512-79yj2AR4U/Oqq/WOV7Lx6hUjau1Zfo4cI+JLAVYeMV5XIlbOhmjEk5ulbTc9fMpmlojzZHkUUxAiK+UKn+hNQQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-module-imports': 7.21.4
      '@babel/helper-simple-access': 7.20.2
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/helper-validator-identifier': 7.19.1
      '@babel/template': 7.20.7
      '@babel/traverse': 7.21.4
      '@babel/types': 7.21.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-optimise-call-expression@7.18.6:
    resolution: {integrity: sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.21.4
    dev: true

  /@babel/helper-plugin-utils@7.20.2:
    resolution: {integrity: sha512-8RvlJG2mj4huQ4pZ+rU9lqKi9ZKiRmuvGuM2HlWmkmgOhbs6zEAw6IEiJ5cQqGbDzGZOhwuOQNtZMi/ENLjZoQ==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-remap-async-to-generator@7.18.9(@babel/core@7.21.4):
    resolution: {integrity: sha512-dI7q50YKd8BAv3VEfgg7PS7yD3Rtbi2J1XMXaalXO0W0164hYLnh8zpjRS0mte9MfVp/tltvr/cfdXPvJr1opA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-wrap-function': 7.20.5
      '@babel/types': 7.21.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-replace-supers@7.20.7:
    resolution: {integrity: sha512-vujDMtB6LVfNW13jhlCrp48QNslK6JXi7lQG736HVbHz/mbf4Dc7tIRh1Xf5C0rF7BP8iiSxGMCmY6Ci1ven3A==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-member-expression-to-functions': 7.21.0
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/template': 7.20.7
      '@babel/traverse': 7.21.4
      '@babel/types': 7.21.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-simple-access@7.20.2:
    resolution: {integrity: sha512-+0woI/WPq59IrqDYbVGfshjT5Dmk/nnbdpcF8SnMhhXObpTq2KNBdLFRFrkVdbDOyUmHBCxzm5FHV1rACIkIbA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.21.4
    dev: true

  /@babel/helper-skip-transparent-expression-wrappers@7.20.0:
    resolution: {integrity: sha512-5y1JYeNKfvnT8sZcK9DVRtpTbGiomYIHviSP3OQWmDPU3DeH4a1ZlT/N2lyQ5P8egjcRaT/Y9aNqUxK0WsnIIg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.21.4
    dev: true

  /@babel/helper-split-export-declaration@7.18.6:
    resolution: {integrity: sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.21.4
    dev: true

  /@babel/helper-string-parser@7.19.4:
    resolution: {integrity: sha512-nHtDoQcuqFmwYNYPz3Rah5ph2p8PFeFCsZk9A/48dPc/rGocJ5J3hAAZ7pb76VWX3fZKu+uEr/FhH5jLx7umrw==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-identifier@7.19.1:
    resolution: {integrity: sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-option@7.21.0:
    resolution: {integrity: sha512-rmL/B8/f0mKS2baE9ZpyTcTavvEuWhTTW8amjzXNvYG4AwBsqTLikfXsEofsJEfKHf+HQVQbFOHy6o+4cnC/fQ==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-wrap-function@7.20.5:
    resolution: {integrity: sha512-bYMxIWK5mh+TgXGVqAtnu5Yn1un+v8DDZtqyzKRLUzrh70Eal2O3aZ7aPYiMADO4uKlkzOiRiZ6GX5q3qxvW9Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-function-name': 7.21.0
      '@babel/template': 7.20.7
      '@babel/traverse': 7.21.4
      '@babel/types': 7.21.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helpers@7.21.0:
    resolution: {integrity: sha512-XXve0CBtOW0pd7MRzzmoyuSj0e3SEzj8pgyFxnTT1NJZL38BD1MK7yYrm8yefRPIDvNNe14xR4FdbHwpInD4rA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.20.7
      '@babel/traverse': 7.21.4
      '@babel/types': 7.21.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/highlight@7.18.6:
    resolution: {integrity: sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.19.1
      chalk: 2.4.2
      js-tokens: 4.0.0
    dev: true

  /@babel/parser@7.23.6:
    resolution: {integrity: sha512-Z2uID7YJ7oNvAI20O9X0bblw7Qqs8Q2hFy0R9tAfnfLkp5MW0UH9eUvnDSnFwKZ0AvgS1ucqR4KzvVHgnke1VQ==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.21.4

  /@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-Dgxsyg54Fx1d4Nge8UnvTrED63vrwOdPmyvPzlNN/boaliRP54pm3pGzZD1SJUwrBA+Cs/xdG8kXX6Mn/RfISQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.20.7(@babel/core@7.21.4):
    resolution: {integrity: sha512-sbr9+wNE5aXMBBFBICk01tt7sBf2Oc9ikRFEcem/ZORup9IMUdNhW7/wVLEbbtlWOsEubJet46mHAL2C8+2jKQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0
      '@babel/plugin-proposal-optional-chaining': 7.21.0(@babel/core@7.21.4)
    dev: true

  /@babel/plugin-proposal-async-generator-functions@7.20.7(@babel/core@7.21.4):
    resolution: {integrity: sha512-xMbiLsn/8RK7Wq7VeVytytS2L6qE69bXPB10YCmMdDZbKF4okCqY74pI/jJQ/8U0b/F6NrT2+14b8/P9/3AMGA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-remap-async-to-generator': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.21.4)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-class-properties@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-class-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-class-static-block@7.21.0(@babel/core@7.21.4):
    resolution: {integrity: sha512-XP5G9MWNUskFuP30IfFSEFB0Z6HzLIUcjYM4bYOPHXl7eiJ9HFv8tWj6TXTN5QODiEhDZAeI4hLok2iHFFV4hw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-class-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.21.4)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-dynamic-import@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-1auuwmK+Rz13SJj36R+jqFPMJWyKEDd7lLSdOj4oJK0UTgGueSAtkrCvz9ewmgyU/P941Rv2fQwZJN8s6QruXw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.21.4)
    dev: true

  /@babel/plugin-proposal-export-namespace-from@7.18.9(@babel/core@7.21.4):
    resolution: {integrity: sha512-k1NtHyOMvlDDFeb9G5PhUXuGj8m/wiwojgQVEhJ/fsVsMCpLyOP4h0uGEjYJKrRI+EVPlb5Jk+Gt9P97lOGwtA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.21.4)
    dev: true

  /@babel/plugin-proposal-json-strings@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-lr1peyn9kOdbYc0xr0OdHTZ5FMqS6Di+H0Fz2I/JwMzGmzJETNeOFq2pBySw6X/KFL5EWDjlJuMsUGRFb8fQgQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.21.4)
    dev: true

  /@babel/plugin-proposal-logical-assignment-operators@7.20.7(@babel/core@7.21.4):
    resolution: {integrity: sha512-y7C7cZgpMIjWlKE5T7eJwp+tnRYM89HmRvWM5EQuB5BoHEONjmQ8lSNmBUwOyy/GFRsohJED51YBF79hE1djug==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.21.4)
    dev: true

  /@babel/plugin-proposal-nullish-coalescing-operator@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.21.4)
    dev: true

  /@babel/plugin-proposal-numeric-separator@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-ozlZFogPqoLm8WBr5Z8UckIoE4YQ5KESVcNudyXOR8uqIkliTEgJ3RoketfG6pmzLdeZF0H/wjE9/cCEitBl7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.21.4)
    dev: true

  /@babel/plugin-proposal-object-rest-spread@7.20.7(@babel/core@7.21.4):
    resolution: {integrity: sha512-d2S98yCiLxDVmBmE8UjGcfPvNEUbA1U5q5WxaWFUGRzJSVAZqm5W6MbPct0jxnegUZ0niLeNX+IOzEs7wYg9Dg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.21.4
      '@babel/core': 7.21.4
      '@babel/helper-compilation-targets': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-transform-parameters': 7.21.3(@babel/core@7.21.4)
    dev: true

  /@babel/plugin-proposal-optional-catch-binding@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-Q40HEhs9DJQyaZfUjjn6vE8Cv4GmMHCYuMGIWUnlxH6400VGxOuwWsPt4FxXxJkC/5eOzgn0z21M9gMT4MOhbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.21.4)
    dev: true

  /@babel/plugin-proposal-optional-chaining@7.21.0(@babel/core@7.21.4):
    resolution: {integrity: sha512-p4zeefM72gpmEe2fkUr/OnOXpWEf8nAgk7ZYVqqfFiyIG7oFfVZcCrU64hWn5xp4tQ9LkV4bTIa5rD0KANpKNA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.21.4)
    dev: true

  /@babel/plugin-proposal-private-methods@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-nutsvktDItsNn4rpGItSNV2sz1XwS+nfU0Rg8aCx3W3NOKVzdMjJRu0O5OkgDp3ZGICSTbgRpxZoWsxoKRvbeA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-class-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-private-property-in-object@7.21.0(@babel/core@7.21.4):
    resolution: {integrity: sha512-ha4zfehbJjc5MmXBlHec1igel5TJXXLDDRbuJ4+XT2TJcyD9/V1919BA8gMvsdHcNMBy4WBUBiRb3nw/EQUtBw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-create-class-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.21.4)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-unicode-property-regex@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w==}
    engines: {node: '>=4'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-regexp-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.21.4):
    resolution: {integrity: sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.21.4):
    resolution: {integrity: sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.21.4):
    resolution: {integrity: sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.21.4):
    resolution: {integrity: sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.21.4):
    resolution: {integrity: sha1-AolkqbqA28CUyRXEh618TnpmRlo=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-import-assertions@7.20.0(@babel/core@7.21.4):
    resolution: {integrity: sha512-IUh1vakzNoWalR8ch/areW7qFopR2AEw03JlG7BbrDqmQ4X3q9uuipQwSGrUn7oGiemKjtSLDhNtQHzMHr1JdQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.21.4):
    resolution: {integrity: sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.21.4):
    resolution: {integrity: sha1-ypHvRjA1MESLkGZSusLp/plB9pk=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.21.4):
    resolution: {integrity: sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.21.4):
    resolution: {integrity: sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.21.4):
    resolution: {integrity: sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.21.4):
    resolution: {integrity: sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.21.4):
    resolution: {integrity: sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.21.4):
    resolution: {integrity: sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.21.4):
    resolution: {integrity: sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-arrow-functions@7.20.7(@babel/core@7.21.4):
    resolution: {integrity: sha512-3poA5E7dzDomxj9WXWwuD6A5F3kc7VXwIJO+E+J8qtDtS+pXPAhrgEyh+9GBwBgPq1Z+bB+/JD60lp5jsN7JPQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-async-to-generator@7.20.7(@babel/core@7.21.4):
    resolution: {integrity: sha512-Uo5gwHPT9vgnSXQxqGtpdufUiWp96gk7yiP4Mp5bm1QMkEmLXBO7PAGYbKoJ6DhAwiNkcHFBol/x5zZZkL/t0Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-module-imports': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-remap-async-to-generator': 7.18.9(@babel/core@7.21.4)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-block-scoped-functions@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-ExUcOqpPWnliRcPqves5HJcJOvHvIIWfuS4sroBUenPuMdmW+SMHDakmtS7qOo13sVppmUijqeTv7qqGsvURpQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-block-scoping@7.21.0(@babel/core@7.21.4):
    resolution: {integrity: sha512-Mdrbunoh9SxwFZapeHVrwFmri16+oYotcZysSzhNIVDwIAb1UV+kvnxULSYq9J3/q5MDG+4X6w8QVgD1zhBXNQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-classes@7.21.0(@babel/core@7.21.4):
    resolution: {integrity: sha512-RZhbYTCEUAe6ntPehC4hlslPWosNHDox+vAs4On/mCLRLfoDVHf6hVEd7kuxr1RnHwJmxFfUM3cZiZRmPxJPXQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-compilation-targets': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.21.0
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-replace-supers': 7.20.7
      '@babel/helper-split-export-declaration': 7.18.6
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-computed-properties@7.20.7(@babel/core@7.21.4):
    resolution: {integrity: sha512-Lz7MvBK6DTjElHAmfu6bfANzKcxpyNPeYBGEafyA6E5HtRpjpZwU+u7Qrgz/2OR0z+5TvKYbPdphfSaAcZBrYQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/template': 7.20.7
    dev: true

  /@babel/plugin-transform-destructuring@7.21.3(@babel/core@7.21.4):
    resolution: {integrity: sha512-bp6hwMFzuiE4HqYEyoGJ/V2LeIWn+hLVKc4pnj++E5XQptwhtcGmSayM029d/j2X1bPKGTlsyPwAubuU22KhMA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-dotall-regex@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-6S3jpun1eEbAxq7TdjLotAsl4WpQI9DxfkycRcKrjhQYzU87qpXdknpBg/e+TdcMehqGnLFi7tnFUBR02Vq6wg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-regexp-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-duplicate-keys@7.18.9(@babel/core@7.21.4):
    resolution: {integrity: sha512-d2bmXCtZXYc59/0SanQKbiWINadaJXqtvIQIzd4+hNwkWBgyCd5F/2t1kXoUdvPMrxzPvhK6EMQRROxsue+mfw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-exponentiation-operator@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-wzEtc0+2c88FVR34aQmiz56dxEkxr2g8DQb/KfaFa1JYXOFVsbhvAonFN6PwVWj++fKmku8NP80plJ5Et4wqHw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.18.9
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-for-of@7.21.0(@babel/core@7.21.4):
    resolution: {integrity: sha512-LlUYlydgDkKpIY7mcBWvyPPmMcOphEyYA27Ef4xpbh1IiDNLr0kZsos2nf92vz3IccvJI25QUwp86Eo5s6HmBQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-function-name@7.18.9(@babel/core@7.21.4):
    resolution: {integrity: sha512-WvIBoRPaJQ5yVHzcnJFor7oS5Ls0PYixlTYE63lCj2RtdQEl15M68FXQlxnG6wdraJIXRdR7KI+hQ7q/9QjrCQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-compilation-targets': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-function-name': 7.21.0
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-literals@7.18.9(@babel/core@7.21.4):
    resolution: {integrity: sha512-IFQDSRoTPnrAIrI5zoZv73IFeZu2dhu6irxQjY9rNjTT53VmKg9fenjvoiOWOkJ6mm4jKVPtdMzBY98Fp4Z4cg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-member-expression-literals@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-qSF1ihLGO3q+/g48k85tUjD033C29TNTVB2paCwZPVmOsjn9pClvYYrM2VeJpBY2bcNkuny0YUyTNRyRxJ54KA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-modules-amd@7.20.11(@babel/core@7.21.4):
    resolution: {integrity: sha512-NuzCt5IIYOW0O30UvqktzHYR2ud5bOWbY0yaxWZ6G+aFzOMJvrs5YHNikrbdaT15+KNO31nPOy5Fim3ku6Zb5g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-module-transforms': 7.21.2
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-modules-commonjs@7.21.2(@babel/core@7.21.4):
    resolution: {integrity: sha512-Cln+Yy04Gxua7iPdj6nOV96smLGjpElir5YwzF0LBPKoPlLDNJePNlrGGaybAJkd0zKRnOVXOgizSqPYMNYkzA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-module-transforms': 7.21.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-simple-access': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-modules-systemjs@7.20.11(@babel/core@7.21.4):
    resolution: {integrity: sha512-vVu5g9BPQKSFEmvt2TA4Da5N+QVS66EX21d8uoOihC+OCpUoGvzVsXeqFdtAEfVa5BILAeFt+U7yVmLbQnAJmw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-module-transforms': 7.21.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-validator-identifier': 7.19.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-modules-umd@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-dcegErExVeXcRqNtkRU/z8WlBLnvD4MRnHgNs3MytRO1Mn1sHRyhbcpYbVMGclAqOjdW+9cfkdZno9dFdfKLfQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-module-transforms': 7.21.2
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-named-capturing-groups-regex@7.20.5(@babel/core@7.21.4):
    resolution: {integrity: sha512-mOW4tTzi5iTLnw+78iEq3gr8Aoq4WNRGpmSlrogqaiCBoR1HFhpU4JkpQFOHfeYx3ReVIFWOQJS4aZBRvuZ6mA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-regexp-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-new-target@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-DjwFA/9Iu3Z+vrAn+8pBUGcjhxKguSMlsFqeCKbhb9BAV756v0krzVK04CRDi/4aqmk8BsHb4a/gFcaA5joXRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-object-super@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-uvGz6zk+pZoS1aTZrOvrbj6Pp/kK2mp45t2B+bTDre2UgsZZ8EZLSJtUg7m/no0zOJUWgFONpB7Zv9W2tSaFlA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-replace-supers': 7.20.7
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-parameters@7.21.3(@babel/core@7.21.4):
    resolution: {integrity: sha512-Wxc+TvppQG9xWFYatvCGPvZ6+SIUxQ2ZdiBP+PHYMIjnPXD+uThCshaz4NZOnODAtBjjcVQQ/3OKs9LW28purQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-property-literals@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-cYcs6qlgafTud3PAzrrRNbQtfpQ8+y/+M5tKmksS9+M1ckbH6kzY8MrexEM9mcA6JDsukE19iIRvAyYl463sMg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-regenerator@7.20.5(@babel/core@7.21.4):
    resolution: {integrity: sha512-kW/oO7HPBtntbsahzQ0qSE3tFvkFwnbozz3NWFhLGqH75vLEg+sCGngLlhVkePlCs3Jv0dBBHDzCHxNiFAQKCQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      regenerator-transform: 0.15.1
    dev: true

  /@babel/plugin-transform-reserved-words@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-oX/4MyMoypzHjFrT1CdivfKZ+XvIPMFXwwxHp/r0Ddy2Vuomt4HDFGmft1TAY2yiTKiNSsh3kjBAzcM8kSdsjA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-shorthand-properties@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-eCLXXJqv8okzg86ywZJbRn19YJHU4XUa55oz2wbHhaQVn/MM+XhukiT7SYqp/7o00dg52Rj51Ny+Ecw4oyoygw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-spread@7.20.7(@babel/core@7.21.4):
    resolution: {integrity: sha512-ewBbHQ+1U/VnH1fxltbJqDeWBU1oNLG8Dj11uIv3xVf7nrQu0bPGe5Rf716r7K5Qz+SqtAOVswoVunoiBtGhxw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0
    dev: true

  /@babel/plugin-transform-sticky-regex@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-kfiDrDQ+PBsQDO85yj1icueWMfGfJFKN1KCkndygtu/C9+XUfydLC8Iv5UYJqRwy4zk8EcplRxEOeLyjq1gm6Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-template-literals@7.18.9(@babel/core@7.21.4):
    resolution: {integrity: sha512-S8cOWfT82gTezpYOiVaGHrCbhlHgKhQt8XH5ES46P2XWmX92yisoZywf5km75wv5sYcXDUCLMmMxOLCtthDgMA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-typeof-symbol@7.18.9(@babel/core@7.21.4):
    resolution: {integrity: sha512-SRfwTtF11G2aemAZWivL7PD+C9z52v9EvMqH9BuYbabyPuKUvSWks3oCg6041pT925L4zVFqaVBeECwsmlguEw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-unicode-escapes@7.18.10(@babel/core@7.21.4):
    resolution: {integrity: sha512-kKAdAI+YzPgGY/ftStBFXTI1LZFju38rYThnfMykS+IXy8BVx+res7s2fxf1l8I35DV2T97ezo6+SGrXz6B3iQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-unicode-regex@7.18.6(@babel/core@7.21.4):
    resolution: {integrity: sha512-gE7A6Lt7YLnNOL3Pb9BNeZvi+d8l7tcRrG4+pwJjK9hD2xX4mEvjlQW60G9EEmfXVYRPv9VRQcyegIVHCql/AA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-regexp-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/preset-env@7.21.4(@babel/core@7.21.4):
    resolution: {integrity: sha512-2W57zHs2yDLm6GD5ZpvNn71lZ0B/iypSdIeq25OurDKji6AdzV07qp4s3n1/x5BqtiGaTrPN3nerlSCaC5qNTw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.21.4
      '@babel/core': 7.21.4
      '@babel/helper-compilation-targets': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-validator-option': 7.21.0
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-proposal-async-generator-functions': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-proposal-class-properties': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-class-static-block': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-proposal-dynamic-import': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-export-namespace-from': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-proposal-json-strings': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-logical-assignment-operators': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-proposal-nullish-coalescing-operator': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-numeric-separator': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-object-rest-spread': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-proposal-optional-catch-binding': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-optional-chaining': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-proposal-private-methods': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.21.4)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.21.4)
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.21.4)
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-import-assertions': 7.20.0(@babel/core@7.21.4)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.21.4)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.21.4)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.21.4)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.21.4)
      '@babel/plugin-transform-arrow-functions': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-transform-async-to-generator': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-transform-block-scoped-functions': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-block-scoping': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-transform-classes': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-transform-computed-properties': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-transform-destructuring': 7.21.3(@babel/core@7.21.4)
      '@babel/plugin-transform-dotall-regex': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-duplicate-keys': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-transform-exponentiation-operator': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-for-of': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-transform-function-name': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-transform-literals': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-transform-member-expression-literals': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-modules-amd': 7.20.11(@babel/core@7.21.4)
      '@babel/plugin-transform-modules-commonjs': 7.21.2(@babel/core@7.21.4)
      '@babel/plugin-transform-modules-systemjs': 7.20.11(@babel/core@7.21.4)
      '@babel/plugin-transform-modules-umd': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.20.5(@babel/core@7.21.4)
      '@babel/plugin-transform-new-target': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-object-super': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-parameters': 7.21.3(@babel/core@7.21.4)
      '@babel/plugin-transform-property-literals': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-regenerator': 7.20.5(@babel/core@7.21.4)
      '@babel/plugin-transform-reserved-words': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-shorthand-properties': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-spread': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-transform-sticky-regex': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-template-literals': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-transform-typeof-symbol': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-transform-unicode-escapes': 7.18.10(@babel/core@7.21.4)
      '@babel/plugin-transform-unicode-regex': 7.18.6(@babel/core@7.21.4)
      '@babel/preset-modules': 0.1.5(@babel/core@7.21.4)
      '@babel/types': 7.21.4
      babel-plugin-polyfill-corejs2: 0.3.3(@babel/core@7.21.4)
      babel-plugin-polyfill-corejs3: 0.6.0(@babel/core@7.21.4)
      babel-plugin-polyfill-regenerator: 0.4.1(@babel/core@7.21.4)
      core-js-compat: 3.30.1
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/preset-modules@0.1.5(@babel/core@7.21.4):
    resolution: {integrity: sha1-75Odbn8miCfhhBY43G/5VRXhFdk=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-dotall-regex': 7.18.6(@babel/core@7.21.4)
      '@babel/types': 7.21.4
      esutils: 2.0.3
    dev: true

  /@babel/regjsgen@0.8.0:
    resolution: {integrity: sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA==}
    dev: true

  /@babel/runtime@7.21.0:
    resolution: {integrity: sha512-xwII0//EObnq89Ji5AKYQaRYiW/nZ3llSv29d49IuxPhKbtJoLP+9QUUZ4nVragQVtaVGeZrpB+ZtG/Pdy/POw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.13.11

  /@babel/template@7.20.7:
    resolution: {integrity: sha512-8SegXApWe6VoNw0r9JHpSteLKTpTiLZ4rMlGIm9JQ18KiCtyQiAMEazujAHrUS5flrcqYZa75ukev3P6QmUwUw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.21.4
      '@babel/parser': 7.23.6
      '@babel/types': 7.21.4
    dev: true

  /@babel/traverse@7.21.4:
    resolution: {integrity: sha512-eyKrRHKdyZxqDm+fV1iqL9UAHMoIg0nDaGqfIOd8rKH17m5snv7Gn4qgjBoFfLz9APvjFU/ICT00NVCv1Epp8Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.21.4
      '@babel/generator': 7.21.4
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.21.0
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/parser': 7.23.6
      '@babel/types': 7.21.4
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/types@7.21.4:
    resolution: {integrity: sha512-rU2oY501qDxE8Pyo7i/Orqma4ziCOrby0/9mvbDUGEfvZjb279Nk9k19e2fiCxHbRRpY2ZyrgW1eq22mvmOIzA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.19.4
      '@babel/helper-validator-identifier': 7.19.1
      to-fast-properties: 2.0.0

  /@ctrl/tinycolor@3.6.0:
    resolution: {integrity: sha512-/Z3l6pXthq0JvMYdUFyX9j0MaCltlIn6mfh9jLyQwg5aPKxkyNa0PTHtU1AlFXLNk55ZuAeJRcpvq+tmLfKmaQ==}
    engines: {node: '>=10'}
    dev: false

  /@ecp/ecp-login-component-vite@3.0.0(@element-plus/icons-vue@2.3.1)(element-plus@2.8.0)(vue@3.4.15):
    resolution: {integrity: sha512-OfUR9pCdp0x2dacJGL68QjqxCSVqTjbdNs49+VY3dHe9wQT1qVdowhXVbBsWZhp0/JoQ2mDae+gw9RtHaRPo9g==}
    peerDependencies:
      '@element-plus/icons-vue': ^2.3.1
      element-plus: ^2.4.2
      vue: ^3.3.2
    dependencies:
      '@element-plus/icons-vue': 2.3.1(vue@3.4.15)
      axios: 0.20.0
      crypto-js: 4.2.0
      element-plus: 2.8.0(vue@3.4.15)
      vue: 3.4.15
    transitivePeerDependencies:
      - debug
    dev: false

  /@ecp/ecp-ui-plus@1.0.2(element-plus@2.8.0)(vue@3.4.15):
    resolution: {integrity: sha512-tDp0Cax9Z1s55N2fzvRD2WQJTH1J8DOCoDMHOvFNtEWLOB1H/T7wKwxGOaHCPgYhAYkFzCZ7iQaNVETu3U63bQ==}
    peerDependencies:
      element-plus: ^2.7.8
      vue: ^3.3.2
    dependencies:
      '@ctrl/tinycolor': 3.6.0
      '@element-plus/icons-vue': 2.3.1(vue@3.4.15)
      '@types/lodash': 4.14.194
      '@types/lodash-es': 4.17.7
      cropperjs: 1.5.12
      d3: 5.9.7
      dayjs: 1.11.7
      element-plus: 2.8.0(vue@3.4.15)
      lodash: 4.17.21
      lodash-es: 4.17.21
      lodash-unified: 1.0.3(@types/lodash-es@4.17.7)(lodash-es@4.17.21)(lodash@4.17.21)
      resize-observer-polyfill: 1.5.1
      rxjs: 7.8.1
      sortablejs: 1.15.2
      style-inject: 0.3.0
      vue: 3.4.15
      wujie: 1.0.22
      wujie-polyfill: 1.0.11
    dev: false

  /@ecp/version-vite-plugin@0.1.5:
    resolution: {integrity: sha512-z/Ckr7N03D0vC/hEy3jY1nttModYIy0KU4EAOV8UpEG1zAmDetxHXSh6untc7bnrb5le+ocVVhSwkadsvBbkPA==}
    requiresBuild: true
    dependencies:
      vue: 3.4.15
    transitivePeerDependencies:
      - typescript
    dev: true

  /@element-plus/icons-vue@2.3.1(vue@3.4.15):
    resolution: {integrity: sha512-XxVUZv48RZAd87ucGS48jPf6pKu0yV5UCg9f4FFwtrYxXOwWuVJo6wOvSLKEoMQKjv8GsX/mhP6UsC1lRwbUWg==}
    peerDependencies:
      vue: ^3.2.0
    dependencies:
      vue: 3.4.15
    dev: false

  /@esbuild/android-arm64@0.18.20:
    resolution: {integrity: sha512-Nz4rJcchGDtENV0eMKUNa6L12zz2zBDXuhj/Vjh18zGqB44Bi7MBMSXjgunJgjRhCmKOjnPuZp4Mb6OKqtMHLQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-arm@0.18.20:
    resolution: {integrity: sha512-fyi7TDI/ijKKNZTUJAQqiG5T7YjJXgnzkURqmGj13C6dCqckZBLdl4h7bkhHt/t0WP+zO9/zwroDvANaOqO5Sw==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-x64@0.18.20:
    resolution: {integrity: sha512-8GDdlePJA8D6zlZYJV/jnrRAi6rOiNaCC/JclcXpB+KIuvfBN4owLtgzY2bsxnx666XjJx2kDPUmnTtR8qKQUg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-arm64@0.18.20:
    resolution: {integrity: sha512-bxRHW5kHU38zS2lPTPOyuyTm+S+eobPUnTNkdJEfAddYgEcll4xkT8DB9d2008DtTbl7uJag2HuE5NZAZgnNEA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-x64@0.18.20:
    resolution: {integrity: sha512-pc5gxlMDxzm513qPGbCbDukOdsGtKhfxD1zJKXjCCcU7ju50O7MeAZ8c4krSJcOIJGFR+qx21yMMVYwiQvyTyQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-arm64@0.18.20:
    resolution: {integrity: sha512-yqDQHy4QHevpMAaxhhIwYPMv1NECwOvIpGCZkECn8w2WFHXjEwrBn3CeNIYsibZ/iZEUemj++M26W3cNR5h+Tw==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-x64@0.18.20:
    resolution: {integrity: sha512-tgWRPPuQsd3RmBZwarGVHZQvtzfEBOreNuxEMKFcd5DaDn2PbBxfwLcj4+aenoh7ctXcbXmOQIn8HI6mCSw5MQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm64@0.18.20:
    resolution: {integrity: sha512-2YbscF+UL7SQAVIpnWvYwM+3LskyDmPhe31pE7/aoTMFKKzIc9lLbyGUpmmb8a8AixOL61sQ/mFh3jEjHYFvdA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm@0.18.20:
    resolution: {integrity: sha512-/5bHkMWnq1EgKr1V+Ybz3s1hWXok7mDFUMQ4cG10AfW3wL02PSZi5kFpYKrptDsgb2WAJIvRcDm+qIvXf/apvg==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ia32@0.18.20:
    resolution: {integrity: sha512-P4etWwq6IsReT0E1KHU40bOnzMHoH73aXp96Fs8TIT6z9Hu8G6+0SHSw9i2isWrD2nbx2qo5yUqACgdfVGx7TA==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-loong64@0.18.20:
    resolution: {integrity: sha512-nXW8nqBTrOpDLPgPY9uV+/1DjxoQ7DoB2N8eocyq8I9XuqJ7BiAMDMf9n1xZM9TgW0J8zrquIb/A7s3BJv7rjg==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-mips64el@0.18.20:
    resolution: {integrity: sha512-d5NeaXZcHp8PzYy5VnXV3VSd2D328Zb+9dEq5HE6bw6+N86JVPExrA6O68OPwobntbNJ0pzCpUFZTo3w0GyetQ==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ppc64@0.18.20:
    resolution: {integrity: sha512-WHPyeScRNcmANnLQkq6AfyXRFr5D6N2sKgkFo2FqguP44Nw2eyDlbTdZwd9GYk98DZG9QItIiTlFLHJHjxP3FA==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-riscv64@0.18.20:
    resolution: {integrity: sha512-WSxo6h5ecI5XH34KC7w5veNnKkju3zBRLEQNY7mv5mtBmrP/MjNBCAlsM2u5hDBlS3NGcTQpoBvRzqBcRtpq1A==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-s390x@0.18.20:
    resolution: {integrity: sha512-+8231GMs3mAEth6Ja1iK0a1sQ3ohfcpzpRLH8uuc5/KVDFneH6jtAJLFGafpzpMRO6DzJ6AvXKze9LfFMrIHVQ==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-x64@0.18.20:
    resolution: {integrity: sha512-UYqiqemphJcNsFEskc73jQ7B9jgwjWrSayxawS6UVFZGWrAAtkzjxSqnoclCXxWtfwLdzU+vTpcNYhpn43uP1w==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/netbsd-x64@0.18.20:
    resolution: {integrity: sha512-iO1c++VP6xUBUmltHZoMtCUdPlnPGdBom6IrO4gyKPFFVBKioIImVooR5I83nTew5UOYrk3gIJhbZh8X44y06A==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/openbsd-x64@0.18.20:
    resolution: {integrity: sha512-e5e4YSsuQfX4cxcygw/UCPIEP6wbIL+se3sxPdCiMbFLBWu0eiZOJ7WoD+ptCLrmjZBK1Wk7I6D/I3NglUGOxg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/sunos-x64@0.18.20:
    resolution: {integrity: sha512-kDbFRFp0YpTQVVrqUd5FTYmWo45zGaXe0X8E1G/LKFC0v8x0vWrhOWSLITcCn63lmZIxfOMXtCfti/RxN/0wnQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-arm64@0.18.20:
    resolution: {integrity: sha512-ddYFR6ItYgoaq4v4JmQQaAI5s7npztfV4Ag6NrhiaW0RrnOXqBkgwZLofVTlq1daVTQNhtI5oieTvkRPfZrePg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-ia32@0.18.20:
    resolution: {integrity: sha512-Wv7QBi3ID/rROT08SABTS7eV4hX26sVduqDOTe1MvGMjNd3EjOz4b7zeexIR62GTIEKrfJXKL9LFxTYgkyeu7g==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-x64@0.18.20:
    resolution: {integrity: sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@eslint-community/eslint-utils@4.4.0(eslint@8.38.0):
    resolution: {integrity: sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
    dependencies:
      eslint: 8.38.0
      eslint-visitor-keys: 3.4.0
    dev: true

  /@eslint-community/regexpp@4.5.0:
    resolution: {integrity: sha512-vITaYzIcNmjn5tF5uxcZ/ft7/RXGrMUIS9HalWckEOF6ESiwXKoMzAQf2UW0aVd6rnOeExTJVd5hmWXucBKGXQ==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}
    dev: true

  /@eslint/eslintrc@2.0.2:
    resolution: {integrity: sha512-3W4f5tDUra+pA+FzgugqL2pRimUTDJWKr7BINqOpkZrC0uYI0NIc0/JFgBROCU07HR6GieA5m3/rsPIhDmCXTQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      ajv: 6.12.6
      debug: 4.3.4
      espree: 9.5.1
      globals: 13.20.0
      ignore: 5.2.4
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@eslint/js@8.38.0:
    resolution: {integrity: sha512-IoD2MfUnOV58ghIHCiil01PcohxjbYR/qCxsoC+xNgUwh1EY8jOOrYmu3d3a71+tJJ23uscEV4X2HJWMsPJu4g==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /@esri/arcgis-to-geojson-utils@1.3.0:
    resolution: {integrity: sha1-eatlvz5AwDnb3rzv9LOw7AiLGJw=}
    dev: false

  /@floating-ui/core@1.2.6:
    resolution: {integrity: sha512-EvYTiXet5XqweYGClEmpu3BoxmsQ4hkj3QaYA6qEnigCWffTP3vNRwBReTdrwDwo7OoJ3wM8Uoe9Uk4n+d4hfg==}
    dev: false

  /@floating-ui/dom@1.2.6:
    resolution: {integrity: sha512-02vxFDuvuVPs22iJICacezYJyf7zwwOCWkPNkWNBr1U0Qt1cKFYzWvxts0AmqcOQGwt/3KJWcWIgtbUU38keyw==}
    dependencies:
      '@floating-ui/core': 1.2.6
    dev: false

  /@geoman-io/leaflet-geoman-free@2.17.0(leaflet@1.9.4):
    resolution: {integrity: sha512-vAY9tKB2I/Ui8d3QUBuebWnunI2sGjsfAUTXMMcf5UpISvPz67io4hpbKXid9GNsW6P4LGv1+ZzrmkpM78GzHA==}
    peerDependencies:
      leaflet: ^1.2.0
    dependencies:
      '@turf/boolean-contains': 6.5.0
      '@turf/kinks': 6.5.0
      '@turf/line-intersect': 6.5.0
      '@turf/line-split': 6.5.0
      leaflet: 1.9.4
      lodash: 4.17.21
      polyclip-ts: 0.16.5
    dev: false

  /@humanwhocodes/config-array@0.11.8:
    resolution: {integrity: sha512-UybHIJzJnR5Qc/MsD9Kr+RpO2h+/P1GhOwdiLPXK5TWk5sgTdu88bTD9UP+CKbPPh5Rni1u0GjAdYQLemG8g+g==}
    engines: {node: '>=10.10.0'}
    dependencies:
      '@humanwhocodes/object-schema': 1.2.1
      debug: 4.3.4
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@humanwhocodes/module-importer@1.0.1:
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}
    dev: true

  /@humanwhocodes/object-schema@1.2.1:
    resolution: {integrity: sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U=}
    dev: true

  /@iconify/types@2.0.0:
    resolution: {integrity: sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg==}
    dev: true

  /@iconify/utils@2.1.5:
    resolution: {integrity: sha512-6MvDI+I6QMvXn5rK9KQGdpEE4mmLTcuQdLZEiX5N+uZB+vc4Yw9K1OtnOgkl8mp4d9X0UrILREyZgF1NUwUt+Q==}
    dependencies:
      '@antfu/install-pkg': 0.1.1
      '@antfu/utils': 0.7.2
      '@iconify/types': 2.0.0
      debug: 4.3.4
      kolorist: 1.7.0
      local-pkg: 0.4.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@jridgewell/gen-mapping@0.3.5:
    resolution: {integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.25
    dev: true

  /@jridgewell/resolve-uri@3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}
    dev: true

  /@jridgewell/set-array@1.2.1:
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}
    dev: true

  /@jridgewell/source-map@0.3.6:
    resolution: {integrity: sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
    dev: true

  /@jridgewell/sourcemap-codec@1.4.15:
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}

  /@jridgewell/trace-mapping@0.3.25:
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.4.15
    dev: true

  /@nodelib/fs.scandir@2.1.5:
    resolution: {integrity: sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: true

  /@nodelib/fs.stat@2.0.5:
    resolution: {integrity: sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=}
    engines: {node: '>= 8'}
    dev: true

  /@nodelib/fs.walk@1.2.8:
    resolution: {integrity: sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.15.0
    dev: true

  /@popperjs/core@2.11.8:
    resolution: {integrity: sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==}
    dev: false

  /@rollup/pluginutils@5.0.2:
    resolution: {integrity: sha512-pTd9rIsP92h+B6wWwFbW8RkZv4hiR/xKsqre4SIuAOaOEQRxi0lqLke9k2/7WegC85GgUs9pjmOjCUi3In4vwA==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0
    peerDependenciesMeta:
      rollup:
        optional: true
    dependencies:
      '@types/estree': 1.0.1
      estree-walker: 2.0.2
      picomatch: 2.3.1
    dev: true

  /@sxzz/popperjs-es@2.11.7:
    resolution: {integrity: sha512-Ccy0NlLkzr0Ex2FKvh2X+OyERHXJ88XJ1MXtsI9y9fGexlaXaVTPzBCRBwIxFkORuOb+uBqeu+RqnpgYTEZRUQ==}
    dev: false

  /@trysound/sax@0.2.0:
    resolution: {integrity: sha1-zMqrdYr1Z2Hre/N69vA/Mm3XmK0=}
    engines: {node: '>=10.13.0'}
    dev: true

  /@turf/along@7.1.0:
    resolution: {integrity: sha512-WLgBZJ/B6CcASF6WL7M+COtHlVP0hBrMbrtKyF7KBlicwRuijJZXDtEQA5oLgr+k1b2HqGN+UqH2A0/E719enQ==}
    dependencies:
      '@turf/bearing': 7.1.0
      '@turf/destination': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/angle@7.1.0:
    resolution: {integrity: sha512-YMHEV/YrARsWgWoQuXEWrQMsvB8z67nTMw2eiLZ883V7jwkhWQGvCW6W+/mGgsWQdHppjCZNcKryryhD2GRWVA==}
    dependencies:
      '@turf/bearing': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/rhumb-bearing': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/area@7.1.0:
    resolution: {integrity: sha512-w91FEe02/mQfMPRX2pXua48scFuKJ2dSVMF2XmJ6+BJfFiCPxp95I3+Org8+ZsYv93CDNKbf0oLNEPnuQdgs2g==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/bbox-clip@7.1.0:
    resolution: {integrity: sha512-PhZubKCzF/afwStUzODqOJluiCbCw244lCtVhXA9F+Pgkhvk8KvbFdgpPquOZ45OwuktrchSB28BrBkSBiadHw==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/bbox-polygon@7.1.0:
    resolution: {integrity: sha512-fvZB09ErCZOVlWVDop836hmpKaGUmfXnR9naMhS73A/8nn4M3hELbQtMv2R8gXj7UakXCuxS/i9erdpDFZ2O+g==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/bbox@6.5.0:
    resolution: {integrity: sha1-vsMKdEAZ6uQg2snqRvt1yqRNjcU=}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
    dev: false

  /@turf/bbox@7.1.0:
    resolution: {integrity: sha512-PdWPz9tW86PD78vSZj2fiRaB8JhUHy6piSa/QXb83lucxPK+HTAdzlDQMTKj5okRCU8Ox/25IR2ep9T8NdopRA==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/bearing@6.5.0:
    resolution: {integrity: sha1-RioFPGxkRDS9tjazn49D+wzYV7A=}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
    dev: false

  /@turf/bearing@7.1.0:
    resolution: {integrity: sha512-X5lackrZ6FW+YhgjWxwVFRgWD1j4xm4t5VvE6EE6v/1PVaHQ5OCjf6u1oaLx5LSG+gaHUhjTlAHrn9MYPFaeTA==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/bezier-spline@7.1.0:
    resolution: {integrity: sha512-bhBY70bcVYJEosuW7B/TFtnE5rmPTTpxmJvljhGC0eyM84oNVv7apDBuseb5KdlTOOBIvdD9nIE4qV8lmplp6w==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/boolean-clockwise@7.1.0:
    resolution: {integrity: sha512-H5DYno+gHwZx+VaiC8DUBZXZQlxYecdSvqCfCACWi1uMsKvlht/O+xy65hz2P57lk2smlcV+1ETFVxJlEZduYg==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/boolean-concave@7.1.0:
    resolution: {integrity: sha512-IFCN25DI+hvngxIsv4+MPuRJQRl/Lz/xnZgpH82leCn4Jqn5wW7KqKFMz7G4GoKK+93cK5/6ioAxY7hVWBXxJw==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/boolean-contains@6.5.0:
    resolution: {integrity: sha1-+ALnQy+1MQkkLVv1c5PvL1OEm78=}
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/boolean-point-on-line': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
    dev: false

  /@turf/boolean-contains@7.1.0:
    resolution: {integrity: sha512-ldy4j1/RVChYTYjEb4wWaE/JyF1jA87WpsB4eVLic6OcAYJGs7POF1kfKbcdkJJiRBmhI3CXNA+u+m9y4Z/j3g==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/boolean-point-on-line': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/boolean-crosses@7.1.0:
    resolution: {integrity: sha512-LK8UM3AENycuGinLCDaL0QSznGMnD0XsjFDGnY4KehshiL5Zd8ZsPyKmHOPygUJT9DWeH69iLx459lOc+5Vj2w==}
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@turf/polygon-to-line': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/boolean-disjoint@7.1.0:
    resolution: {integrity: sha512-JapOG03kOCoGeYMWgTQjEifhr1nUoK4Os2cX0iC5X9kvZF4qCHeruX8/rffBQDx7PDKQKusSTXq8B1ISFi0hOw==}
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/polygon-to-line': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/boolean-equal@7.1.0:
    resolution: {integrity: sha512-deghtFMApc7fNsdXtZdgYR4gsU+TVfowcv666nrvZbPPsXL6NTYGBhDFmYXsJ8gPTCGT9uT0WXppdgT8diWOxA==}
    dependencies:
      '@turf/clean-coords': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      geojson-equality-ts: 1.0.2
      tslib: 2.6.2
    dev: false

  /@turf/boolean-intersects@7.1.0:
    resolution: {integrity: sha512-gpksWbb0RT+Z3nfqRfoACY3KEFyv2BPaxJ3L76PH67DhHZviq3Nfg85KYbpuhS64FSm+9tXe4IaKn6EjbHo20g==}
    dependencies:
      '@turf/boolean-disjoint': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/boolean-overlap@7.1.0:
    resolution: {integrity: sha512-mJRN0X8JiPm8eDZk5sLvIrsP03A2GId6ijx4VgSE1AvHwV6qB561KlUbWxga2AScocIfv/y/qd2OCs+/TQSZcg==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@turf/line-overlap': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      geojson-equality-ts: 1.0.2
      tslib: 2.6.2
    dev: false

  /@turf/boolean-parallel@7.1.0:
    resolution: {integrity: sha512-tA84Oux0X91CxUc6c/lZph5W9wUZGNT4fxFOg5Gp1IMTSwtxSYL1LMvKsr/VmMnwdOUkNcqAgU06+t4wBLtDfg==}
    dependencies:
      '@turf/clean-coords': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/line-segment': 7.1.0
      '@turf/rhumb-bearing': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/boolean-point-in-polygon@6.5.0:
    resolution: {integrity: sha1-bS6cid5M0uQ2UATB5RSQt3laY88=}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
    dev: false

  /@turf/boolean-point-in-polygon@7.1.0:
    resolution: {integrity: sha512-mprVsyIQ+ijWTZwbnO4Jhxu94ZW2M2CheqLiRTsGJy0Ooay9v6Av5/Nl3/Gst7ZVXxPqMeMaFYkSzcTc87AKew==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      point-in-polygon-hao: 1.1.0
      tslib: 2.6.2
    dev: false

  /@turf/boolean-point-on-line@6.5.0:
    resolution: {integrity: sha1-qO+nutiHYGdvOVr7mYB0a8Wzduk=}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
    dev: false

  /@turf/boolean-point-on-line@7.1.0:
    resolution: {integrity: sha512-Kd83EjeTyY4kVMAhcW3Lb8aChwh24BUIhmpE9Or8M+ETNsFGzn9M7qtIySJHLRzKAL3letvWSKXKQPuK1AhAzg==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/boolean-touches@7.1.0:
    resolution: {integrity: sha512-qN4LCs3RfVtNAAdn5GpsUFBqoZyAaK9UzSnGSh67GP9sy5M8MEHwM/HAJ5zGWJqQADrczI3U6BRWGLcGfGSz3Q==}
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/boolean-point-on-line': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/boolean-valid@7.1.0:
    resolution: {integrity: sha512-zq1QCfQEyn+piHlvxxDifjmsJn2xl53i4mnKFYdMQI/i09XiX+Fi/MVM3i2hf3D5AsEPsud8Tk7C7rWNCm4nVw==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/boolean-crosses': 7.1.0
      '@turf/boolean-disjoint': 7.1.0
      '@turf/boolean-overlap': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/boolean-point-on-line': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@types/geojson': 7946.0.14
      geojson-polygon-self-intersections: 1.2.1
      tslib: 2.6.2
    dev: false

  /@turf/boolean-within@7.1.0:
    resolution: {integrity: sha512-pgXgKCzYHssADQ1nClB1Q9aWI/dE1elm2jy3B5X59XdoFXKrKDZA+gCHYOYgp2NGO/txzVfl3UKvnxIj54Fa4w==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/boolean-point-on-line': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/buffer@7.1.0:
    resolution: {integrity: sha512-QM3JiCMYA19k5ouO8wJtvICX3Y8XntxVpDfHSKhFFidZcCkMTR2PWWOpwS6EoL3t75rSKw/FOLIPLZGtIu963w==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/center': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/jsts': 2.7.1
      '@turf/meta': 7.1.0
      '@turf/projection': 7.1.0
      '@types/geojson': 7946.0.14
      d3-geo: 1.7.1
    dev: false

  /@turf/center-mean@7.1.0:
    resolution: {integrity: sha512-NQZB1LUVsyAD+p0+D4huzX2XVnfVx1yEEI9EX602THmi+g+nkge4SK9OMV11ov/Tv8JJ6aVNVPo/cy1vm/LCIQ==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/center-median@7.1.0:
    resolution: {integrity: sha512-jx4/Ql5+v41Cd0J/gseNCUbLTzWUT2LUaiXn8eFWDrvmEgqHIx7KJcGcJd5HzV+9zJwng4AXxyh5NMvUR0NjwA==}
    dependencies:
      '@turf/center-mean': 7.1.0
      '@turf/centroid': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/center-of-mass@7.1.0:
    resolution: {integrity: sha512-j38oBlj7LBoCjZbrIo8EoHVGhk7UQmMLQ1fe8ZPAF9pd05XEL1qxyHKZKdQ/deGISiaEhXCyfLNrKAHAuy25RA==}
    dependencies:
      '@turf/centroid': 7.1.0
      '@turf/convex': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/center@7.1.0:
    resolution: {integrity: sha512-p9AvBMwNZmRg65kU27cGKHAUQnEcdz8Y7f/i5DvaMfm4e8zmawr+hzPKXaUpUfiTyLs8Xt2W9vlOmNGyH+6X3w==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/centroid@7.1.0:
    resolution: {integrity: sha512-1Y1b2l+ZB1CZ+ITjUCsGqC4/tSjwm/R4OUfDztVqyyCq/VvezkLmTNqvXTGXgfP0GXkpv68iCfxF5M7QdM5pJQ==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/circle@7.1.0:
    resolution: {integrity: sha512-6qhF1drjwH0Dg3ZB9om1JkWTJfAqBcbtIrAj5UPlrAeHP87hGoCO2ZEsFEAL9Q18vntpivT89Uho/nqQUjJhYw==}
    dependencies:
      '@turf/destination': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/clean-coords@7.1.0:
    resolution: {integrity: sha512-q1U8UbRVL5cRdwOlNjD8mad8pWjFGe0s4ihg1pSiVNq7i47WASJ3k20yZiUFvuAkyNjV0rZ/A7Jd7WzjcierFg==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/clone@7.1.0:
    resolution: {integrity: sha512-5R9qeWvL7FDdBIbEemd0eCzOStr09oburDvJ1hRiPCFX6rPgzcZBQ0gDmZzoF4AFcNLb5IwknbLZjVLaUGWtFA==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/clusters-dbscan@7.1.0:
    resolution: {integrity: sha512-BmrBTOEaKN5FIED6b3yb3V3ejfK0A2Q3pT9/ji3mcRLJiBaRGeiN5V6gtGXe7PeMYdoqhHykU5Ye2uUtREWRdQ==}
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      rbush: 3.0.1
      tslib: 2.6.2
    dev: false

  /@turf/clusters-kmeans@7.1.0:
    resolution: {integrity: sha512-M8cCqR6iE1jDSUF/UU9QdPUFrobZS2fo59TfF1IRHZ2G1EjbcK4GzZcUfmQS6DZraGudYutpMYIuNdm1dPMqdQ==}
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      skmeans: 0.9.7
      tslib: 2.6.2
    dev: false

  /@turf/clusters@7.1.0:
    resolution: {integrity: sha512-7CY3Ai+5V6q2O9/IgqLpJQrmrTy7aUJjTW1iRan8Tz3WixvxyJHeS3iyRy8Oc0046chQIaHLtyTgKVt2QdsPSA==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/collect@7.1.0:
    resolution: {integrity: sha512-6indMWLiKeBh4AsioNeFeFnO0k9U5CBsWAFEje6tOEFI4c+P7LF9mNA9z91H8KkrhegR9XNO5Vm2rmdY63aYXw==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      rbush: 3.0.1
      tslib: 2.6.2
    dev: false

  /@turf/combine@7.1.0:
    resolution: {integrity: sha512-Xl7bGKKjgzIq2T/IemS6qnIykyuxU6cMxKtz+qLeWJGoNww/BllwxXePSV+dWRPXZTFFj96KIhBXAW0aUjAQKQ==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/concave@7.1.0:
    resolution: {integrity: sha512-aSid53gYRee4Tjc4pfeI3KI+RoBUnL/hRMilxIPduagTgZZS+cvvk01OQWBKm5UTVfHRGuy0XIqnK8y9RFinDQ==}
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/tin': 7.1.0
      '@types/geojson': 7946.0.14
      topojson-client: 3.1.0
      topojson-server: 3.0.1
      tslib: 2.6.2
    dev: false

  /@turf/convex@7.1.0:
    resolution: {integrity: sha512-w9fUMZYE36bLrEWEj7L7aVMCB7NBtr2o8G+avRvUIwF4DPqbtcjlcZE9EEBfq44uYdn+/Pke6Iq42T/zyD/cpg==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      concaveman: 1.2.1
      tslib: 2.6.2
    dev: false

  /@turf/destination@6.5.0:
    resolution: {integrity: sha1-MKhHAvlnfQdhMOBEDTIjrlA/2uE=}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
    dev: false

  /@turf/destination@7.1.0:
    resolution: {integrity: sha512-97XuvB0iaAiMg86hrnZ529WwP44TQAA9mmI5PMlchACiA4LFrEtWjjDzvO6234coieoqhrw6dZYcJvd5O2PwrQ==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/difference@7.1.0:
    resolution: {integrity: sha512-+JVzdskICQ8ULKQ9CpWUM5kBvoXxN4CO78Ez/Ki3/7NXl7+HM/nb12B0OyM8hkJchpb8TsOi0YwyJiKMqEpTBA==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      polygon-clipping: 0.15.7
      tslib: 2.6.2
    dev: false

  /@turf/dissolve@7.1.0:
    resolution: {integrity: sha512-fyOnCSYVUZ8SF9kt9ROnQYlkJTE0hpWSoWwbMZQCAR7oVZVPiuPq7eIbzTP+k5jzEAnofsqoGs5qVDTjHcWMiw==}
    dependencies:
      '@turf/flatten': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      polygon-clipping: 0.15.7
      tslib: 2.6.2
    dev: false

  /@turf/distance-weight@7.1.0:
    resolution: {integrity: sha512-8m6s4y8Yyt6r3itf44yAJjXC+62UkrkhOpskIfaE0lHcBcvZz9wjboHoBf3bS4l/42E4StcanbFZdjOpODAdZw==}
    dependencies:
      '@turf/centroid': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/distance@6.5.0:
    resolution: {integrity: sha1-IfBNX4boZNVOKr3hbzXBW082FJo=}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
    dev: false

  /@turf/distance@7.1.0:
    resolution: {integrity: sha512-hhNHhxCHB3ddzAGCNY4BtE29OZh+DAJPvUapQz+wOjISnlwvMcwLKvslgHWSYF536QDVe/93FEU2q67+CsZTPA==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/ellipse@7.1.0:
    resolution: {integrity: sha512-AfOahUmStDExWGPg8ZWxxkgom+fdJs7Mn9DzZH+fV/uZ+je1bLQpbPCUu9/ev6u/HhbYGl4VAL/CeQzjOyy6LQ==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/rhumb-destination': 7.1.0
      '@turf/transform-rotate': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/envelope@7.1.0:
    resolution: {integrity: sha512-WeLQse9wuxsxhzSqrJA6Ha7rLWnLKgdKY9cfxmJKHSpgqcJyNk60m7+T3UpI/nkGwpfbpeyB3EGC1EWPbxiDUg==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/bbox-polygon': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/explode@7.1.0:
    resolution: {integrity: sha512-To+GUbU6HtcHZ8S0w/dw1EbdQIOCXALTr6Ug5/IFg8hIBMJelDpVr3Smwy8uqhDRFinY2eprBwQnDPcd10eCqA==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/flatten@7.1.0:
    resolution: {integrity: sha512-Kb23pqEarcLsdBqnQcK0qTrSMiWNTVb9tOFrNlZc66DIhDLAdpOKG4eqk00CMoUzWTixlnawDgJRqcStRrR4WA==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/flip@7.1.0:
    resolution: {integrity: sha512-vac73W8WblzzNFanzWYLBzWDIcqc5xczOrtEO07RDEiKEI3Heo0471Jed3v9W506uuOX6/HAiCjXbRjTLjiLfw==}
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/geojson-rbush@7.1.0:
    resolution: {integrity: sha512-j1C7Ohlxa1z644bNOpgibcFGaDLgLXGLOzwF1tfQaP5y7E4PJQUXL0DWIgNb3Ke7gZC05LPHM25a5TRReUfFBQ==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      rbush: 3.0.1
    dev: false

  /@turf/great-circle@7.1.0:
    resolution: {integrity: sha512-92q5fqUp5oW+FYekUIrUVR5PZBWbOV6NHKHPIiNahiPvtkpZItbbjoO+tGn5+2i8mxZP9FGOthayJe4V0a1xkg==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
    dev: false

  /@turf/helpers@6.5.0:
    resolution: {integrity: sha1-95rwlL1rjOftK9PgiahJPubK6C4=}
    dev: false

  /@turf/helpers@7.1.0:
    resolution: {integrity: sha512-dTeILEUVeNbaEeoZUOhxH5auv7WWlOShbx7QSd4s0T4Z0/iz90z9yaVCtZOLbU89umKotwKaJQltBNO9CzVgaQ==}
    dependencies:
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/hex-grid@7.1.0:
    resolution: {integrity: sha512-I+Apx0smOPkMzaS5HHL44YOxSkSUvrz+wtSIETsDFWWLT2xKNkaaEcYU5MkgSoEfQsj082M7EkOIIpocXlA3kg==}
    dependencies:
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/intersect': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/interpolate@7.1.0:
    resolution: {integrity: sha512-VWec1OW9gHZLPS3yYkUXAHKMGQuYO4aqh8WCltT7Ym4efrKqkSOE5T+mBqO68QgcL8nY4kiNa8lxwXd0SfXDSA==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/centroid': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/hex-grid': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/point-grid': 7.1.0
      '@turf/square-grid': 7.1.0
      '@turf/triangle-grid': 7.1.0
      '@types/geojson': 7946.0.14
    dev: false

  /@turf/intersect@7.1.0:
    resolution: {integrity: sha512-T0VhI6yhptX9EoMsuuBETyqV+edyq31SUC8bfuM6kdJ5WwJ0EvUfQoC+3bhMtCOn60lHawrUuGBgW+vCO8KGMg==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      polygon-clipping: 0.15.7
      tslib: 2.6.2
    dev: false

  /@turf/invariant@6.5.0:
    resolution: {integrity: sha1-lwr8mIAj45x8yrI0G9Bped3HRj8=}
    dependencies:
      '@turf/helpers': 6.5.0
    dev: false

  /@turf/invariant@7.1.0:
    resolution: {integrity: sha512-OCLNqkItBYIP1nE9lJGuIUatWGtQ4rhBKAyTfFu0z8npVzGEYzvguEeof8/6LkKmTTEHW53tCjoEhSSzdRh08Q==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/isobands@7.1.0:
    resolution: {integrity: sha512-iMLTOP/K5C05AttF4N1WeV+KrY4O5VWW/abO0N86XCWh1OeqmIUgqIBKEmhDzttAqC0UK2YrUfj0lI1Ez1fYZQ==}
    dependencies:
      '@turf/area': 7.1.0
      '@turf/bbox': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/explode': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      marchingsquares: 1.3.3
      tslib: 2.6.2
    dev: false

  /@turf/isolines@7.1.0:
    resolution: {integrity: sha512-V6QTHXBT5ZsL3s9ZVBJgHYtz3gCFKqNnQLysNE02LE0fVVqaSao3sFrcpghmdDxf0hBCDK8lZVvyRGO6o32LHQ==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      marchingsquares: 1.3.3
      tslib: 2.6.2
    dev: false

  /@turf/jsts@2.7.1:
    resolution: {integrity: sha512-+nwOKme/aUprsxnLSfr2LylV6eL6T1Tuln+4Hl92uwZ8FrmjDRCH5Bi1LJNVfWCiYgk8+5K+t2zDphWNTsIFDA==}
    dependencies:
      jsts: 2.7.1
    dev: false

  /@turf/kinks@6.5.0:
    resolution: {integrity: sha1-gOdFY2dTU2UBL2WM8amIs5oskgs=}
    dependencies:
      '@turf/helpers': 6.5.0
    dev: false

  /@turf/kinks@7.1.0:
    resolution: {integrity: sha512-KKLYUsyJPU17fODwA81mhHzFYGQYocdbk9NxDPCcdRHvxzM8t95lptkGx/2k/9rXBs1DK7NmyzI4m7zDO0DK7g==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/length@7.1.0:
    resolution: {integrity: sha512-wUJj9WLKEudG1ngNao2ZwD+Dt6UkvWIbubuJ6lR6FndFDL3iezFhNGy0IXS+0xH9kXi2apiTnM9Vk5+i8BTEvQ==}
    dependencies:
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/line-arc@7.1.0:
    resolution: {integrity: sha512-9/bM34PozTyJ5FXXPAzl/j0RpcTImgMFJZ0WhH0pZZEZRum6P0rJnENt2E2qI441zeozQ9H6X5DCiJogDmRUEw==}
    dependencies:
      '@turf/circle': 7.1.0
      '@turf/destination': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/line-chunk@7.1.0:
    resolution: {integrity: sha512-1lIUfqAQvCWAuUNC2ip8UYmM5kDltXOidLPW45Ee1OAIKYGBeFNtjwnxc0mQ40tnfTXclTYLDdOOP9LShspT9w==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/length': 7.1.0
      '@turf/line-slice-along': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
    dev: false

  /@turf/line-intersect@6.5.0:
    resolution: {integrity: sha1-3qSDSLMMCTcV0hldLddSSu5M8CA=}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/line-segment': 6.5.0
      '@turf/meta': 6.5.0
      geojson-rbush: 3.2.0
    dev: false

  /@turf/line-intersect@7.1.0:
    resolution: {integrity: sha512-JI3dvOsAoCqd4vUJ134FIzgcC42QpC/tBs+b4OJoxWmwDek3REv4qGaZY6wCg9X4hFSlCKFcnhMIQQZ/n720Qg==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      sweepline-intersections: 1.5.0
      tslib: 2.6.2
    dev: false

  /@turf/line-offset@7.1.0:
    resolution: {integrity: sha512-pz6irzhiQlJurU7DoXada6k3ei7PzY+VpsE/Wotm0D2KEAnoxqum2WK0rqqrhKPHKn+xpUGsHN9W/6K+qtmaHg==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
    dev: false

  /@turf/line-overlap@7.1.0:
    resolution: {integrity: sha512-BdHuEoFAtqvVw3LkjCdivG035nfuwZuxji2ijst+mkmDnlv7uwSBudJqcDGjU6up2r8P1mXChS4im4xjUz+lwg==}
    dependencies:
      '@turf/boolean-point-on-line': 7.1.0
      '@turf/geojson-rbush': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-segment': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/nearest-point-on-line': 7.1.0
      '@types/geojson': 7946.0.14
      fast-deep-equal: 3.1.3
      tslib: 2.6.2
    dev: false

  /@turf/line-segment@6.5.0:
    resolution: {integrity: sha1-7nPz/8t8lWIDtk7ZZtlq84Ck3WU=}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
    dev: false

  /@turf/line-segment@7.1.0:
    resolution: {integrity: sha512-9rgIIH6ZzC3IiWxDQtKsq+j6eu8fRinMkJeusfI9HqOTm4vO02Ll4F/FigjOMOO/6X3TJ+Pqe3gS99TUaBINkw==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/line-slice-along@7.1.0:
    resolution: {integrity: sha512-UwfnFORZnu4xdnuRXiQM3ODa8f9Q0FBjQF/XHNsPEI/xxmnwgQj3MZiULbAeHUbtU/7psTC7gEjfE3Lf0tcKQw==}
    dependencies:
      '@turf/bearing': 7.1.0
      '@turf/destination': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
    dev: false

  /@turf/line-slice@7.1.0:
    resolution: {integrity: sha512-44xcjgMQxTa7tTAZlSD3t1cFjHi5SCfAqjg1ONv45EYKsQSonPaxD7LGzCbU5pR2RJjx3R7QRJx2G88hnGcXjQ==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/nearest-point-on-line': 7.1.0
      '@types/geojson': 7946.0.14
    dev: false

  /@turf/line-split@6.5.0:
    resolution: {integrity: sha1-EW1/v3FEV4eCJRh/WCDvmNt7AsI=}
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/line-intersect': 6.5.0
      '@turf/line-segment': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/nearest-point-on-line': 6.5.0
      '@turf/square': 6.5.0
      '@turf/truncate': 6.5.0
      geojson-rbush: 3.2.0
    dev: false

  /@turf/line-split@7.1.0:
    resolution: {integrity: sha512-QqUAmtlrnEu75cpLOmpEuiYU63BeVwpSKOBllBbu5gkP+7H/WBM/9fh7J0VgHNFHzqZCKiu8v4158k+CZr0QAg==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/geojson-rbush': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@turf/line-segment': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/nearest-point-on-line': 7.1.0
      '@turf/square': 7.1.0
      '@turf/truncate': 7.1.0
      '@types/geojson': 7946.0.14
    dev: false

  /@turf/line-to-polygon@7.1.0:
    resolution: {integrity: sha512-n/IWBRbo+l4XDTz4sfQsQm5bU9xex8KrthK397jQasd7a9PiOKGon9Z1t/lddTJhND6ajVyJ3hl+eZMtpQaghQ==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/mask@7.1.0:
    resolution: {integrity: sha512-d+u3IIiRhe17TDfP/+UMn9qRlJYPJpK7sj6WorsssluGi0yIG/Z24uWpcLskWKSI8NNgkIbDrp+GIYkJi2t7SA==}
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      polygon-clipping: 0.15.7
      tslib: 2.6.2
    dev: false

  /@turf/meta@6.5.0:
    resolution: {integrity: sha1-tyXDZTyfQyEz6qBNNCH35R4EGMo=}
    dependencies:
      '@turf/helpers': 6.5.0
    dev: false

  /@turf/meta@7.1.0:
    resolution: {integrity: sha512-ZgGpWWiKz797Fe8lfRj7HKCkGR+nSJ/5aKXMyofCvLSc2PuYJs/qyyifDPWjASQQCzseJ7AlF2Pc/XQ/3XkkuA==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
    dev: false

  /@turf/midpoint@7.1.0:
    resolution: {integrity: sha512-uiUU9TwRZOCeiTUn8+7oE6MJUvclfq+n6KQ5VCMTZXiRUJjPu7nDLpBle1t2WSv7/w7O0kSQ4FfKXh0gHnkJOw==}
    dependencies:
      '@turf/bearing': 7.1.0
      '@turf/destination': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/moran-index@7.1.0:
    resolution: {integrity: sha512-xsvAr3IRF/C6PlRMoN/ANrRx6c3QFUJgBCIVfI7re+Lkdprrzgw1HZA48ZjP4F91xbhgA1scnRgQdHFi2vO2SA==}
    dependencies:
      '@turf/distance-weight': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/nearest-neighbor-analysis@7.1.0:
    resolution: {integrity: sha512-FAhT8/op3DuvqH0XFhv055JhYq/FC4aaIxEZ4hj8c7W6sYhUHAQgdRZ0tJ1RLe5/h+eXhCTbQ+DFfnfv3klu8g==}
    dependencies:
      '@turf/area': 7.1.0
      '@turf/bbox': 7.1.0
      '@turf/bbox-polygon': 7.1.0
      '@turf/centroid': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/nearest-point': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/nearest-point-on-line@6.5.0:
    resolution: {integrity: sha1-jhzSzcC1rK9MjYs7M7sAjTy5nns=}
    dependencies:
      '@turf/bearing': 6.5.0
      '@turf/destination': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/line-intersect': 6.5.0
      '@turf/meta': 6.5.0
    dev: false

  /@turf/nearest-point-on-line@7.1.0:
    resolution: {integrity: sha512-aTjAOm7ab0tl5JoxGYRx/J/IbRL1DY1ZCIYQDMEQjK5gOllhclgeBC0wDXDkEZFGaVftjw0W2RtE2I0jX7RG4A==}
    dependencies:
      '@turf/bearing': 7.1.0
      '@turf/destination': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/nearest-point-to-line@7.1.0:
    resolution: {integrity: sha512-rY2F/iY4S6U8H0hIoOI25xMWYEiKywxeTvTvn5GP8KCu+2oemfZROWa7n2+hQDRwO2/uaegrGEpxO7zlFarvzg==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/point-to-line-distance': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/nearest-point@7.1.0:
    resolution: {integrity: sha512-VyInmhqfVWp+jE7sCK95o46qc4tDjAgzbRfRjr+rTgfFS1Sndyy1PdwyNn6TjBFDxiM6e+mjMEeGPjb1smJlEg==}
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/planepoint@7.1.0:
    resolution: {integrity: sha512-hFORBkCd7Q0kNUzLqksT4XglLgTQF9tCjG+dbnZ1VehpZu+w+vlHdoW/mY7XCX3Kj1ObiyzVmXffmVYgwXwF6Q==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/point-grid@7.1.0:
    resolution: {integrity: sha512-ihuuUcWuCu4Z1+34UYCM5NGsU2DJaB4uE8cS3jDQoUqlc+8ii2ng8kcGEtTwVn0HdPsoKA7bgvSZcisJO0v6Ww==}
    dependencies:
      '@turf/boolean-within': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/point-on-feature@7.1.0:
    resolution: {integrity: sha512-lOO5J9I0diuGbN+r6jViEKRH3qfymsBvv25b7U0MuP8g/YC19ncUXZ86dmKfJx1++Rb485DS9h0nFvPmJpaOdg==}
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/center': 7.1.0
      '@turf/explode': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/nearest-point': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/point-to-line-distance@7.1.0:
    resolution: {integrity: sha512-Ps9eTOCaiNgxDaSNQux0wAcSLcrI0y0zYFaD9HnVm+yCMRliQXneFti2XXotS+gR7TpgnLRAAzyx4VzJMSN2tw==}
    dependencies:
      '@turf/bearing': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/projection': 7.1.0
      '@turf/rhumb-bearing': 7.1.0
      '@turf/rhumb-distance': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/points-within-polygon@7.1.0:
    resolution: {integrity: sha512-SzqeD9Gcp11rEya+rCVMy6IPuYMrphNEkCiQ39W6ec9hsaqKlruqmtudKhhckMGVLVUUBCQAu5f55yjcDfVW2w==}
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/polygon-smooth@7.1.0:
    resolution: {integrity: sha512-mTlmg4XUP5rKgCP/73N91owkAXIc3t1ZKLuwsJGQM1/Op48T3rJmDwVR/WZIMnVlxl5tFbssWCCB3blj4ivx9g==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/polygon-tangents@7.1.0:
    resolution: {integrity: sha512-ffBgHXtkrpgkNs8E6s9sVLSKG4lPGH3WBk294FNKBt9NS+rbhNCv8yTuOMeP0bOm/WizaCq/SUtVryJpUSoI/g==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/boolean-within': 7.1.0
      '@turf/explode': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/nearest-point': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/polygon-to-line@7.1.0:
    resolution: {integrity: sha512-FBlfyBWNQZCTVGqlJH7LR2VXmvj8AydxrA8zegqek/5oPGtQDeUgIppKmvmuNClqbglhv59QtCUVaDK4bOuCTA==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/polygonize@7.1.0:
    resolution: {integrity: sha512-FBjxnOzO29MbE7MWnMPHHYtOo93cQopT5pXhkuPyoKgcTUCntR1+iVFpl5YFbMkYup0j5Oexjo/pbY38lVSZGw==}
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/envelope': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/projection@7.1.0:
    resolution: {integrity: sha512-3wHluMoOvXnTe7dfi0kcluTyLNG5MwGsSsK5OA98vkkLH6a1xvItn8e9GcesuT07oB2km/bgefxYEIvjQG5JCA==}
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/quadrat-analysis@7.1.0:
    resolution: {integrity: sha512-4O5h9PyWgpqYXja9O+kzr+qk5MUz0IkJqPtt5oWWX5s4jRcLNqiEUf+zi/GDBQkVV8jH3S5klT5CLrF1fxK3hQ==}
    dependencies:
      '@turf/area': 7.1.0
      '@turf/bbox': 7.1.0
      '@turf/bbox-polygon': 7.1.0
      '@turf/centroid': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/point-grid': 7.1.0
      '@turf/random': 7.1.0
      '@turf/square-grid': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/random@7.1.0:
    resolution: {integrity: sha512-22mXv8ejDMUWkz8DSMMqdZb0s7a0ISJzXt6T9cHovfT//vsotzkVH+5PDxJQjvmigKMnpaUgobHmQss23tAwEQ==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/rectangle-grid@7.1.0:
    resolution: {integrity: sha512-4d2AuDj4LfMMJxNHbds5yX1oFR3mIVAB5D7mx6pFB0e+YkQW0mE2dUWhDTFGJZM+n45yqbNQ5hg19bmiXv94ug==}
    dependencies:
      '@turf/boolean-intersects': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/rewind@7.1.0:
    resolution: {integrity: sha512-zX0KDZpeiH89m1vYLTEJdDL6mFyoAsCxcG0P94mXO7/JXWf0AaxzA9MkNnA/d2QYX0G4ioCMjZ5cD6nXb8SXzw==}
    dependencies:
      '@turf/boolean-clockwise': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/rhumb-bearing@7.1.0:
    resolution: {integrity: sha512-ESZt70eOljHVnQMFKIdiu8LIHuQlpZgzh2nqSfV40BrYjsjI/sBKeK+sp2cBWk88nsSDlriPuMTNh4f50Jqpkw==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/rhumb-destination@7.1.0:
    resolution: {integrity: sha512-WA2TeO3qrv5ZrzNihtTLLYu8X4kd12WEC6JKElm99XhgLao1/4ao2SJUi43l88HqwbrnNiq4TueGQ6tYpXGU7A==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/rhumb-distance@7.1.0:
    resolution: {integrity: sha512-fR1V+yC4E1tnbdThomosiLcv0PQOwbfLSPM8rSWuxbMcJtffsncWxyJ0+N1F5juuHbcdaYhlduX8ri5I0ZCejw==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/sample@7.1.0:
    resolution: {integrity: sha512-9Iq/Ankr4+sgBoh4FpuVVvoW+AA10eej3FS89Zu79SFdCqUIdT7T42Nn3MlSVj4jMyA1oXyT2HIAlNWkwgLw6Q==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/sector@7.1.0:
    resolution: {integrity: sha512-2FI2rg//eXpa/l+WJtFfvHaf1NJ7ie2MoJ+RH5dKANtrfoof1Ed+y9dXSyuhem2tp/Srq2GhrjaSofFN5/g5vA==}
    dependencies:
      '@turf/circle': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-arc': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/shortest-path@7.1.0:
    resolution: {integrity: sha512-1UmFhS5zHNacLv5rszoFOXq02BGov1oJvjlDatXsSWAd+Z7tqxpDc8D+41edrXy0ZB0Yxsy6WPNagM6hG9PRaA==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/bbox-polygon': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/clean-coords': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/transform-scale': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/simplify@7.1.0:
    resolution: {integrity: sha512-JypymaoiSiFzGHwEoUkK0OPW1KQSnH3hEsEW3UIRS+apzltJ4HdFovYjsfqQgGZJZ+NJ9+dv7h8pgGLYuqcBUQ==}
    dependencies:
      '@turf/clean-coords': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/square-grid@7.1.0:
    resolution: {integrity: sha512-JyhsALULVRlkh8htdTi9aXaXFSUv6wRNbeFbqyGJKKlA5eF+AYmyWdI/BlFGQN27xtbtMPeAuLmj+8jaB2omGw==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/rectangle-grid': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/square@6.5.0:
    resolution: {integrity: sha1-q0Pu+Z05w2FXq1uAQWu+uh9rISI=}
    dependencies:
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
    dev: false

  /@turf/square@7.1.0:
    resolution: {integrity: sha512-ANuA+WXZheGTLW6Veq0i+/B2S4KMhEHAixDv9gQEb9e6FTyqTJVwrqP4CHI3OzA3DZ/ytFf+NTKVofetO/BBQg==}
    dependencies:
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/standard-deviational-ellipse@7.1.0:
    resolution: {integrity: sha512-JqvQFH/witHh+3XgPC1Qk4+3G8w8WQta2NTJjnGinOgFulH+7RD4DcxCT+XXtCHoeq8IvL9VPJRX3ciaW5nSCg==}
    dependencies:
      '@turf/center-mean': 7.1.0
      '@turf/ellipse': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/points-within-polygon': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/tag@7.1.0:
    resolution: {integrity: sha512-cD8TC++DnNmdI1B/apTf3nj2zRNY6SoLRliB8K76OB+70Kev8tOf4ZVgAqOd0u+Hpdg/T6l7dO7fyJ6UouE7jA==}
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/tesselate@7.1.0:
    resolution: {integrity: sha512-E/Z94Mx6kUjvQVbEcSuM9MbEo2dkOczRe4ZzjhFlLgJh1dCkfRgwYLH49mb2CcfG/me1arxoCgmtG+qgm7LrCg==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      earcut: 2.2.4
      tslib: 2.6.2
    dev: false

  /@turf/tin@7.1.0:
    resolution: {integrity: sha512-h8Bdm0IYN6OpKHM8lBRWGxkJnZcxL0KYecf8U6pa6DCEYsEXuEExMTvYSD2OmqIsL5ml8P6RjwgyI+dZeE0O9A==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/transform-rotate@7.1.0:
    resolution: {integrity: sha512-Vp7VBZ6DqaPV8mkwSycksBFRLqSj3y16zg+uEPSCsXUjbFtw9DOLcyH2F5vMpnC2bOpS9NOB4hebhJRwBwAPWQ==}
    dependencies:
      '@turf/centroid': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/rhumb-bearing': 7.1.0
      '@turf/rhumb-destination': 7.1.0
      '@turf/rhumb-distance': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/transform-scale@7.1.0:
    resolution: {integrity: sha512-m5fLnh3JqrWSv0sAC8Aieet/fr5IZND8BFaE9LakMidtNaJqOIPOyVmUoklcrGn6eK6MX+66rRPn+5a1pahlLQ==}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/center': 7.1.0
      '@turf/centroid': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/rhumb-bearing': 7.1.0
      '@turf/rhumb-destination': 7.1.0
      '@turf/rhumb-distance': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/transform-translate@7.1.0:
    resolution: {integrity: sha512-XA6Oh7VqUDrieY9m9/OF4XpBTd8qlfVGi3ObywojCqtHaHKLK3aXwTBZ276i0QKmZqOQA+2jFa9NhgF/TgBDrw==}
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/rhumb-destination': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/triangle-grid@7.1.0:
    resolution: {integrity: sha512-hrPyRAuX5PKu7txmc/11VPKrlJDR+JGzd+eijupKTspNLR4n2sqZUx8UXqSxZ/1nq06ScTyjIfGQJVzlRS8BTg==}
    dependencies:
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/intersect': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/truncate@6.5.0:
    resolution: {integrity: sha1-w6FsrZWfG+HFFWFX1VVcZLGRhdg=}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
    dev: false

  /@turf/truncate@7.1.0:
    resolution: {integrity: sha512-rrF3AML9PGZw2i5wmt53ESI+Ln9cZyCXgJ7QrEvkT8NbE4OFgmw6p8/1xT8+VEWFSpD4gHz+hmM+5FaFxXvtNg==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/turf@7.1.0:
    resolution: {integrity: sha512-7NA6tAjbu9oIvIfpRO5AdPrZbFTlUFU02HVA7sLJM9jFeNIZovW09QuDo23uoS2z5l94SXV1GgKKxN5wo7prCw==}
    dependencies:
      '@turf/along': 7.1.0
      '@turf/angle': 7.1.0
      '@turf/area': 7.1.0
      '@turf/bbox': 7.1.0
      '@turf/bbox-clip': 7.1.0
      '@turf/bbox-polygon': 7.1.0
      '@turf/bearing': 7.1.0
      '@turf/bezier-spline': 7.1.0
      '@turf/boolean-clockwise': 7.1.0
      '@turf/boolean-concave': 7.1.0
      '@turf/boolean-contains': 7.1.0
      '@turf/boolean-crosses': 7.1.0
      '@turf/boolean-disjoint': 7.1.0
      '@turf/boolean-equal': 7.1.0
      '@turf/boolean-intersects': 7.1.0
      '@turf/boolean-overlap': 7.1.0
      '@turf/boolean-parallel': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/boolean-point-on-line': 7.1.0
      '@turf/boolean-touches': 7.1.0
      '@turf/boolean-valid': 7.1.0
      '@turf/boolean-within': 7.1.0
      '@turf/buffer': 7.1.0
      '@turf/center': 7.1.0
      '@turf/center-mean': 7.1.0
      '@turf/center-median': 7.1.0
      '@turf/center-of-mass': 7.1.0
      '@turf/centroid': 7.1.0
      '@turf/circle': 7.1.0
      '@turf/clean-coords': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/clusters': 7.1.0
      '@turf/clusters-dbscan': 7.1.0
      '@turf/clusters-kmeans': 7.1.0
      '@turf/collect': 7.1.0
      '@turf/combine': 7.1.0
      '@turf/concave': 7.1.0
      '@turf/convex': 7.1.0
      '@turf/destination': 7.1.0
      '@turf/difference': 7.1.0
      '@turf/dissolve': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/distance-weight': 7.1.0
      '@turf/ellipse': 7.1.0
      '@turf/envelope': 7.1.0
      '@turf/explode': 7.1.0
      '@turf/flatten': 7.1.0
      '@turf/flip': 7.1.0
      '@turf/geojson-rbush': 7.1.0
      '@turf/great-circle': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/hex-grid': 7.1.0
      '@turf/interpolate': 7.1.0
      '@turf/intersect': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/isobands': 7.1.0
      '@turf/isolines': 7.1.0
      '@turf/kinks': 7.1.0
      '@turf/length': 7.1.0
      '@turf/line-arc': 7.1.0
      '@turf/line-chunk': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@turf/line-offset': 7.1.0
      '@turf/line-overlap': 7.1.0
      '@turf/line-segment': 7.1.0
      '@turf/line-slice': 7.1.0
      '@turf/line-slice-along': 7.1.0
      '@turf/line-split': 7.1.0
      '@turf/line-to-polygon': 7.1.0
      '@turf/mask': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/midpoint': 7.1.0
      '@turf/moran-index': 7.1.0
      '@turf/nearest-neighbor-analysis': 7.1.0
      '@turf/nearest-point': 7.1.0
      '@turf/nearest-point-on-line': 7.1.0
      '@turf/nearest-point-to-line': 7.1.0
      '@turf/planepoint': 7.1.0
      '@turf/point-grid': 7.1.0
      '@turf/point-on-feature': 7.1.0
      '@turf/point-to-line-distance': 7.1.0
      '@turf/points-within-polygon': 7.1.0
      '@turf/polygon-smooth': 7.1.0
      '@turf/polygon-tangents': 7.1.0
      '@turf/polygon-to-line': 7.1.0
      '@turf/polygonize': 7.1.0
      '@turf/projection': 7.1.0
      '@turf/quadrat-analysis': 7.1.0
      '@turf/random': 7.1.0
      '@turf/rectangle-grid': 7.1.0
      '@turf/rewind': 7.1.0
      '@turf/rhumb-bearing': 7.1.0
      '@turf/rhumb-destination': 7.1.0
      '@turf/rhumb-distance': 7.1.0
      '@turf/sample': 7.1.0
      '@turf/sector': 7.1.0
      '@turf/shortest-path': 7.1.0
      '@turf/simplify': 7.1.0
      '@turf/square': 7.1.0
      '@turf/square-grid': 7.1.0
      '@turf/standard-deviational-ellipse': 7.1.0
      '@turf/tag': 7.1.0
      '@turf/tesselate': 7.1.0
      '@turf/tin': 7.1.0
      '@turf/transform-rotate': 7.1.0
      '@turf/transform-scale': 7.1.0
      '@turf/transform-translate': 7.1.0
      '@turf/triangle-grid': 7.1.0
      '@turf/truncate': 7.1.0
      '@turf/union': 7.1.0
      '@turf/unkink-polygon': 7.1.0
      '@turf/voronoi': 7.1.0
      '@types/geojson': 7946.0.14
      tslib: 2.6.2
    dev: false

  /@turf/union@7.1.0:
    resolution: {integrity: sha512-7VI8jONdBg9qmbfNlLQycPr93l5aU9HGMgWI9M6pb4ERuU2+p8KgffCgs2NyMtP2HxPrKSybzj31g7bnbEKofQ==}
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      polygon-clipping: 0.15.7
      tslib: 2.6.2
    dev: false

  /@turf/unkink-polygon@7.1.0:
    resolution: {integrity: sha512-pqkirni2aLpRA1ELFIuJz+mkjYyJQX8Ar6BflSu1b0ajY/CTrcDxbIv1x8UfvbybLzdJc4Gxzg5mo4cEtSwtaQ==}
    dependencies:
      '@turf/area': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.14
      rbush: 3.0.1
      tslib: 2.6.2
    dev: false

  /@turf/voronoi@7.1.0:
    resolution: {integrity: sha512-xUvzPDG6GaqEekgxd+pjeMKJXOYJ3eFIqUHbTe/ISKzzv3f2cFGiR2VH7ZGXri8d4ozzCQbUQ27ilHPPLf5+xw==}
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/d3-voronoi': 1.1.12
      '@types/geojson': 7946.0.14
      d3-voronoi: 1.1.2
      tslib: 2.6.2
    dev: false

  /@tweenjs/tween.js@18.6.4:
    resolution: {integrity: sha1-QKPQqTZHEkhy3sjg/RvVkmaVtso=}
    dev: false

  /@types/chart.js@2.9.41:
    resolution: {integrity: sha512-3dvkDvueckY83UyUXtJMalYoH6faOLkWQoaTlJgB4Djde3oORmNP0Jw85HtzTuXyliUHcdp704s0mZFQKio/KQ==}
    dependencies:
      moment: 2.30.1
    dev: true

  /@types/d3-voronoi@1.1.12:
    resolution: {integrity: sha512-DauBl25PKZZ0WVJr42a6CNvI6efsdzofl9sajqZr2Gf5Gu733WkDdUGiPkUHXiUvYGzNNlFQde2wdZdfQPG+yw==}
    dev: false

  /@types/estree@1.0.1:
    resolution: {integrity: sha512-LG4opVs2ANWZ1TJoKc937iMmNstM/d0ae1vNbnBvBhqCSezgVUOzcLCqbI5elV8Vy6WKwKjaqR+zO9VKirBBCA==}
    dev: true

  /@types/geojson@7946.0.14:
    resolution: {integrity: sha512-WCfD5Ht3ZesJUsONdhvm84dmzWOiOzOAqOncN0++w0lBw1o8OuDNJF2McvvCef/yBqb/HYRahp1BYtODFQ8bRg==}
    dev: false

  /@types/geojson@7946.0.8:
    resolution: {integrity: sha512-1rkryxURpr6aWP7R786/UQOkJ3PcpQiWkAXBmdWc7ryFWqN6a4xfK7BtjXvFBKO9LjQ+MWQSWxYeZX1OApnArA==}
    dev: false

  /@types/json-schema@7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}
    dev: false

  /@types/json5@0.0.29:
    resolution: {integrity: sha1-7ihweulOEdK4J7y+UnC86n8+ce4=}
    dev: true

  /@types/lodash-es@4.17.7:
    resolution: {integrity: sha512-z0ptr6UI10VlU6l5MYhGwS4mC8DZyYer2mCoyysZtSF7p26zOX8UpbrV0YpNYLGS8K4PUFIyEr62IMFFjveSiQ==}
    dependencies:
      '@types/lodash': 4.14.194
    dev: false

  /@types/lodash@4.14.194:
    resolution: {integrity: sha512-r22s9tAS7imvBt2lyHC9B8AGwWnXaYb1tY09oyLkXDs4vArpYJzw09nj8MLx5VfciBPGIb+ZwG0ssYnEPJxn/g==}
    dev: false

  /@types/node@20.2.4:
    resolution: {integrity: sha512-ni5f8Xlf4PwnT/Z3f0HURc3ZSw8UyrqMqmM3L5ysa7VjHu8c3FOmIo1nKCcLrV/OAmtf3N4kFna/aJqxsfEtnA==}
    dev: true

  /@types/svgo@2.6.4:
    resolution: {integrity: sha512-l4cmyPEckf8moNYHdJ+4wkHvFxjyW6ulm9l4YGaOxeyBWPhBOT0gvni1InpFPdzx1dKf/2s62qGITwxNWnPQng==}
    dependencies:
      '@types/node': 20.2.4
    dev: true

  /@types/web-bluetooth@0.0.16:
    resolution: {integrity: sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ==}
    dev: false

  /@vitejs/plugin-legacy@4.0.2(terser@5.30.3)(vite@4.5.0):
    resolution: {integrity: sha512-ivnt9sCkgwJTYTWLjuvY6H/HTuiQC1EgzAPkiAvi0yNAssiqOJjyjhG3hAK5LFUUorE0w9kGxn8K0f/74DlbxQ==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      terser: ^5.4.0
      vite: ^4.0.0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/preset-env': 7.21.4(@babel/core@7.21.4)
      browserslist: 4.21.5
      core-js: 3.30.1
      magic-string: 0.30.0
      regenerator-runtime: 0.13.11
      systemjs: 6.14.1
      terser: 5.30.3
      vite: 4.5.0(sass@1.62.0)(terser@5.30.3)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@vitejs/plugin-vue@4.1.0(vite@4.5.0)(vue@3.4.15):
    resolution: {integrity: sha512-++9JOAFdcXI3lyer9UKUV4rfoQ3T1RN8yDqoCLar86s0xQct5yblxAE+yWgRnU5/0FOlVCpTZpYSBV/bGWrSrQ==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.0.0
      vue: ^3.2.25
    dependencies:
      vite: 4.5.0(sass@1.62.0)(terser@5.30.3)
      vue: 3.4.15
    dev: true

  /@vue/compiler-core@3.4.15:
    resolution: {integrity: sha512-XcJQVOaxTKCnth1vCxEChteGuwG6wqnUHxAm1DO3gCz0+uXKaJNx8/digSz4dLALCy8n2lKq24jSUs8segoqIw==}
    dependencies:
      '@babel/parser': 7.23.6
      '@vue/shared': 3.4.15
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.0.2

  /@vue/compiler-dom@3.4.15:
    resolution: {integrity: sha512-wox0aasVV74zoXyblarOM3AZQz/Z+OunYcIHe1OsGclCHt8RsRm04DObjefaI82u6XDzv+qGWZ24tIsRAIi5MQ==}
    dependencies:
      '@vue/compiler-core': 3.4.15
      '@vue/shared': 3.4.15

  /@vue/compiler-sfc@2.7.16:
    resolution: {integrity: sha512-KWhJ9k5nXuNtygPU7+t1rX6baZeqOYLEforUPjgNDBnLicfHCoi48H87Q8XyLZOrNNsmhuwKqtpDQWjEFe6Ekg==}
    dependencies:
      '@babel/parser': 7.23.6
      postcss: 8.4.33
      source-map: 0.6.1
    optionalDependencies:
      prettier: 2.8.7
    dev: false

  /@vue/compiler-sfc@3.4.15:
    resolution: {integrity: sha512-LCn5M6QpkpFsh3GQvs2mJUOAlBQcCco8D60Bcqmf3O3w5a+KWS5GvYbrrJBkgvL1BDnTp+e8q0lXCLgHhKguBA==}
    dependencies:
      '@babel/parser': 7.23.6
      '@vue/compiler-core': 3.4.15
      '@vue/compiler-dom': 3.4.15
      '@vue/compiler-ssr': 3.4.15
      '@vue/shared': 3.4.15
      estree-walker: 2.0.2
      magic-string: 0.30.5
      postcss: 8.4.33
      source-map-js: 1.0.2

  /@vue/compiler-ssr@3.4.15:
    resolution: {integrity: sha512-1jdeQyiGznr8gjFDadVmOJqZiLNSsMa5ZgqavkPZ8O2wjHv0tVuAEsw5hTdUoUW4232vpBbL/wJhzVW/JwY1Uw==}
    dependencies:
      '@vue/compiler-dom': 3.4.15
      '@vue/shared': 3.4.15

  /@vue/devtools-api@6.5.0:
    resolution: {integrity: sha512-o9KfBeaBmCKl10usN4crU53fYtC1r7jJwdGKjPT24t348rHxgfpZ0xL3Xm/gLUYnc0oTp8LAmrxOeLyu6tbk2Q==}
    dev: false

  /@vue/reactivity@3.4.15:
    resolution: {integrity: sha512-55yJh2bsff20K5O84MxSvXKPHHt17I2EomHznvFiJCAZpJTNW8IuLj1xZWMLELRhBK3kkFV/1ErZGHJfah7i7w==}
    dependencies:
      '@vue/shared': 3.4.15

  /@vue/runtime-core@3.4.15:
    resolution: {integrity: sha512-6E3by5m6v1AkW0McCeAyhHTw+3y17YCOKG0U0HDKDscV4Hs0kgNT5G+GCHak16jKgcCDHpI9xe5NKb8sdLCLdw==}
    dependencies:
      '@vue/reactivity': 3.4.15
      '@vue/shared': 3.4.15

  /@vue/runtime-dom@3.4.15:
    resolution: {integrity: sha512-EVW8D6vfFVq3V/yDKNPBFkZKGMFSvZrUQmx196o/v2tHKdwWdiZjYUBS+0Ez3+ohRyF8Njwy/6FH5gYJ75liUw==}
    dependencies:
      '@vue/runtime-core': 3.4.15
      '@vue/shared': 3.4.15
      csstype: 3.1.3

  /@vue/server-renderer@3.4.15(vue@3.4.15):
    resolution: {integrity: sha512-3HYzaidu9cHjrT+qGUuDhFYvF/j643bHC6uUN9BgM11DVy+pM6ATsG6uPBLnkwOgs7BpJABReLmpL3ZPAsUaqw==}
    peerDependencies:
      vue: 3.4.15
    dependencies:
      '@vue/compiler-ssr': 3.4.15
      '@vue/shared': 3.4.15
      vue: 3.4.15

  /@vue/shared@3.4.15:
    resolution: {integrity: sha512-KzfPTxVaWfB+eGcGdbSf4CWdaXcGDqckoeXUh7SB3fZdEtzPCK2Vq9B/lRRL3yutax/LWITz+SwvgyOxz5V75g==}

  /@vueuse/core@9.13.0(vue@3.4.15):
    resolution: {integrity: sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==}
    dependencies:
      '@types/web-bluetooth': 0.0.16
      '@vueuse/metadata': 9.13.0
      '@vueuse/shared': 9.13.0(vue@3.4.15)
      vue-demi: 0.14.10(vue@3.4.15)
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue
    dev: false

  /@vueuse/metadata@9.13.0:
    resolution: {integrity: sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==}
    dev: false

  /@vueuse/shared@9.13.0(vue@3.4.15):
    resolution: {integrity: sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==}
    dependencies:
      vue-demi: 0.14.10(vue@3.4.15)
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue
    dev: false

  /@webassemblyjs/ast@1.9.0:
    resolution: {integrity: sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=}
    dependencies:
      '@webassemblyjs/helper-module-context': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/wast-parser': 1.9.0
    dev: false

  /@webassemblyjs/floating-point-hex-parser@1.9.0:
    resolution: {integrity: sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=}
    dev: false

  /@webassemblyjs/helper-api-error@1.9.0:
    resolution: {integrity: sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=}
    dev: false

  /@webassemblyjs/helper-buffer@1.9.0:
    resolution: {integrity: sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=}
    dev: false

  /@webassemblyjs/helper-code-frame@1.9.0:
    resolution: {integrity: sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=}
    dependencies:
      '@webassemblyjs/wast-printer': 1.9.0
    dev: false

  /@webassemblyjs/helper-fsm@1.9.0:
    resolution: {integrity: sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=}
    dev: false

  /@webassemblyjs/helper-module-context@1.9.0:
    resolution: {integrity: sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
    dev: false

  /@webassemblyjs/helper-wasm-bytecode@1.9.0:
    resolution: {integrity: sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=}
    dev: false

  /@webassemblyjs/helper-wasm-section@1.9.0:
    resolution: {integrity: sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
    dev: false

  /@webassemblyjs/ieee754@1.9.0:
    resolution: {integrity: sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=}
    dependencies:
      '@xtuc/ieee754': 1.2.0
    dev: false

  /@webassemblyjs/leb128@1.9.0:
    resolution: {integrity: sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=}
    dependencies:
      '@xtuc/long': 4.2.2
    dev: false

  /@webassemblyjs/utf8@1.9.0:
    resolution: {integrity: sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=}
    dev: false

  /@webassemblyjs/wasm-edit@1.9.0:
    resolution: {integrity: sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/helper-wasm-section': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
      '@webassemblyjs/wasm-opt': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
      '@webassemblyjs/wast-printer': 1.9.0
    dev: false

  /@webassemblyjs/wasm-gen@1.9.0:
    resolution: {integrity: sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/ieee754': 1.9.0
      '@webassemblyjs/leb128': 1.9.0
      '@webassemblyjs/utf8': 1.9.0
    dev: false

  /@webassemblyjs/wasm-opt@1.9.0:
    resolution: {integrity: sha1-IhEYHlsxMmRDzIES658LkChyGmE=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
    dev: false

  /@webassemblyjs/wasm-parser@1.9.0:
    resolution: {integrity: sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-api-error': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/ieee754': 1.9.0
      '@webassemblyjs/leb128': 1.9.0
      '@webassemblyjs/utf8': 1.9.0
    dev: false

  /@webassemblyjs/wast-parser@1.9.0:
    resolution: {integrity: sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/floating-point-hex-parser': 1.9.0
      '@webassemblyjs/helper-api-error': 1.9.0
      '@webassemblyjs/helper-code-frame': 1.9.0
      '@webassemblyjs/helper-fsm': 1.9.0
      '@xtuc/long': 4.2.2
    dev: false

  /@webassemblyjs/wast-printer@1.9.0:
    resolution: {integrity: sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/wast-parser': 1.9.0
      '@xtuc/long': 4.2.2
    dev: false

  /@xtuc/ieee754@1.2.0:
    resolution: {integrity: sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=}
    dev: false

  /@xtuc/long@4.2.2:
    resolution: {integrity: sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=}
    dev: false

  /acorn-jsx@5.3.2(acorn@8.11.2):
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 8.11.2
    dev: true

  /acorn@6.4.2:
    resolution: {integrity: sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /acorn@8.11.2:
    resolution: {integrity: sha512-nc0Axzp/0FILLEVsm4fNwLCwMttvhEI263QtVPQcbpfZZ3ts0hLsZGOpE6czNlid7CJ9MlyH8reXkpsf3YUY4w==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /acorn@8.11.3:
    resolution: {integrity: sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  /aight@2.1.1:
    resolution: {integrity: sha1-nYUnECSVY8pO087gYFdvUD/hBKs=}
    hasBin: true
    dependencies:
      falafel: 0.3.1
      rw: 0.1.4
    dev: false

  /ajv-errors@1.0.1(ajv@6.12.6):
    resolution: {integrity: sha1-81mGrOuRr63sQQL72FAUlQzvpk0=}
    peerDependencies:
      ajv: '>=5.0.0'
    dependencies:
      ajv: 6.12.6
    dev: false

  /ajv-keywords@3.5.2(ajv@6.12.6):
    resolution: {integrity: sha1-MfKdpatuANHC0yms97WSlhTVAU0=}
    peerDependencies:
      ajv: ^6.9.1
    dependencies:
      ajv: 6.12.6
    dev: false

  /ajv@6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  /ansi-regex@2.1.1:
    resolution: {integrity: sha1-w7M6te42DYbg5ijwRorn7yfWVN8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-regex@5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=}
    engines: {node: '>=8'}
    dev: true

  /ansi-styles@2.2.1:
    resolution: {integrity: sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-styles@3.2.1:
    resolution: {integrity: sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3
    dev: true

  /ansi-styles@4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1
    dev: true

  /anymatch@2.0.0:
    resolution: {integrity: sha1-vLJLTzeTTZqnrBe0ra+J58du8us=}
    requiresBuild: true
    dependencies:
      micromatch: 3.1.10
      normalize-path: 2.1.1
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  /aproba@1.2.0:
    resolution: {integrity: sha1-aALmJk79GMeQobDVF/DyYnvyyUo=}
    dev: false

  /argparse@2.0.1:
    resolution: {integrity: sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=}
    dev: true

  /arr-diff@4.0.0:
    resolution: {integrity: sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=}
    engines: {node: '>=0.10.0'}

  /arr-flatten@1.1.0:
    resolution: {integrity: sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=}
    engines: {node: '>=0.10.0'}

  /arr-union@3.1.0:
    resolution: {integrity: sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=}
    engines: {node: '>=0.10.0'}

  /array-buffer-byte-length@1.0.0:
    resolution: {integrity: sha512-LPuwb2P+NrQw3XhxGc36+XSvuBPopovXYTR9Ew++Du9Yb/bx5AzBfrIsBoj0EZUifjQU+sHL21sseZ3jerWO/A==}
    dependencies:
      call-bind: 1.0.2
      is-array-buffer: 3.0.2
    dev: true

  /array-includes@3.1.6:
    resolution: {integrity: sha512-sgTbLvL6cNnw24FnbaDyjmvddQ2ML8arZsgaJhoABMoplz/4QRhtrYS+alr1BUM1Bwp6dhx8vVCBSLG+StwOFw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
      get-intrinsic: 1.2.0
      is-string: 1.0.7
    dev: true

  /array-unique@0.3.2:
    resolution: {integrity: sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=}
    engines: {node: '>=0.10.0'}

  /array.prototype.flat@1.3.1:
    resolution: {integrity: sha512-roTU0KWIOmJ4DRLmwKd19Otg0/mT3qPNt0Qb3GWW8iObuZXxrjB/pzn0R3hqpRSWg4HCwqx+0vwOnWnvlOyeIA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
      es-shim-unscopables: 1.0.0
    dev: true

  /array.prototype.flatmap@1.3.1:
    resolution: {integrity: sha512-8UGn9O1FDVvMNB0UlLv4voxRMze7+FpHyF5mSMRjWHUMlpoDViniy05870VlxhfgTnLbpuwTzvD76MTtWxB/mQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
      es-shim-unscopables: 1.0.0
    dev: true

  /asap@2.0.6:
    resolution: {integrity: sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=}
    dev: false

  /asn1.js@4.10.1:
    resolution: {integrity: sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA=}
    dependencies:
      bn.js: 4.12.0
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: false

  /assert@1.5.1:
    resolution: {integrity: sha512-zzw1uCAgLbsKwBfFc8CX78DDg+xZeBksSO3vwVIDDN5i94eOrPsSSyiVhmsSABFDM/OcpE2aagCat9dnWQLG1A==}
    dependencies:
      object.assign: 4.1.4
      util: 0.10.4
    dev: false

  /assign-symbols@1.0.0:
    resolution: {integrity: sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=}
    engines: {node: '>=0.10.0'}

  /async-each@1.0.6:
    resolution: {integrity: sha512-c646jH1avxr+aVpndVMeAfYw7wAa6idufrlN3LPA4PmKS0QEGp6PIC9nwz0WQkkvBGAMEki3pFdtxaF39J9vvg==}
    requiresBuild: true
    dev: false
    optional: true

  /async-validator@4.2.5:
    resolution: {integrity: sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==}
    dev: false

  /asynckit@0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=}
    dev: false

  /atob@2.1.2:
    resolution: {integrity: sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=}
    engines: {node: '>= 4.5.0'}
    hasBin: true

  /available-typed-arrays@1.0.5:
    resolution: {integrity: sha1-kvlWFlAQadB9EO2y/DfT4cZRI7c=}
    engines: {node: '>= 0.4'}
    dev: true

  /axios@0.20.0:
    resolution: {integrity: sha1-BXujDwSIRpSZOozQf6OUz/EcUL0=}
    dependencies:
      follow-redirects: 1.15.2
    transitivePeerDependencies:
      - debug
    dev: false

  /axios@1.3.6:
    resolution: {integrity: sha512-PEcdkk7JcdPiMDkvM4K6ZBRYq9keuVJsToxm2zQIM70Qqo2WHTdJZMXcG9X+RmRp2VPNUQC8W1RAGbgt6b1yMg==}
    dependencies:
      follow-redirects: 1.15.2
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug
    dev: false

  /babel-plugin-polyfill-corejs2@0.3.3(@babel/core@7.21.4):
    resolution: {integrity: sha512-8hOdmFYFSZhqg2C/JgLUQ+t52o5nirNwaWM2B9LWteozwIvM14VSwdsCAUET10qT+kmySAlseadmfeeSWFCy+Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.21.4
      '@babel/core': 7.21.4
      '@babel/helper-define-polyfill-provider': 0.3.3(@babel/core@7.21.4)
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-polyfill-corejs3@0.6.0(@babel/core@7.21.4):
    resolution: {integrity: sha512-+eHqR6OPcBhJOGgsIar7xoAB1GcSwVUA3XjAd7HJNzOXT4wv6/H7KIdA/Nc60cvUlDbKApmqNvD1B1bzOt4nyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-define-polyfill-provider': 0.3.3(@babel/core@7.21.4)
      core-js-compat: 3.30.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-polyfill-regenerator@0.4.1(@babel/core@7.21.4):
    resolution: {integrity: sha512-NtQGmyQDXjQqQ+IzRkBVwEOz9lQ4zxAQZgoAYEtU9dJjnl1Oc98qnN7jcp+bE7O7aYzVpavXE3/VKXNzUbh7aw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-define-polyfill-provider': 0.3.3(@babel/core@7.21.4)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /balanced-match@1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=}

  /base64-js@1.5.1:
    resolution: {integrity: sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=}
    dev: false

  /base@0.11.2:
    resolution: {integrity: sha1-e95c7RRbbVUakNuH+DxVi060io8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      cache-base: 1.0.1
      class-utils: 0.3.6
      component-emitter: 1.3.0
      define-property: 1.0.0
      isobject: 3.0.1
      mixin-deep: 1.3.2
      pascalcase: 0.1.1

  /bezier-js@2.6.1:
    resolution: {integrity: sha1-7Zxuv8syFHlP8hPPkO6KrGROz38=}
    dev: false

  /big.js@5.2.2:
    resolution: {integrity: sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=}

  /bignumber.js@9.1.2:
    resolution: {integrity: sha512-2/mKyZH9K85bzOEfhXDBFZTGd1CTs+5IHpeFQo9luiBG7hghdC851Pj2WAhb6E3R6b9tZj/XKhbg4fum+Kepug==}
    dev: false

  /binary-extensions@1.13.1:
    resolution: {integrity: sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    dev: false
    optional: true

  /binary-extensions@2.2.0:
    resolution: {integrity: sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=}
    engines: {node: '>=8'}

  /bindings@1.5.0:
    resolution: {integrity: sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=}
    requiresBuild: true
    dependencies:
      file-uri-to-path: 1.0.0
    dev: false
    optional: true

  /bluebird@3.7.2:
    resolution: {integrity: sha1-nyKcFb4nJFT/qXOs4NvueaGww28=}

  /bn.js@4.12.0:
    resolution: {integrity: sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=}
    dev: false

  /bn.js@5.2.1:
    resolution: {integrity: sha512-eXRvHzWyYPBuB4NBy0cmYQjGitUrtqwbvlzP3G6VFnNRbsZQIxQ10PbKKHt8gZ/HW/D/747aDl+QkDqg3KQLMQ==}
    dev: false

  /boolbase@1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=}
    dev: true

  /brace-expansion@1.1.11:
    resolution: {integrity: sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  /brace-expansion@2.0.1:
    resolution: {integrity: sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=}
    dependencies:
      balanced-match: 1.0.2
    dev: true

  /braces@2.3.2:
    resolution: {integrity: sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-flatten: 1.1.0
      array-unique: 0.3.2
      extend-shallow: 2.0.1
      fill-range: 4.0.0
      isobject: 3.0.1
      repeat-element: 1.1.4
      snapdragon: 0.8.2
      snapdragon-node: 2.1.1
      split-string: 3.1.0
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  /braces@3.0.2:
    resolution: {integrity: sha1-NFThpGLujVmeI23zNs2epPiv4Qc=}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.0.1

  /brorand@1.1.0:
    resolution: {integrity: sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=}
    dev: false

  /browserify-aes@1.2.0:
    resolution: {integrity: sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=}
    dependencies:
      buffer-xor: 1.0.3
      cipher-base: 1.0.4
      create-hash: 1.2.0
      evp_bytestokey: 1.0.3
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /browserify-cipher@1.0.1:
    resolution: {integrity: sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=}
    dependencies:
      browserify-aes: 1.2.0
      browserify-des: 1.0.2
      evp_bytestokey: 1.0.3
    dev: false

  /browserify-des@1.0.2:
    resolution: {integrity: sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=}
    dependencies:
      cipher-base: 1.0.4
      des.js: 1.1.0
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /browserify-rsa@4.1.0:
    resolution: {integrity: sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0=}
    dependencies:
      bn.js: 5.2.1
      randombytes: 2.1.0
    dev: false

  /browserify-sign@4.2.3:
    resolution: {integrity: sha512-JWCZW6SKhfhjJxO8Tyiiy+XYB7cqd2S5/+WeYHsKdNKFlCBhKbblba1A/HN/90YwtxKc8tCErjffZl++UNmGiw==}
    engines: {node: '>= 0.12'}
    dependencies:
      bn.js: 5.2.1
      browserify-rsa: 4.1.0
      create-hash: 1.2.0
      create-hmac: 1.1.7
      elliptic: 6.5.7
      hash-base: 3.0.4
      inherits: 2.0.4
      parse-asn1: 5.1.7
      readable-stream: 2.3.8
      safe-buffer: 5.2.1
    dev: false

  /browserify-zlib@0.2.0:
    resolution: {integrity: sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=}
    dependencies:
      pako: 1.0.11
    dev: false

  /browserslist@4.21.5:
    resolution: {integrity: sha512-tUkiguQGW7S3IhB7N+c2MV/HZPSCPAAiYBZXLsBhFB/PCy6ZKKsZrmBayHV9fdGV/ARIfJ14NkxKzRDjvp7L6w==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001480
      electron-to-chromium: 1.4.368
      node-releases: 2.0.10
      update-browserslist-db: 1.0.11(browserslist@4.21.5)
    dev: true

  /buffer-from@1.1.2:
    resolution: {integrity: sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=}

  /buffer-xor@1.0.3:
    resolution: {integrity: sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=}
    dev: false

  /buffer@4.9.2:
    resolution: {integrity: sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
      isarray: 1.0.0
    dev: false

  /builtin-status-codes@3.0.0:
    resolution: {integrity: sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=}
    dev: false

  /builtins@5.0.1:
    resolution: {integrity: sha512-qwVpFEHNfhYJIzNRBvd2C1kyo6jz3ZSMPyyuR47OPdiKWlbYnZNyDWuyR175qDnAJLiCo5fBBqPb3RiXgWlkOQ==}
    dependencies:
      semver: 7.5.0
    dev: true

  /cacache@12.0.4:
    resolution: {integrity: sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=}
    dependencies:
      bluebird: 3.7.2
      chownr: 1.1.4
      figgy-pudding: 3.5.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      infer-owner: 1.0.4
      lru-cache: 5.1.1
      mississippi: 3.0.0
      mkdirp: 0.5.6
      move-concurrently: 1.0.1
      promise-inflight: 1.0.1(bluebird@3.7.2)
      rimraf: 2.7.1
      ssri: 6.0.2
      unique-filename: 1.1.1
      y18n: 4.0.3
    dev: false

  /cache-base@1.0.1:
    resolution: {integrity: sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      collection-visit: 1.0.0
      component-emitter: 1.3.0
      get-value: 2.0.6
      has-value: 1.0.0
      isobject: 3.0.1
      set-value: 2.0.1
      to-object-path: 0.3.0
      union-value: 1.0.1
      unset-value: 1.0.0

  /call-bind@1.0.2:
    resolution: {integrity: sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=}
    dependencies:
      function-bind: 1.1.1
      get-intrinsic: 1.2.0

  /call-bind@1.0.7:
    resolution: {integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2
    dev: false

  /callsites@3.1.0:
    resolution: {integrity: sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=}
    engines: {node: '>=6'}
    dev: true

  /caniuse-lite@1.0.30001480:
    resolution: {integrity: sha512-q7cpoPPvZYgtyC4VaBSN0Bt+PJ4c4EYRf0DrduInOz2SkFpHD5p3LnvEpqBp7UnJn+8x1Ogl1s38saUxe+ihQQ==}
    dev: true

  /caniuse-lite@1.0.30001660:
    resolution: {integrity: sha512-GacvNTTuATm26qC74pt+ad1fW15mlQ/zuTzzY1ZoIzECTP8HURDfF43kNxPgf7H1jmelCBQTTbBNxdSXOA7Bqg==}
    dev: false

  /chalk@1.1.3:
    resolution: {integrity: sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0
    dev: true

  /chalk@2.4.2:
    resolution: {integrity: sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0
    dev: true

  /chalk@4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /chokidar@2.1.8:
    resolution: {integrity: sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=}
    requiresBuild: true
    dependencies:
      anymatch: 2.0.0
      async-each: 1.0.6
      braces: 2.3.2
      glob-parent: 3.1.0
      inherits: 2.0.4
      is-binary-path: 1.0.1
      is-glob: 4.0.3
      normalize-path: 3.0.0
      path-is-absolute: 1.0.1
      readdirp: 2.2.1
      upath: 1.2.0
    optionalDependencies:
      fsevents: 1.2.13
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /chokidar@3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  /chownr@1.1.4:
    resolution: {integrity: sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=}
    dev: false

  /chrome-trace-event@1.0.4:
    resolution: {integrity: sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==}
    engines: {node: '>=6.0'}
    dev: false

  /cipher-base@1.0.4:
    resolution: {integrity: sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=}
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /class-utils@0.3.6:
    resolution: {integrity: sha1-+TNprouafOAv1B+q0MqDAzGQxGM=}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      define-property: 0.2.5
      isobject: 3.0.1
      static-extend: 0.1.2

  /clone@2.1.2:
    resolution: {integrity: sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=}
    engines: {node: '>=0.8'}
    dev: true

  /collection-visit@1.0.0:
    resolution: {integrity: sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-visit: 1.0.0
      object-visit: 1.0.1

  /color-convert@1.9.3:
    resolution: {integrity: sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=}
    dependencies:
      color-name: 1.1.3
    dev: true

  /color-convert@2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4
    dev: true

  /color-name@1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=}
    dev: true

  /color-name@1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=}
    dev: true

  /combined-stream@1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0
    dev: false

  /commander@2.20.3:
    resolution: {integrity: sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=}

  /commander@7.2.0:
    resolution: {integrity: sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc=}
    engines: {node: '>= 10'}
    dev: true

  /commondir@1.0.1:
    resolution: {integrity: sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=}
    dev: false

  /component-emitter@1.3.0:
    resolution: {integrity: sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=}

  /concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=}

  /concat-stream@1.6.2:
    resolution: {integrity: sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=}
    engines: {'0': node >= 0.8}
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 2.3.8
      typedarray: 0.0.6
    dev: false

  /concaveman@1.2.1:
    resolution: {integrity: sha512-PwZYKaM/ckQSa8peP5JpVr7IMJ4Nn/MHIaWUjP4be+KoZ7Botgs8seAZGpmaOM+UZXawcdYRao/px9ycrCihHw==}
    dependencies:
      point-in-polygon: 1.1.0
      rbush: 3.0.1
      robust-predicates: 2.0.4
      tinyqueue: 2.0.3
    dev: false

  /console-browserify@1.2.0:
    resolution: {integrity: sha1-ZwY871fOts9Jk6KrOlWECujEkzY=}
    dev: false

  /constants-browserify@1.0.0:
    resolution: {integrity: sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=}
    dev: false

  /convert-source-map@1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}
    dev: true

  /copy-concurrently@1.0.5:
    resolution: {integrity: sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=}
    dependencies:
      aproba: 1.2.0
      fs-write-stream-atomic: 1.0.10
      iferr: 0.1.5
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3
    dev: false

  /copy-descriptor@0.1.1:
    resolution: {integrity: sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=}
    engines: {node: '>=0.10.0'}

  /core-js-compat@3.30.1:
    resolution: {integrity: sha512-d690npR7MC6P0gq4npTl5n2VQeNAmUrJ90n+MHiKS7W2+xno4o3F5GDEuylSdi6EJ3VssibSGXOa1r3YXD3Mhw==}
    dependencies:
      browserslist: 4.21.5
    dev: true

  /core-js@3.30.1:
    resolution: {integrity: sha512-ZNS5nbiSwDTq4hFosEDqm65izl2CWmLz0hARJMyNQBgkUZMIF51cQiMvIQKA6hvuaeWxQDP3hEedM1JZIgTldQ==}
    requiresBuild: true

  /core-util-is@1.0.3:
    resolution: {integrity: sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=}
    dev: false

  /cors@2.8.5:
    resolution: {integrity: sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=}
    engines: {node: '>= 0.10'}
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2
    dev: true

  /create-ecdh@4.0.4:
    resolution: {integrity: sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=}
    dependencies:
      bn.js: 4.12.0
      elliptic: 6.5.7
    dev: false

  /create-hash@1.2.0:
    resolution: {integrity: sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=}
    dependencies:
      cipher-base: 1.0.4
      inherits: 2.0.4
      md5.js: 1.3.5
      ripemd160: 2.0.2
      sha.js: 2.4.11
    dev: false

  /create-hmac@1.1.7:
    resolution: {integrity: sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=}
    dependencies:
      cipher-base: 1.0.4
      create-hash: 1.2.0
      inherits: 2.0.4
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11
    dev: false

  /cropperjs@1.5.12:
    resolution: {integrity: sha1-2cDbK/uMDXadUXOej5FrvEThD1A=}
    dev: false

  /cross-spawn@7.0.3:
    resolution: {integrity: sha1-9zqFudXUHQRVUcF34ogtSshXKKY=}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: true

  /crypto-browserify@3.12.0:
    resolution: {integrity: sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=}
    dependencies:
      browserify-cipher: 1.0.1
      browserify-sign: 4.2.3
      create-ecdh: 4.0.4
      create-hash: 1.2.0
      create-hmac: 1.1.7
      diffie-hellman: 5.0.3
      inherits: 2.0.4
      pbkdf2: 3.1.2
      public-encrypt: 4.0.3
      randombytes: 2.1.0
      randomfill: 1.0.4
    dev: false

  /crypto-js@4.2.0:
    resolution: {integrity: sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==}
    dev: false

  /css-select@4.3.0:
    resolution: {integrity: sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==}
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1
    dev: true

  /css-tree@1.1.3:
    resolution: {integrity: sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=}
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1
    dev: true

  /css-what@6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}
    dev: true

  /cssesc@3.0.0:
    resolution: {integrity: sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /csso@4.2.0:
    resolution: {integrity: sha1-6jpWE0bo3J9UbW/r7dUBh884lSk=}
    engines: {node: '>=8.0.0'}
    dependencies:
      css-tree: 1.1.3
    dev: true

  /csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  /cyclist@1.0.2:
    resolution: {integrity: sha512-0sVXIohTfLqVIW3kb/0n6IiWF3Ifj5nm2XaSrLq2DI6fKIGa2fYAZdk917rUneaeLVpYfFcyXE2ft0fe3remsA==}
    dev: false

  /d3-array@1.2.4:
    resolution: {integrity: sha1-Y1zk1e6nWfb2BYY9vPww7cc39x8=}
    dev: false

  /d3-axis@1.0.12:
    resolution: {integrity: sha1-zfILohDPu0N5WvM3Vohvs2ONqsk=}
    dev: false

  /d3-brush@1.1.6:
    resolution: {integrity: sha1-sKIsc3LKvsEovd35vdwFhZL4nps=}
    dependencies:
      d3-dispatch: 1.0.6
      d3-drag: 1.2.5
      d3-interpolate: 1.4.0
      d3-selection: 1.4.2
      d3-transition: 1.3.2
    dev: false

  /d3-chord@1.0.6:
    resolution: {integrity: sha1-MJFX4/LbLHUvAoD+3TXyBnzLsV8=}
    dependencies:
      d3-array: 1.2.4
      d3-path: 1.0.9
    dev: false

  /d3-collection@1.0.7:
    resolution: {integrity: sha1-NJvSqpl32wcQkcExRNXk8WtbMQ4=}
    dev: false

  /d3-color@1.4.1:
    resolution: {integrity: sha1-xSACv4hGraRCTVXZeYL+8m6zvIo=}
    dev: false

  /d3-contour@1.3.2:
    resolution: {integrity: sha1-ZSqs1QDSJkyzQjzuENtp9vWb6tM=}
    dependencies:
      d3-array: 1.2.4
    dev: false

  /d3-dispatch@1.0.6:
    resolution: {integrity: sha1-ANN7zuTdjNl3Kd2JOgrCnKq6XVg=}
    dev: false

  /d3-drag@1.2.5:
    resolution: {integrity: sha1-JTf0UazTnTFAZne33HfIL32Yj3A=}
    dependencies:
      d3-dispatch: 1.0.6
      d3-selection: 1.4.2
    dev: false

  /d3-dsv@1.2.0:
    resolution: {integrity: sha1-nV91w6X4q9YR900/WEew1DOLiFw=}
    hasBin: true
    dependencies:
      commander: 2.20.3
      iconv-lite: 0.4.24
      rw: 1.3.3
    dev: false

  /d3-ease@1.0.7:
    resolution: {integrity: sha1-moNIkO+LiujFWLL+Vb1X9Zk7heI=}
    dev: false

  /d3-fetch@1.2.0:
    resolution: {integrity: sha1-Fc4uz8QbCSsdtQq9LFUsIxbPf8c=}
    dependencies:
      d3-dsv: 1.2.0
    dev: false

  /d3-force@1.2.1:
    resolution: {integrity: sha1-/Sml0f8YHJ5/BmnkvXK9sOkU7As=}
    dependencies:
      d3-collection: 1.0.7
      d3-dispatch: 1.0.6
      d3-quadtree: 1.0.7
      d3-timer: 1.0.10
    dev: false

  /d3-format@1.4.5:
    resolution: {integrity: sha1-N08roTIONxfrdKk1bGfa7hen7bQ=}
    dev: false

  /d3-geo@1.12.1:
    resolution: {integrity: sha1-f8KrdBS3Lln7y9YD6A2a3AKbA18=}
    dependencies:
      d3-array: 1.2.4
    dev: false

  /d3-geo@1.7.1:
    resolution: {integrity: sha1-RLvHohix/YWfPY/XxEPKg2Vpzpk=}
    dependencies:
      d3-array: 1.2.4
    dev: false

  /d3-hierarchy@1.1.9:
    resolution: {integrity: sha1-L2vuJMqupD+Nw3VF+gFihVlkeoM=}
    dev: false

  /d3-interpolate@1.4.0:
    resolution: {integrity: sha1-Um554tgNqjg/ngwcHH3MDwWD6Yc=}
    dependencies:
      d3-color: 1.4.1
    dev: false

  /d3-path@1.0.9:
    resolution: {integrity: sha1-SMBQux/owmJJOoyvVSTj6VkXAc8=}
    dev: false

  /d3-polygon@1.0.6:
    resolution: {integrity: sha1-C/jLgYCm3BB/UY3feXXhKrv7044=}
    dev: false

  /d3-quadtree@1.0.7:
    resolution: {integrity: sha1-youE33u1N2P+PC8kvUNRN/TlMTU=}
    dev: false

  /d3-random@1.1.2:
    resolution: {integrity: sha1-KDO+fBJDYL+eLT/U8zhHz+bKspE=}
    dev: false

  /d3-scale-chromatic@1.5.0:
    resolution: {integrity: sha1-VOMz/HghL0ObFGQftVgB3YETWpg=}
    dependencies:
      d3-color: 1.4.1
      d3-interpolate: 1.4.0
    dev: false

  /d3-scale@2.2.2:
    resolution: {integrity: sha1-TogOCydFrKrd0+3iap6Qip4XuB8=}
    dependencies:
      d3-array: 1.2.4
      d3-collection: 1.0.7
      d3-format: 1.4.5
      d3-interpolate: 1.4.0
      d3-time: 1.1.0
      d3-time-format: 2.3.0
    dev: false

  /d3-selection@1.4.2:
    resolution: {integrity: sha1-3KpJUiwNvzLWwYWK/Ca2CUVVvFw=}
    dev: false

  /d3-shape@1.3.7:
    resolution: {integrity: sha1-32OAG+B7yYa8VPY3ibT+UCmStdc=}
    dependencies:
      d3-path: 1.0.9
    dev: false

  /d3-time-format@2.3.0:
    resolution: {integrity: sha1-EHvcAoZneIqJJLoED68fvM1aeFA=}
    dependencies:
      d3-time: 1.1.0
    dev: false

  /d3-time@1.1.0:
    resolution: {integrity: sha1-seGdMH2unJALflsl/8XcwkmooPE=}
    dev: false

  /d3-timer@1.0.10:
    resolution: {integrity: sha1-3+dripF0iDGxO22ceT/71QjdneU=}
    dev: false

  /d3-transition@1.3.2:
    resolution: {integrity: sha1-qY7yFRvo2GAFQ0NMHKgBQK4js5g=}
    dependencies:
      d3-color: 1.4.1
      d3-dispatch: 1.0.6
      d3-ease: 1.0.7
      d3-interpolate: 1.4.0
      d3-selection: 1.4.2
      d3-timer: 1.0.10
    dev: false

  /d3-voronoi@1.1.2:
    resolution: {integrity: sha1-Fodmfo8TotFYyAwUgMWinLDYlzw=}
    dev: false

  /d3-voronoi@1.1.4:
    resolution: {integrity: sha1-3Tx412U9K7NZKErkeGRdlZRMgpc=}
    dev: false

  /d3-zoom@1.8.3:
    resolution: {integrity: sha1-tqPb5zjHdjEhzQW4p3lf/hf0/Ao=}
    dependencies:
      d3-dispatch: 1.0.6
      d3-drag: 1.2.5
      d3-interpolate: 1.4.0
      d3-selection: 1.4.2
      d3-transition: 1.3.2
    dev: false

  /d3@5.9.7:
    resolution: {integrity: sha1-84NWSKFytDjontV+tsUlvavc19w=}
    dependencies:
      d3-array: 1.2.4
      d3-axis: 1.0.12
      d3-brush: 1.1.6
      d3-chord: 1.0.6
      d3-collection: 1.0.7
      d3-color: 1.4.1
      d3-contour: 1.3.2
      d3-dispatch: 1.0.6
      d3-drag: 1.2.5
      d3-dsv: 1.2.0
      d3-ease: 1.0.7
      d3-fetch: 1.2.0
      d3-force: 1.2.1
      d3-format: 1.4.5
      d3-geo: 1.12.1
      d3-hierarchy: 1.1.9
      d3-interpolate: 1.4.0
      d3-path: 1.0.9
      d3-polygon: 1.0.6
      d3-quadtree: 1.0.7
      d3-random: 1.1.2
      d3-scale: 2.2.2
      d3-scale-chromatic: 1.5.0
      d3-selection: 1.4.2
      d3-shape: 1.3.7
      d3-time: 1.1.0
      d3-time-format: 2.3.0
      d3-timer: 1.0.10
      d3-transition: 1.3.2
      d3-voronoi: 1.1.4
      d3-zoom: 1.8.3
    dev: false

  /d@1.0.2:
    resolution: {integrity: sha512-MOqHvMWF9/9MX6nza0KgvFH4HpMU0EF5uUDXqX/BtxtU8NfB0QzRtJ8Oe/6SuS4kbhyzVJwjd97EA4PKrzJ8bw==}
    engines: {node: '>=0.12'}
    dependencies:
      es5-ext: 0.10.64
      type: 2.7.3
    dev: false

  /dayjs@1.11.7:
    resolution: {integrity: sha512-+Yw9U6YO5TQohxLcIkrXBeY73WP3ejHWVvx8XCk3gxvQDCTEmS48ZrSZCKciI7Bhl/uCMyxYtE9UqRILmFphkQ==}
    dev: false

  /debug@2.6.9:
    resolution: {integrity: sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.0.0

  /debug@3.2.7:
    resolution: {integrity: sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3
    dev: true

  /debug@4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
    dev: true

  /decode-uri-component@0.2.2:
    resolution: {integrity: sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==}
    engines: {node: '>=0.10'}

  /deep-is@0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=}
    dev: true

  /define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1
    dev: false

  /define-properties@1.2.0:
    resolution: {integrity: sha512-xvqAVKGfT1+UAvPwKTVw/njhdQ8ZhXK4lI0bCIuCMrp2up9nPnaDftrLtmpTazqd1o+UY4zgzU+avtMbDP+ldA==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-property-descriptors: 1.0.0
      object-keys: 1.1.1

  /define-property@0.2.5:
    resolution: {integrity: sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 0.1.6

  /define-property@1.0.0:
    resolution: {integrity: sha1-dp66rz9KY6rTr56NMEybvnm/sOY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.2

  /define-property@2.0.2:
    resolution: {integrity: sha1-1Flono1lS6d+AqgX+HENcCyxbp0=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.2
      isobject: 3.0.1

  /delayed-stream@1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=}
    engines: {node: '>=0.4.0'}
    dev: false

  /des.js@1.1.0:
    resolution: {integrity: sha512-r17GxjhUCjSRy8aiJpr8/UadFIzMzJGexI3Nmz4ADi9LYSFx4gTBp80+NaX/YsXWWLhpZ7v/v/ubEc/bCNfKwg==}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: false

  /diffie-hellman@5.0.3:
    resolution: {integrity: sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=}
    dependencies:
      bn.js: 4.12.0
      miller-rabin: 4.0.1
      randombytes: 2.1.0
    dev: false

  /doctrine@2.1.0:
    resolution: {integrity: sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=}
    engines: {node: '>=0.10.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /doctrine@3.0.0:
    resolution: {integrity: sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=}
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /dom-serializer@0.2.2:
    resolution: {integrity: sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=}
    dependencies:
      domelementtype: 2.3.0
      entities: 2.2.0
    dev: true

  /dom-serializer@1.4.1:
    resolution: {integrity: sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0
    dev: true

  /dom-to-image@2.6.0:
    resolution: {integrity: sha1-ilA2CAiMh7HCL5A0rgMuGJiVWGc=}
    dev: false

  /domain-browser@1.2.0:
    resolution: {integrity: sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=}
    engines: {node: '>=0.4', npm: '>=1.2'}
    dev: false

  /domelementtype@1.3.1:
    resolution: {integrity: sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=}
    dev: true

  /domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}
    dev: true

  /domhandler@2.4.2:
    resolution: {integrity: sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=}
    dependencies:
      domelementtype: 1.3.1
    dev: true

  /domhandler@4.3.1:
    resolution: {integrity: sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.3.0
    dev: true

  /domutils@1.7.0:
    resolution: {integrity: sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=}
    dependencies:
      dom-serializer: 0.2.2
      domelementtype: 1.3.1
    dev: true

  /domutils@2.8.0:
    resolution: {integrity: sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=}
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1
    dev: true

  /duplexify@3.7.1:
    resolution: {integrity: sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=}
    dependencies:
      end-of-stream: 1.4.4
      inherits: 2.0.4
      readable-stream: 2.3.8
      stream-shift: 1.0.3
    dev: false

  /earcut@2.2.4:
    resolution: {integrity: sha512-/pjZsA1b4RPHbeWZQn66SWS8nZZWLQQ23oE3Eam7aroEFGEvwKAsJfZ9ytiEMycfzXWpca4FA9QIOehf7PocBQ==}
    dev: false

  /echarts@5.5.0:
    resolution: {integrity: sha512-rNYnNCzqDAPCr4m/fqyUFv7fD9qIsd50S6GDFgO1DxZhncCsNsG7IfUlAlvZe5oSEQxtsjnHiUuppzccry93Xw==}
    dependencies:
      tslib: 2.3.0
      zrender: 5.5.0
    dev: false

  /ecp-chart@2.0.4(vue@3.4.15):
    resolution: {integrity: sha512-dhL8ZXBSBd5Nezj1q3xQibdBWY4sg6WAUWUnCMrreezkRPzSRADRqcsgsRodM1tIAAweyI0K23+bfRUBMnBauA==}
    engines: {node: '>=18'}
    peerDependencies:
      '@vue/composition-api': '>=1.0.0'
      vue: ^2.6.14 || >=3.0.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
    dependencies:
      echarts: 5.5.0
      lodash: 4.17.21
      resize-detector: 0.3.0
      v-title: 2.1.8(vue@3.4.15)
      vue: 3.4.15
      vue-demi: 0.14.10(vue@3.4.15)
    dev: false

  /electron-to-chromium@1.4.368:
    resolution: {integrity: sha512-e2aeCAixCj9M7nJxdB/wDjO6mbYX+lJJxSJCXDzlr5YPGYVofuJwGN9nKg2o6wWInjX6XmxRinn3AeJMK81ltw==}
    dev: true

  /element-plus@2.8.0(vue@3.4.15):
    resolution: {integrity: sha512-7ngapVlVlQAjocVqD4MUKvKXlBneT9DSDk2mmBOSLRFWNm/HLDT15ozmsvUBfy18sajnyUeSIHTtINE8gfrGMg==}
    peerDependencies:
      vue: ^3.2.0
    dependencies:
      '@ctrl/tinycolor': 3.6.0
      '@element-plus/icons-vue': 2.3.1(vue@3.4.15)
      '@floating-ui/dom': 1.2.6
      '@popperjs/core': /@sxzz/popperjs-es@2.11.7
      '@types/lodash': 4.14.194
      '@types/lodash-es': 4.17.7
      '@vueuse/core': 9.13.0(vue@3.4.15)
      async-validator: 4.2.5
      dayjs: 1.11.7
      escape-html: 1.0.3
      lodash: 4.17.21
      lodash-es: 4.17.21
      lodash-unified: 1.0.3(@types/lodash-es@4.17.7)(lodash-es@4.17.21)(lodash@4.17.21)
      memoize-one: 6.0.0
      normalize-wheel-es: 1.2.0
      vue: 3.4.15
    transitivePeerDependencies:
      - '@vue/composition-api'
    dev: false

  /elliptic@6.5.7:
    resolution: {integrity: sha512-ESVCtTwiA+XhY3wyh24QqRGBoP3rEdDUl3EDUUo9tft074fi19IrdpH7hLCMMP3CIj7jb3W96rn8lt/BqIlt5Q==}
    dependencies:
      bn.js: 4.12.0
      brorand: 1.1.0
      hash.js: 1.1.7
      hmac-drbg: 1.0.1
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1
    dev: false

  /emojis-list@3.0.0:
    resolution: {integrity: sha1-VXBmIEatKeLpFucariYKvf9Pang=}
    engines: {node: '>= 4'}

  /end-of-stream@1.4.4:
    resolution: {integrity: sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=}
    dependencies:
      once: 1.4.0
    dev: false

  /enhanced-resolve@4.5.0:
    resolution: {integrity: sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=}
    engines: {node: '>=6.9.0'}
    dependencies:
      graceful-fs: 4.2.11
      memory-fs: 0.5.0
      tapable: 1.1.3
    dev: false

  /entities@1.1.2:
    resolution: {integrity: sha1-vfpzUplmTfr9NFKe1PhSKidf6lY=}
    dev: true

  /entities@2.2.0:
    resolution: {integrity: sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=}
    dev: true

  /entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  /errno@0.1.8:
    resolution: {integrity: sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=}
    hasBin: true
    dependencies:
      prr: 1.0.1
    dev: false

  /es-abstract@1.21.2:
    resolution: {integrity: sha512-y/B5POM2iBnIxCiernH1G7rC9qQoM77lLIMQLuob0zhp8C56Po81+2Nj0WFKnd0pNReDTnkYryc+zhOzpEIROg==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.0
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      es-set-tostringtag: 2.0.1
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.5
      get-intrinsic: 1.2.0
      get-symbol-description: 1.0.0
      globalthis: 1.0.3
      gopd: 1.0.1
      has: 1.0.3
      has-property-descriptors: 1.0.0
      has-proto: 1.0.1
      has-symbols: 1.0.3
      internal-slot: 1.0.5
      is-array-buffer: 3.0.2
      is-callable: 1.2.7
      is-negative-zero: 2.0.2
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.2
      is-string: 1.0.7
      is-typed-array: 1.1.10
      is-weakref: 1.0.2
      object-inspect: 1.12.3
      object-keys: 1.1.1
      object.assign: 4.1.4
      regexp.prototype.flags: 1.5.0
      safe-regex-test: 1.0.0
      string.prototype.trim: 1.2.7
      string.prototype.trimend: 1.0.6
      string.prototype.trimstart: 1.0.6
      typed-array-length: 1.0.4
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.9
    dev: true

  /es-define-property@1.0.0:
    resolution: {integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.4
    dev: false

  /es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}
    dev: false

  /es-set-tostringtag@2.0.1:
    resolution: {integrity: sha512-g3OMbtlwY3QewlqAiMLI47KywjWZoEytKr8pf6iTC8uJq5bIAH52Z9pnQ8pVL6whrCto53JZDuUIsifGeLorTg==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.0
      has: 1.0.3
      has-tostringtag: 1.0.0
    dev: true

  /es-shim-unscopables@1.0.0:
    resolution: {integrity: sha512-Jm6GPcCdC30eMLbZ2x8z2WuRwAws3zTBBKuusffYVUrNj/GVSUAZ+xKMaUpfNDR5IbyNA5LJbaecoUVbmUcB1w==}
    dependencies:
      has: 1.0.3
    dev: true

  /es-to-primitive@1.2.1:
    resolution: {integrity: sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4
    dev: true

  /es5-ext@0.10.64:
    resolution: {integrity: sha512-p2snDhiLaXe6dahss1LddxqEm+SkuDvV8dnIQG0MWjyHpcMNfXKPE+/Cc0y+PhxJX3A4xGNeFCj5oc0BUh6deg==}
    engines: {node: '>=0.10'}
    requiresBuild: true
    dependencies:
      es6-iterator: 2.0.3
      es6-symbol: 3.1.4
      esniff: 2.0.1
      next-tick: 1.1.0
    dev: false

  /es6-iterator@2.0.3:
    resolution: {integrity: sha1-p96IkUGgWpSwhUQDstCg+/qY87c=}
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64
      es6-symbol: 3.1.4
    dev: false

  /es6-map@0.1.5:
    resolution: {integrity: sha1-kTbgUD3MBqMBaQ8LsU/042TpSfA=}
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64
      es6-iterator: 2.0.3
      es6-set: 0.1.6
      es6-symbol: 3.1.4
      event-emitter: 0.3.5
    dev: false

  /es6-promise@4.2.8:
    resolution: {integrity: sha1-TrIVlMlyvEBVPSduUQU5FD21Pgo=}
    dev: false

  /es6-set@0.1.6:
    resolution: {integrity: sha512-TE3LgGLDIBX332jq3ypv6bcOpkLO0AslAQo7p2VqX/1N46YNsvIWgvjojjSEnWEGWMhr1qUbYeTSir5J6mFHOw==}
    engines: {node: '>=0.12'}
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64
      es6-iterator: 2.0.3
      es6-symbol: 3.1.4
      event-emitter: 0.3.5
      type: 2.7.3
    dev: false

  /es6-symbol@3.1.4:
    resolution: {integrity: sha512-U9bFFjX8tFiATgtkJ1zg25+KviIXpgRvRHS8sau3GfhVzThRQrOeksPeT0BWW2MNZs1OEWJ1DPXOQMn0KKRkvg==}
    engines: {node: '>=0.12'}
    dependencies:
      d: 1.0.2
      ext: 1.7.0
    dev: false

  /esbuild@0.18.20:
    resolution: {integrity: sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA==}
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/android-arm': 0.18.20
      '@esbuild/android-arm64': 0.18.20
      '@esbuild/android-x64': 0.18.20
      '@esbuild/darwin-arm64': 0.18.20
      '@esbuild/darwin-x64': 0.18.20
      '@esbuild/freebsd-arm64': 0.18.20
      '@esbuild/freebsd-x64': 0.18.20
      '@esbuild/linux-arm': 0.18.20
      '@esbuild/linux-arm64': 0.18.20
      '@esbuild/linux-ia32': 0.18.20
      '@esbuild/linux-loong64': 0.18.20
      '@esbuild/linux-mips64el': 0.18.20
      '@esbuild/linux-ppc64': 0.18.20
      '@esbuild/linux-riscv64': 0.18.20
      '@esbuild/linux-s390x': 0.18.20
      '@esbuild/linux-x64': 0.18.20
      '@esbuild/netbsd-x64': 0.18.20
      '@esbuild/openbsd-x64': 0.18.20
      '@esbuild/sunos-x64': 0.18.20
      '@esbuild/win32-arm64': 0.18.20
      '@esbuild/win32-ia32': 0.18.20
      '@esbuild/win32-x64': 0.18.20
    dev: true

  /escalade@3.1.1:
    resolution: {integrity: sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=}
    engines: {node: '>=6'}
    dev: true

  /escape-html@1.0.3:
    resolution: {integrity: sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=}
    dev: false

  /escape-string-regexp@1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=}
    engines: {node: '>=0.8.0'}
    dev: true

  /escape-string-regexp@4.0.0:
    resolution: {integrity: sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=}
    engines: {node: '>=10'}
    dev: true

  /escape-string-regexp@5.0.0:
    resolution: {integrity: sha1-RoMSa1ALYXYvLb66zhgG6L4xscg=}
    engines: {node: '>=12'}
    dev: true

  /eslint-config-standard@17.0.0(eslint-plugin-import@2.27.5)(eslint-plugin-n@15.7.0)(eslint-plugin-promise@6.1.1)(eslint@8.38.0):
    resolution: {integrity: sha512-/2ks1GKyqSOkH7JFvXJicu0iMpoojkwB+f5Du/1SC0PtBL+s8v30k9njRZ21pm2drKYm2342jFnGWzttxPmZVg==}
    peerDependencies:
      eslint: ^8.0.1
      eslint-plugin-import: ^2.25.2
      eslint-plugin-n: ^15.0.0
      eslint-plugin-promise: ^6.0.0
    dependencies:
      eslint: 8.38.0
      eslint-plugin-import: 2.27.5(eslint@8.38.0)
      eslint-plugin-n: 15.7.0(eslint@8.38.0)
      eslint-plugin-promise: 6.1.1(eslint@8.38.0)
    dev: true

  /eslint-import-resolver-node@0.3.7:
    resolution: {integrity: sha512-gozW2blMLJCeFpBwugLTGyvVjNoeo1knonXAcatC6bjPBZitotxdWf7Gimr25N4c0AAOo4eOUfaG82IJPDpqCA==}
    dependencies:
      debug: 3.2.7
      is-core-module: 2.12.0
      resolve: 1.22.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-module-utils@2.8.0(eslint-import-resolver-node@0.3.7)(eslint@8.38.0):
    resolution: {integrity: sha512-aWajIYfsqCKRDgUfjEXNN/JlrzauMuSEy5sbd7WXbtW3EH6A6MpwEh42c7qD+MqQo9QMJ6fWLAeIJynx0g6OAw==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true
    dependencies:
      debug: 3.2.7
      eslint: 8.38.0
      eslint-import-resolver-node: 0.3.7
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-plugin-es@4.1.0(eslint@8.38.0):
    resolution: {integrity: sha1-8IIvDBilNal8PnFOifiFhqdkHsk=}
    engines: {node: '>=8.10.0'}
    peerDependencies:
      eslint: '>=4.19.1'
    dependencies:
      eslint: 8.38.0
      eslint-utils: 2.1.0
      regexpp: 3.2.0
    dev: true

  /eslint-plugin-import@2.27.5(eslint@8.38.0):
    resolution: {integrity: sha512-LmEt3GVofgiGuiE+ORpnvP+kAm3h6MLZJ4Q5HCyHADofsb4VzXFsRiWj3c0OFiV+3DWFh0qg3v9gcPlfc3zRow==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
    dependencies:
      array-includes: 3.1.6
      array.prototype.flat: 1.3.1
      array.prototype.flatmap: 1.3.1
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 8.38.0
      eslint-import-resolver-node: 0.3.7
      eslint-module-utils: 2.8.0(eslint-import-resolver-node@0.3.7)(eslint@8.38.0)
      has: 1.0.3
      is-core-module: 2.12.0
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.values: 1.1.6
      resolve: 1.22.3
      semver: 6.3.0
      tsconfig-paths: 3.14.2
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color
    dev: true

  /eslint-plugin-n@15.7.0(eslint@8.38.0):
    resolution: {integrity: sha512-jDex9s7D/Qial8AGVIHq4W7NswpUD5DPDL2RH8Lzd9EloWUuvUkHfv4FRLMipH5q2UtyurorBkPeNi1wVWNh3Q==}
    engines: {node: '>=12.22.0'}
    peerDependencies:
      eslint: '>=7.0.0'
    dependencies:
      builtins: 5.0.1
      eslint: 8.38.0
      eslint-plugin-es: 4.1.0(eslint@8.38.0)
      eslint-utils: 3.0.0(eslint@8.38.0)
      ignore: 5.2.4
      is-core-module: 2.12.0
      minimatch: 3.1.2
      resolve: 1.22.3
      semver: 7.5.0
    dev: true

  /eslint-plugin-promise@6.1.1(eslint@8.38.0):
    resolution: {integrity: sha512-tjqWDwVZQo7UIPMeDReOpUgHCmCiH+ePnVT+5zVapL0uuHnegBUs2smM13CzOs2Xb5+MHMRFTs9v24yjba4Oig==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
    dependencies:
      eslint: 8.38.0
    dev: true

  /eslint-plugin-vue@9.11.0(eslint@8.38.0):
    resolution: {integrity: sha512-bBCJAZnkBV7ATH4Z1E7CvN3nmtS4H7QUU3UBxPdo8WohRU+yHjnQRALpTbxMVcz0e4Mx3IyxIdP5HYODMxK9cQ==}
    engines: {node: ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.2.0 || ^7.0.0 || ^8.0.0
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@8.38.0)
      eslint: 8.38.0
      natural-compare: 1.4.0
      nth-check: 2.1.1
      postcss-selector-parser: 6.0.11
      semver: 7.5.0
      vue-eslint-parser: 9.1.1(eslint@8.38.0)
      xml-name-validator: 4.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-scope@4.0.3:
    resolution: {integrity: sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=}
    engines: {node: '>=4.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0
    dev: false

  /eslint-scope@7.2.0:
    resolution: {integrity: sha512-DYj5deGlHBfMt15J7rdtyKNq/Nqlv5KfU4iodrQ019XESsRnwXH9KAE0y3cwtUHDo2ob7CypAnCqefh6vioWRw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0
    dev: true

  /eslint-utils@2.1.0:
    resolution: {integrity: sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=}
    engines: {node: '>=6'}
    dependencies:
      eslint-visitor-keys: 1.3.0
    dev: true

  /eslint-utils@3.0.0(eslint@8.38.0):
    resolution: {integrity: sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=}
    engines: {node: ^10.0.0 || ^12.0.0 || >= 14.0.0}
    peerDependencies:
      eslint: '>=5'
    dependencies:
      eslint: 8.38.0
      eslint-visitor-keys: 2.1.0
    dev: true

  /eslint-visitor-keys@1.3.0:
    resolution: {integrity: sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=}
    engines: {node: '>=4'}
    dev: true

  /eslint-visitor-keys@2.1.0:
    resolution: {integrity: sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=}
    engines: {node: '>=10'}
    dev: true

  /eslint-visitor-keys@3.4.0:
    resolution: {integrity: sha512-HPpKPUBQcAsZOsHAFwTtIKcYlCje62XB7SEAcxjtmW6TD1WVpkS6i6/hOVtTZIl4zGj/mBqpFVGvaDneik+VoQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /eslint@8.38.0:
    resolution: {integrity: sha512-pIdsD2jwlUGf/U38Jv97t8lq6HpaU/G9NKbYmpWpZGw3LdTNhZLbJePqxOXGB5+JEKfOPU/XLxYxFh03nr1KTg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    hasBin: true
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@8.38.0)
      '@eslint-community/regexpp': 4.5.0
      '@eslint/eslintrc': 2.0.2
      '@eslint/js': 8.38.0
      '@humanwhocodes/config-array': 0.11.8
      '@humanwhocodes/module-importer': 1.0.1
      '@nodelib/fs.walk': 1.2.8
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.3
      debug: 4.3.4
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.0
      eslint-visitor-keys: 3.4.0
      espree: 9.5.1
      esquery: 1.5.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.20.0
      grapheme-splitter: 1.0.4
      ignore: 5.2.4
      import-fresh: 3.3.0
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-sdsl: 4.4.0
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.1
      strip-ansi: 6.0.1
      strip-json-comments: 3.1.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /esniff@2.0.1:
    resolution: {integrity: sha512-kTUIGKQ/mDPFoJ0oVfcmyJn4iBDRptjNVIzwIFR7tqWXdVI9xfA2RMwY/gbSpJG3lkdWNEjLap/NqVHZiJsdfg==}
    engines: {node: '>=0.10'}
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64
      event-emitter: 0.3.5
      type: 2.7.3
    dev: false

  /espree@9.5.1:
    resolution: {integrity: sha512-5yxtHSZXRSW5pvv3hAlXM5+/Oswi1AUFqBmbibKb5s6bp3rGIDkyXU6xCoyuuLhijr4SFwPrXRoZjz0AZDN9tg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      acorn: 8.11.2
      acorn-jsx: 5.3.2(acorn@8.11.2)
      eslint-visitor-keys: 3.4.0
    dev: true

  /esquery@1.5.0:
    resolution: {integrity: sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==}
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /esrecurse@4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0

  /estraverse@4.3.0:
    resolution: {integrity: sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=}
    engines: {node: '>=4.0'}
    dev: false

  /estraverse@5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=}
    engines: {node: '>=4.0'}

  /estree-walker@2.0.2:
    resolution: {integrity: sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=}

  /esutils@2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=}
    engines: {node: '>=0.10.0'}
    dev: true

  /etag@1.8.1:
    resolution: {integrity: sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=}
    engines: {node: '>= 0.6'}
    dev: true

  /eve-raphael@0.5.0:
    resolution: {integrity: sha1-F8dUt5K+7z+maE15z1pHxjxM2jA=}
    dev: false

  /event-emitter@0.3.5:
    resolution: {integrity: sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk=}
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64
    dev: false

  /events@3.3.0:
    resolution: {integrity: sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=}
    engines: {node: '>=0.8.x'}
    dev: false

  /evp_bytestokey@1.0.3:
    resolution: {integrity: sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=}
    dependencies:
      md5.js: 1.3.5
      safe-buffer: 5.2.1
    dev: false

  /execa@5.1.1:
    resolution: {integrity: sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: true

  /expand-brackets@2.1.4:
    resolution: {integrity: sha1-t3c14xXOMPa27/D4OwQVGiJEliI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      posix-character-classes: 0.1.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  /ext@1.7.0:
    resolution: {integrity: sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw==}
    dependencies:
      type: 2.7.3
    dev: false

  /extend-shallow@2.0.1:
    resolution: {integrity: sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: 0.1.1

  /extend-shallow@3.0.2:
    resolution: {integrity: sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      assign-symbols: 1.0.0
      is-extendable: 1.0.1

  /extglob@2.0.4:
    resolution: {integrity: sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-unique: 0.3.2
      define-property: 1.0.0
      expand-brackets: 2.1.4
      extend-shallow: 2.0.1
      fragment-cache: 0.2.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  /falafel@0.3.1:
    resolution: {integrity: sha1-81RnSIFPfQlUPRny+z1gkPE2hT0=}
    engines: {node: '>=0.4.0'}
    dev: false
    bundledDependencies:
      - esprima

  /fast-deep-equal@3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=}

  /fast-glob@3.2.12:
    resolution: {integrity: sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5
    dev: true

  /fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=}

  /fast-levenshtein@2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=}
    dev: true

  /fastq@1.15.0:
    resolution: {integrity: sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==}
    dependencies:
      reusify: 1.0.4
    dev: true

  /fetch-ie8@1.5.0:
    resolution: {integrity: sha1-8RQcP5bLyJN6oxsPvBp3AiD7wVs=}
    dev: false

  /fetch-jsonp@1.3.0:
    resolution: {integrity: sha512-hxCYGvmANEmpkHpeWY8Kawfa5Z1t2csTpIClIDG/0S92eALWHRU1RnGaj86Tf5Cc0QF+afSa4SQ4pFB2rFM5QA==}
    dev: false

  /figgy-pudding@3.5.2:
    resolution: {integrity: sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=}
    dev: false

  /file-entry-cache@6.0.1:
    resolution: {integrity: sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flat-cache: 3.0.4
    dev: true

  /file-loader@4.3.0(webpack@4.47.0):
    resolution: {integrity: sha1-eA8ED3KbPRgBnyBgX3I+hEuKWK8=}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      loader-utils: 1.4.2
      schema-utils: 2.7.1
      webpack: 4.47.0
    dev: false

  /file-uri-to-path@1.0.0:
    resolution: {integrity: sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=}
    requiresBuild: true
    dev: false
    optional: true

  /fill-range@4.0.0:
    resolution: {integrity: sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-number: 3.0.0
      repeat-string: 1.6.1
      to-regex-range: 2.1.1

  /fill-range@7.0.1:
    resolution: {integrity: sha1-GRmmp8df44ssfHflGYU12prN2kA=}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1

  /find-cache-dir@2.1.0:
    resolution: {integrity: sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=}
    engines: {node: '>=6'}
    dependencies:
      commondir: 1.0.1
      make-dir: 2.1.0
      pkg-dir: 3.0.0
    dev: false

  /find-up@3.0.0:
    resolution: {integrity: sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=}
    engines: {node: '>=6'}
    dependencies:
      locate-path: 3.0.0
    dev: false

  /find-up@5.0.0:
    resolution: {integrity: sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=}
    engines: {node: '>=10'}
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0
    dev: true

  /flat-cache@3.0.4:
    resolution: {integrity: sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flatted: 3.2.7
      rimraf: 3.0.2
    dev: true

  /flatted@3.2.7:
    resolution: {integrity: sha512-5nqDSxl8nn5BSNxyR3n4I6eDmbolI6WT+QqR547RwxQapgjQBmtktdP+HTBb/a/zLsbzERTONyUB5pefh5TtjQ==}
    dev: true

  /flush-write-stream@1.1.1:
    resolution: {integrity: sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: false

  /follow-redirects@1.15.2:
    resolution: {integrity: sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: false

  /for-each@0.3.3:
    resolution: {integrity: sha1-abRH6IoKXTLD5whPPxcQA0shN24=}
    dependencies:
      is-callable: 1.2.7
    dev: true

  /for-in@1.0.2:
    resolution: {integrity: sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=}
    engines: {node: '>=0.10.0'}

  /form-data@4.0.0:
    resolution: {integrity: sha1-k5Gdrq82HuUpWEubMWZNwSyfpFI=}
    engines: {node: '>= 6'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35
    dev: false

  /fragment-cache@0.2.1:
    resolution: {integrity: sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-cache: 0.2.2

  /from2@2.3.0:
    resolution: {integrity: sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: false

  /fs-extra@10.1.0:
    resolution: {integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==}
    engines: {node: '>=12'}
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.0
    dev: true

  /fs-write-stream-atomic@1.0.10:
    resolution: {integrity: sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=}
    dependencies:
      graceful-fs: 4.2.11
      iferr: 0.1.5
      imurmurhash: 0.1.4
      readable-stream: 2.3.8
    dev: false

  /fs.realpath@1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=}

  /fsevents@1.2.13:
    resolution: {integrity: sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=}
    engines: {node: '>= 4.0'}
    os: [darwin]
    requiresBuild: true
    dependencies:
      bindings: 1.5.0
      nan: 2.20.0
    dev: false
    optional: true

  /fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    optional: true

  /function-bind@1.1.1:
    resolution: {integrity: sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=}

  /function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}
    dev: false

  /function.prototype.name@1.1.5:
    resolution: {integrity: sha512-uN7m/BzVKQnCUF/iW8jYea67v++2u7m5UgENbHRtdDVclOUP+FMPlCNdmk0h/ysGyo2tavMJEDqJAkJdRa1vMA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
      functions-have-names: 1.2.3
    dev: true

  /functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}
    dev: true

  /gensync@1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=}
    engines: {node: '>=6.9.0'}
    dev: true

  /geojson-equality-ts@1.0.2:
    resolution: {integrity: sha512-h3Ryq+0mCSN/7yLs0eDgrZhvc9af23o/QuC4aTiuuzP/MRCtd6mf5rLsLRY44jX0RPUfM8c4GqERQmlUxPGPoQ==}
    dependencies:
      '@types/geojson': 7946.0.14
    dev: false

  /geojson-polygon-self-intersections@1.2.1:
    resolution: {integrity: sha512-/QM1b5u2d172qQVO//9CGRa49jEmclKEsYOQmWP9ooEjj63tBM51m2805xsbxkzlEELQ2REgTf700gUhhlegxA==}
    dependencies:
      rbush: 2.0.2
    dev: false

  /geojson-rbush@3.2.0:
    resolution: {integrity: sha1-i1Q88NVvmbePrx2lK7ZqytbfwpA=}
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
      '@types/geojson': 7946.0.8
      rbush: 3.0.1
    dev: false

  /get-intrinsic@1.2.0:
    resolution: {integrity: sha512-L049y6nFOuom5wGyRc3/gdTLO94dySVKRACj1RmJZBQXlbTMhtNIgkWkUHq+jYmZvKf14EW1EoJnnjbmoHij0Q==}
    dependencies:
      function-bind: 1.1.1
      has: 1.0.3
      has-symbols: 1.0.3

  /get-intrinsic@1.2.4:
    resolution: {integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.1
      has-symbols: 1.0.3
      hasown: 2.0.2
    dev: false

  /get-stream@6.0.1:
    resolution: {integrity: sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=}
    engines: {node: '>=10'}
    dev: true

  /get-symbol-description@1.0.0:
    resolution: {integrity: sha1-f9uByQAQH71WTdXxowr1qtweWNY=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.0
    dev: true

  /get-value@2.0.6:
    resolution: {integrity: sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=}
    engines: {node: '>=0.10.0'}

  /glob-parent@3.1.0:
    resolution: {integrity: sha512-E8Ak/2+dZY6fnzlR7+ueWvhsH1SjHr4jjss4YS/h4py44jY9MhK/VFdaZJAWDz6BbL21KeteKxFSFpq8OS5gVA==}
    requiresBuild: true
    dependencies:
      is-glob: 3.1.0
      path-dirname: 1.0.2
    dev: false
    optional: true

  /glob-parent@5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3

  /glob-parent@6.0.2:
    resolution: {integrity: sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=}
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  /globals@11.12.0:
    resolution: {integrity: sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=}
    engines: {node: '>=4'}
    dev: true

  /globals@13.20.0:
    resolution: {integrity: sha512-Qg5QtVkCy/kv3FUSlu4ukeZDVf9ee0iXLAUYX13gbR17bnejFTzr4iS9bY7kwCf1NztRNm1t91fjOiyx4CSwPQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.20.2
    dev: true

  /globalthis@1.0.3:
    resolution: {integrity: sha512-sFdI5LyBiNTHjRd7cGPWapiHWMOXKyuBNX/cWJ3NfzrZQVa8GI/8cofCl74AOVqq9W5kNmguTIzJ/1s2gyI9wA==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-properties: 1.2.0
    dev: true

  /gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}
    dependencies:
      get-intrinsic: 1.2.0

  /graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  /grapheme-splitter@1.0.4:
    resolution: {integrity: sha512-bzh50DW9kTPM00T8y4o8vQg89Di9oLJVLW/KaOGIXJWP/iqCN6WKYkbNOF04vFLJhwcpYUh9ydh/+5vpOqV4YQ==}
    dev: true

  /has-ansi@2.0.0:
    resolution: {integrity: sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: true

  /has-bigints@1.0.2:
    resolution: {integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==}
    dev: true

  /has-flag@1.0.0:
    resolution: {integrity: sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=}
    engines: {node: '>=0.10.0'}
    dev: true

  /has-flag@3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=}
    engines: {node: '>=4'}
    dev: true

  /has-flag@4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=}
    engines: {node: '>=8'}
    dev: true

  /has-property-descriptors@1.0.0:
    resolution: {integrity: sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==}
    dependencies:
      get-intrinsic: 1.2.0

  /has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}
    dependencies:
      es-define-property: 1.0.0
    dev: false

  /has-proto@1.0.1:
    resolution: {integrity: sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==}
    engines: {node: '>= 0.4'}

  /has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}

  /has-tostringtag@1.0.0:
    resolution: {integrity: sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: true

  /has-value@0.3.1:
    resolution: {integrity: sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 0.1.4
      isobject: 2.1.0

  /has-value@1.0.0:
    resolution: {integrity: sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 1.0.0
      isobject: 3.0.1

  /has-values@0.1.4:
    resolution: {integrity: sha1-bWHeldkd/Km5oCCJrThL/49it3E=}
    engines: {node: '>=0.10.0'}

  /has-values@1.0.0:
    resolution: {integrity: sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      kind-of: 4.0.0

  /has@1.0.3:
    resolution: {integrity: sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=}
    engines: {node: '>= 0.4.0'}
    dependencies:
      function-bind: 1.1.1

  /hash-base@3.0.4:
    resolution: {integrity: sha1-X8hoaEfs1zSZQDMZprCj8/auSRg=}
    engines: {node: '>=4'}
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /hash-base@3.1.0:
    resolution: {integrity: sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=}
    engines: {node: '>=4'}
    dependencies:
      inherits: 2.0.4
      readable-stream: 3.6.2
      safe-buffer: 5.2.1
    dev: false

  /hash.js@1.1.7:
    resolution: {integrity: sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: false

  /hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: 1.1.2
    dev: false

  /he@1.2.0:
    resolution: {integrity: sha1-hK5l+n6vsWX922FWauFLrwVmTw8=}
    hasBin: true
    dev: true

  /hmac-drbg@1.0.1:
    resolution: {integrity: sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=}
    dependencies:
      hash.js: 1.1.7
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1
    dev: false

  /htmlparser2@3.10.1:
    resolution: {integrity: sha1-vWedw/WYl7ajS7EHSchVu1OpOS8=}
    dependencies:
      domelementtype: 1.3.1
      domhandler: 2.4.2
      domutils: 1.7.0
      entities: 1.1.2
      inherits: 2.0.4
      readable-stream: 3.6.2
    dev: true

  /https-browserify@1.0.0:
    resolution: {integrity: sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=}
    dev: false

  /human-signals@2.1.0:
    resolution: {integrity: sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=}
    engines: {node: '>=10.17.0'}
    dev: true

  /iconv-lite@0.4.24:
    resolution: {integrity: sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /ieee754@1.2.1:
    resolution: {integrity: sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=}
    dev: false

  /iferr@0.1.5:
    resolution: {integrity: sha1-xg7taebY/bazEEofy8ocGS3FtQE=}
    dev: false

  /ignore@5.2.4:
    resolution: {integrity: sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==}
    engines: {node: '>= 4'}
    dev: true

  /image-size@0.5.5:
    resolution: {integrity: sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dev: true

  /immutable@4.3.0:
    resolution: {integrity: sha512-0AOCmOip+xgJwEVTQj1EfiDDOkPmuyllDuTuEX+DDXUgapLAsBIfkg3sxCYyCEA8mQqZrrxPUGjcOQ2JS3WLkg==}
    dev: true

  /import-fresh@3.3.0:
    resolution: {integrity: sha1-NxYsJfy566oublPVtNiM4X2eDCs=}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: true

  /imurmurhash@0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=}
    engines: {node: '>=0.8.19'}

  /infer-owner@1.0.4:
    resolution: {integrity: sha1-xM78qo5RBRwqQLos6KPScpWvlGc=}
    dev: false

  /inflight@1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=}
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  /inherits@2.0.3:
    resolution: {integrity: sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=}
    dev: false

  /inherits@2.0.4:
    resolution: {integrity: sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=}

  /internal-slot@1.0.5:
    resolution: {integrity: sha512-Y+R5hJrzs52QCG2laLn4udYVnxsfny9CpOhNhUvk/SSSVyF6T27FzRbF0sroPidSu3X8oEAkOn2K804mjpt6UQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.0
      has: 1.0.3
      side-channel: 1.0.4
    dev: true

  /is-accessor-descriptor@0.1.6:
    resolution: {integrity: sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /is-accessor-descriptor@1.0.0:
    resolution: {integrity: sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 6.0.3

  /is-array-buffer@3.0.2:
    resolution: {integrity: sha512-y+FyyR/w8vfIRq4eQcM1EYgSTnmHXPqaF+IgzgraytCFq5Xh8lllDVmAZolPJiZttZLeFSINPYMaEJ7/vWUa1w==}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.0
      is-typed-array: 1.1.10
    dev: true

  /is-bigint@1.0.4:
    resolution: {integrity: sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=}
    dependencies:
      has-bigints: 1.0.2
    dev: true

  /is-binary-path@1.0.1:
    resolution: {integrity: sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    dependencies:
      binary-extensions: 1.13.1
    dev: false
    optional: true

  /is-binary-path@2.1.0:
    resolution: {integrity: sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.2.0

  /is-boolean-object@1.1.2:
    resolution: {integrity: sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: true

  /is-buffer@1.1.6:
    resolution: {integrity: sha1-76ouqdqg16suoTqXsritUf776L4=}

  /is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-core-module@2.12.0:
    resolution: {integrity: sha512-RECHCBCd/viahWmwj6enj19sKbHfJrddi/6cBDsNTKbNq0f7VeaUkBo60BqzvPqo/W54ChS62Z5qyun7cfOMqQ==}
    dependencies:
      has: 1.0.3
    dev: true

  /is-data-descriptor@0.1.4:
    resolution: {integrity: sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /is-data-descriptor@1.0.0:
    resolution: {integrity: sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 6.0.3

  /is-date-object@1.0.5:
    resolution: {integrity: sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: true

  /is-descriptor@0.1.6:
    resolution: {integrity: sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-accessor-descriptor: 0.1.6
      is-data-descriptor: 0.1.4
      kind-of: 5.1.0

  /is-descriptor@1.0.2:
    resolution: {integrity: sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-accessor-descriptor: 1.0.0
      is-data-descriptor: 1.0.0
      kind-of: 6.0.3

  /is-extendable@0.1.1:
    resolution: {integrity: sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=}
    engines: {node: '>=0.10.0'}

  /is-extendable@1.0.1:
    resolution: {integrity: sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-object: 2.0.4

  /is-extglob@2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=}
    engines: {node: '>=0.10.0'}

  /is-glob@3.1.0:
    resolution: {integrity: sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    dependencies:
      is-extglob: 2.1.1
    dev: false
    optional: true

  /is-glob@4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1

  /is-negative-zero@2.0.2:
    resolution: {integrity: sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-number-object@1.0.7:
    resolution: {integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: true

  /is-number@3.0.0:
    resolution: {integrity: sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /is-number@7.0.0:
    resolution: {integrity: sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=}
    engines: {node: '>=0.12.0'}

  /is-path-inside@3.0.3:
    resolution: {integrity: sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=}
    engines: {node: '>=8'}
    dev: true

  /is-plain-obj@1.1.0:
    resolution: {integrity: sha1-caUMhCnfync8kqOQpKA7OfzVHT4=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-plain-object@2.0.4:
    resolution: {integrity: sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1

  /is-regex@1.1.4:
    resolution: {integrity: sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: true

  /is-shared-array-buffer@1.0.2:
    resolution: {integrity: sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==}
    dependencies:
      call-bind: 1.0.2
    dev: true

  /is-stream@2.0.1:
    resolution: {integrity: sha1-+sHj1TuXrVqdCunO8jifWBClwHc=}
    engines: {node: '>=8'}
    dev: true

  /is-string@1.0.7:
    resolution: {integrity: sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: true

  /is-symbol@1.0.4:
    resolution: {integrity: sha1-ptrJO2NbBjymhyI23oiRClevE5w=}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: true

  /is-typed-array@1.1.10:
    resolution: {integrity: sha512-PJqgEHiWZvMpaFZ3uTc8kHPM4+4ADTlDniuQL7cU/UDA0Ql7F70yGfHph3cLNe+c9toaigv+DFzTJKhc2CtO6A==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.0
    dev: true

  /is-weakref@1.0.2:
    resolution: {integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==}
    dependencies:
      call-bind: 1.0.2
    dev: true

  /is-windows@1.0.2:
    resolution: {integrity: sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=}
    engines: {node: '>=0.10.0'}

  /is-wsl@1.1.0:
    resolution: {integrity: sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=}
    engines: {node: '>=4'}
    dev: false

  /isarray@1.0.0:
    resolution: {integrity: sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=}

  /isexe@2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=}
    dev: true

  /isobject@2.1.0:
    resolution: {integrity: sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      isarray: 1.0.0

  /isobject@3.0.1:
    resolution: {integrity: sha1-TkMekrEalzFjaqH5yNHMvP2reN8=}
    engines: {node: '>=0.10.0'}

  /jquery@1.12.2:
    resolution: {integrity: sha1-uKi0WTcxKhnuu89aBYmwMRyCILs=}
    dev: false

  /js-base64@2.6.4:
    resolution: {integrity: sha1-9OaGxd4eofhn28rT1G2WlCjfmMQ=}
    dev: true

  /js-cookie@3.0.1:
    resolution: {integrity: sha1-njm0xsL1ZWNwjX0x9vXyGHOpJBQ=}
    engines: {node: '>=12'}
    dev: false

  /js-sdsl@4.4.0:
    resolution: {integrity: sha512-FfVSdx6pJ41Oa+CF7RDaFmTnCaFhua+SNYQX74riGOpl96x+2jQCqEfQ2bnXu/5DPCqlRuiqyvTJM0Qjz26IVg==}
    dev: true

  /js-tokens@4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=}
    dev: true

  /js-yaml@4.1.0:
    resolution: {integrity: sha1-wftl+PUBeQHN0slRhkuhhFihBgI=}
    hasBin: true
    dependencies:
      argparse: 2.0.1
    dev: true

  /jsesc@0.5.0:
    resolution: {integrity: sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=}
    hasBin: true
    dev: true

  /jsesc@2.5.2:
    resolution: {integrity: sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /json-parse-better-errors@1.0.2:
    resolution: {integrity: sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=}
    dev: false

  /json-schema-traverse@0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=}

  /json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=}
    dev: true

  /json5@1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==}
    hasBin: true
    dependencies:
      minimist: 1.2.8

  /json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  /jsonc-parser@3.2.0:
    resolution: {integrity: sha512-gfFQZrcTc8CnKXp6Y4/CBT3fTc0OVuDofpre4aEeEpSBPV5X5v4+Vmx+8snU7RLPrNHPKSgLxGo9YuQzz20o+w==}
    dev: true

  /jsonfile@6.1.0:
    resolution: {integrity: sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=}
    dependencies:
      universalify: 2.0.0
    optionalDependencies:
      graceful-fs: 4.2.11
    dev: true

  /jsts@2.1.0:
    resolution: {integrity: sha512-gxv7hw5FmXwoieHBNKyrl+DZVp1aaJbUPDgPRGf54y7owWoq75CWwL6VCmdKxt5jaMHh9CKOaG1u/WZJRUM5NA==}
    engines: {node: '>= 8'}
    dev: false

  /jsts@2.7.1:
    resolution: {integrity: sha512-x2wSZHEBK20CY+Wy+BPE7MrFQHW6sIsdaGUMEqmGAio+3gFzQaBYPwLRonUfQf9Ak8pBieqj9tUofX1+WtAEIg==}
    engines: {node: '>= 12'}
    dev: false

  /kind-of@3.2.2:
    resolution: {integrity: sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6

  /kind-of@4.0.0:
    resolution: {integrity: sha1-IIE989cSkosgc3hpGkUGb65y3Vc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6

  /kind-of@5.1.0:
    resolution: {integrity: sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=}
    engines: {node: '>=0.10.0'}

  /kind-of@6.0.3:
    resolution: {integrity: sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=}
    engines: {node: '>=0.10.0'}

  /kolorist@1.7.0:
    resolution: {integrity: sha512-ymToLHqL02udwVdbkowNpzjFd6UzozMtshPQKVi5k1EjKRqKqBrOnE9QbLEb0/pV76SAiIT13hdL8R6suc+f3g==}
    dev: true

  /leaflet-path-drag@1.9.5:
    resolution: {integrity: sha512-u2oQzU/IR+XezHwREyB4T3Og5DJXgdyxtmK+GEQyrVLUwLlWaFxa0QMGxlWgE4Ch+rSyUeYOq/x8uLnelC3DnQ==}
    dev: false

  /leaflet.tilelayer.colorfilter@1.2.5:
    resolution: {integrity: sha1-8ey8FQA1I2JkPEwE5di2orSXhMM=}
    dev: false

  /leaflet.vectorgrid@1.3.0(leaflet@1.9.4):
    resolution: {integrity: sha1-/y3uoX0GqcbMXmXYxyhwk5UOIZo=}
    peerDependencies:
      leaflet: ^1.0.2
    dependencies:
      leaflet: 1.9.4
      pbf: 3.3.0
      topojson-client: 2.1.0
      vector-tile: 1.3.0
      whatwg-fetch: 2.0.4
    dev: false

  /leaflet@1.9.4:
    resolution: {integrity: sha512-nxS1ynzJOmOlHp+iL3FyWqK89GtNL8U8rvlMOsQdTTssxZwCXh8N2NB3GDQOL+YR3XnWyZAxwQixURb+FA74PA==}
    dev: false

  /levn@0.4.1:
    resolution: {integrity: sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0
    dev: true

  /loader-runner@2.4.0:
    resolution: {integrity: sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dev: false

  /loader-utils@1.4.2:
    resolution: {integrity: sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==}
    engines: {node: '>=4.0.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 1.0.2

  /local-pkg@0.4.3:
    resolution: {integrity: sha512-SFppqq5p42fe2qcZQqqEOiVRXl+WCP1MdT6k7BDEW1j++sp5fIY+/fdRQitvKgB5BrBcmrs5m/L0v2FrU5MY1g==}
    engines: {node: '>=14'}
    dev: true

  /locate-path@3.0.0:
    resolution: {integrity: sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=}
    engines: {node: '>=6'}
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0
    dev: false

  /locate-path@6.0.0:
    resolution: {integrity: sha1-VTIeswn+u8WcSAHZMackUqaB0oY=}
    engines: {node: '>=10'}
    dependencies:
      p-locate: 5.0.0
    dev: true

  /lodash-es@4.17.21:
    resolution: {integrity: sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=}
    dev: false

  /lodash-unified@1.0.3(@types/lodash-es@4.17.7)(lodash-es@4.17.21)(lodash@4.17.21):
    resolution: {integrity: sha512-WK9qSozxXOD7ZJQlpSqOT+om2ZfcT4yO+03FuzAHD0wF6S0l0090LRPDx3vhTTLZ8cFKpBn+IOcVXK6qOcIlfQ==}
    peerDependencies:
      '@types/lodash-es': '*'
      lodash: '*'
      lodash-es: '*'
    dependencies:
      '@types/lodash-es': 4.17.7
      lodash: 4.17.21
      lodash-es: 4.17.21
    dev: false

  /lodash._reinterpolate@3.0.0:
    resolution: {integrity: sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0=}
    dev: false

  /lodash.clonedeep@4.5.0:
    resolution: {integrity: sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=}
    dev: false

  /lodash.debounce@4.0.8:
    resolution: {integrity: sha1-gteb/zCmfEAF/9XiUVMArZyk168=}

  /lodash.merge@4.6.2:
    resolution: {integrity: sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=}
    dev: true

  /lodash.template@4.5.0:
    resolution: {integrity: sha1-+XYZXPPzR9DV9SSDVp/oAxzM6Ks=}
    dependencies:
      lodash._reinterpolate: 3.0.0
      lodash.templatesettings: 4.2.0
    dev: false

  /lodash.templatesettings@4.2.0:
    resolution: {integrity: sha1-5IExDwSdPPbUfpEq0JMTsVTw+zM=}
    dependencies:
      lodash._reinterpolate: 3.0.0
    dev: false

  /lodash@4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=}

  /lru-cache@5.1.1:
    resolution: {integrity: sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=}
    dependencies:
      yallist: 3.1.1

  /lru-cache@6.0.0:
    resolution: {integrity: sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /magic-string@0.30.0:
    resolution: {integrity: sha512-LA+31JYDJLs82r2ScLrlz1GjSgu66ZV518eyWT+S8VhyQn/JL0u9MeBOvQMGYiPk1DBiSN9DDMOcXvigJZaViQ==}
    engines: {node: '>=12'}
    dependencies:
      '@jridgewell/sourcemap-codec': 1.4.15
    dev: true

  /magic-string@0.30.5:
    resolution: {integrity: sha512-7xlpfBaQaP/T6Vh8MO/EqXSW5En6INHEvEXQiuff7Gku0PWjU3uf6w/j9o7O+SpB5fOAkrI5HeoNgwjEO0pFsA==}
    engines: {node: '>=12'}
    dependencies:
      '@jridgewell/sourcemap-codec': 1.4.15

  /make-dir@2.1.0:
    resolution: {integrity: sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=}
    engines: {node: '>=6'}
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    dev: false

  /map-cache@0.2.2:
    resolution: {integrity: sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=}
    engines: {node: '>=0.10.0'}

  /map-visit@1.0.0:
    resolution: {integrity: sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-visit: 1.0.1

  /marchingsquares@1.3.3:
    resolution: {integrity: sha512-gz6nNQoVK7Lkh2pZulrT4qd4347S/toG9RXH2pyzhLgkL5mLkBoqgv4EvAGXcV0ikDW72n/OQb3Xe8bGagQZCg==}
    dev: false

  /md5.js@1.3.5:
    resolution: {integrity: sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=}
    dependencies:
      hash-base: 3.1.0
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /mdn-data@2.0.14:
    resolution: {integrity: sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=}
    dev: true

  /memoize-one@6.0.0:
    resolution: {integrity: sha1-slkbhx7YKUiu5HJ9xqvO7qyMEEU=}
    dev: false

  /memory-fs@0.4.1:
    resolution: {integrity: sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.8
    dev: false

  /memory-fs@0.5.0:
    resolution: {integrity: sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.8
    dev: false

  /merge-options@1.0.1:
    resolution: {integrity: sha1-KmSyRFe+zU5NxggoMkfpTOWJqjI=}
    engines: {node: '>=4'}
    dependencies:
      is-plain-obj: 1.1.0
    dev: true

  /merge-stream@2.0.0:
    resolution: {integrity: sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=}
    dev: true

  /merge2@1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=}
    engines: {node: '>= 8'}
    dev: true

  /mgrs@0.0.3:
    resolution: {integrity: sha1-MFjTiukuGr+/dLMqj2y1IlpuqgU=}
    dev: false

  /micromatch@3.1.0:
    resolution: {integrity: sha1-UQLU6vILaZfWAI46z+HESj+oFeI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2
      define-property: 1.0.0
      extend-shallow: 2.0.1
      extglob: 2.0.4
      fragment-cache: 0.2.1
      kind-of: 5.1.0
      nanomatch: 1.2.13
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /micromatch@3.1.10:
    resolution: {integrity: sha1-cIWbyVyYQJUvNZoGij/En57PrCM=}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      extglob: 2.0.4
      fragment-cache: 0.2.1
      kind-of: 6.0.3
      nanomatch: 1.2.13
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /micromatch@4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1
    dev: true

  /miller-rabin@4.0.1:
    resolution: {integrity: sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=}
    hasBin: true
    dependencies:
      bn.js: 4.12.0
      brorand: 1.1.0
    dev: false

  /mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}
    dev: false

  /mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0
    dev: false

  /mimic-fn@2.1.0:
    resolution: {integrity: sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=}
    engines: {node: '>=6'}
    dev: true

  /minimalistic-assert@1.0.1:
    resolution: {integrity: sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=}
    dev: false

  /minimalistic-crypto-utils@1.0.1:
    resolution: {integrity: sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=}
    dev: false

  /minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.11

  /minimatch@7.4.6:
    resolution: {integrity: sha512-sBz8G/YjVniEz6lKPNpKxXwazJe4c19fEfV2GDMX6AjFz+MX9uDWIZW8XreVhkFW3fkIdTv/gxWr/Kks5FFAVw==}
    engines: {node: '>=10'}
    dependencies:
      brace-expansion: 2.0.1
    dev: true

  /minimatch@9.0.0:
    resolution: {integrity: sha512-0jJj8AvgKqWN05mrwuqi8QYKx1WmYSUoKSxu5Qhs9prezTz10sxAHGNZe9J9cqIJzta8DWsleh2KaVaLl6Ru2w==}
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: 2.0.1
    dev: true

  /minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  /mississippi@3.0.0:
    resolution: {integrity: sha1-6goykfl+C16HdrNj1fChLZTGcCI=}
    engines: {node: '>=4.0.0'}
    dependencies:
      concat-stream: 1.6.2
      duplexify: 3.7.1
      end-of-stream: 1.4.4
      flush-write-stream: 1.1.1
      from2: 2.3.0
      parallel-transform: 1.2.0
      pump: 3.0.2
      pumpify: 1.5.1
      stream-each: 1.2.3
      through2: 2.0.5
    dev: false

  /mitt@2.1.0:
    resolution: {integrity: sha1-90BXfCMXbGIFsSGylzUU6t4bIjA=}
    dev: false

  /mixin-deep@1.3.2:
    resolution: {integrity: sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-in: 1.0.2
      is-extendable: 1.0.1

  /mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true
    dependencies:
      minimist: 1.2.8
    dev: false

  /mlly@1.2.0:
    resolution: {integrity: sha512-+c7A3CV0KGdKcylsI6khWyts/CYrGTrRVo4R/I7u/cUsy0Conxa6LUhiEzVKIw14lc2L5aiO4+SeVe4TeGRKww==}
    dependencies:
      acorn: 8.11.2
      pathe: 1.1.0
      pkg-types: 1.0.2
      ufo: 1.1.1
    dev: true

  /moment@2.30.1:
    resolution: {integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==}
    dev: true

  /move-concurrently@1.0.1:
    resolution: {integrity: sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=}
    dependencies:
      aproba: 1.2.0
      copy-concurrently: 1.0.5
      fs-write-stream-atomic: 1.0.10
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3
    dev: false

  /ms@2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=}

  /ms@2.1.2:
    resolution: {integrity: sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=}
    dev: true

  /ms@2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=}
    dev: true

  /nan@2.20.0:
    resolution: {integrity: sha512-bk3gXBZDGILuuo/6sKtr0DQmSThYHLtNCdSdXk9YkxD/jK6X2vmCyyXBBxyqZ4XcnzTyYEAThfX3DCEnLf6igw==}
    requiresBuild: true
    dev: false
    optional: true

  /nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  /nanomatch@1.2.13:
    resolution: {integrity: sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      fragment-cache: 0.2.1
      is-windows: 1.0.2
      kind-of: 6.0.3
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  /natural-compare@1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=}
    dev: true

  /neo-async@2.6.2:
    resolution: {integrity: sha1-tKr7k+OustgXTKU88WOrfXMIMF8=}
    dev: false

  /next-tick@1.1.0:
    resolution: {integrity: sha1-GDbuMK1W1n7ygbIr0Zn3CUSbNes=}
    dev: false

  /node-libs-browser@2.2.1:
    resolution: {integrity: sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=}
    dependencies:
      assert: 1.5.1
      browserify-zlib: 0.2.0
      buffer: 4.9.2
      console-browserify: 1.2.0
      constants-browserify: 1.0.0
      crypto-browserify: 3.12.0
      domain-browser: 1.2.0
      events: 3.3.0
      https-browserify: 1.0.0
      os-browserify: 0.3.0
      path-browserify: 0.0.1
      process: 0.11.10
      punycode: 1.4.1
      querystring-es3: 0.2.1
      readable-stream: 2.3.8
      stream-browserify: 2.0.2
      stream-http: 2.8.3
      string_decoder: 1.3.0
      timers-browserify: 2.0.12
      tty-browserify: 0.0.0
      url: 0.11.4
      util: 0.11.1
      vm-browserify: 1.1.2
    dev: false

  /node-releases@2.0.10:
    resolution: {integrity: sha512-5GFldHPXVG/YZmFzJvKK2zDSzPKhEp0+ZR5SVaoSag9fsL5YgHbUHDfnG5494ISANDcK4KwPXAx2xqVEydmd7w==}
    dev: true

  /normalize-path@2.1.1:
    resolution: {integrity: sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    dependencies:
      remove-trailing-separator: 1.1.0
    dev: false
    optional: true

  /normalize-path@3.0.0:
    resolution: {integrity: sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=}
    engines: {node: '>=0.10.0'}

  /normalize-wheel-es@1.2.0:
    resolution: {integrity: sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw==}
    dev: false

  /npm-run-path@4.0.1:
    resolution: {integrity: sha1-t+zR5e1T2o43pV4cImnguX7XSOo=}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1
    dev: true

  /nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}
    dependencies:
      boolbase: 1.0.0
    dev: true

  /object-assign@4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=}
    engines: {node: '>=0.10.0'}
    dev: true

  /object-copy@0.1.0:
    resolution: {integrity: sha1-fn2Fi3gb18mRpBupde04EnVOmYw=}
    engines: {node: '>=0.10.0'}
    dependencies:
      copy-descriptor: 0.1.1
      define-property: 0.2.5
      kind-of: 3.2.2

  /object-inspect@1.12.3:
    resolution: {integrity: sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==}

  /object-inspect@1.13.2:
    resolution: {integrity: sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==}
    engines: {node: '>= 0.4'}
    dev: false

  /object-keys@1.1.1:
    resolution: {integrity: sha1-HEfyct8nfzsdrwYWd9nILiMixg4=}
    engines: {node: '>= 0.4'}

  /object-visit@1.0.1:
    resolution: {integrity: sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1

  /object.assign@4.1.4:
    resolution: {integrity: sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      has-symbols: 1.0.3
      object-keys: 1.1.1

  /object.pick@1.3.0:
    resolution: {integrity: sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1

  /object.values@1.1.6:
    resolution: {integrity: sha512-FVVTkD1vENCsAcwNs9k6jea2uHC/X0+JcjG8YA60FN5CMaJmG95wT9jek/xX9nornqGRrBkKtzuAu2wuHpKqvw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
    dev: true

  /once@1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=}
    dependencies:
      wrappy: 1.0.2

  /onetime@5.1.2:
    resolution: {integrity: sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0
    dev: true

  /optionator@0.9.1:
    resolution: {integrity: sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.3
    dev: true

  /os-browserify@0.3.0:
    resolution: {integrity: sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=}
    dev: false

  /p-limit@2.3.0:
    resolution: {integrity: sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=}
    engines: {node: '>=6'}
    dependencies:
      p-try: 2.2.0
    dev: false

  /p-limit@3.1.0:
    resolution: {integrity: sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=}
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: 0.1.0
    dev: true

  /p-locate@3.0.0:
    resolution: {integrity: sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=}
    engines: {node: '>=6'}
    dependencies:
      p-limit: 2.3.0
    dev: false

  /p-locate@5.0.0:
    resolution: {integrity: sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=}
    engines: {node: '>=10'}
    dependencies:
      p-limit: 3.1.0
    dev: true

  /p-try@2.2.0:
    resolution: {integrity: sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=}
    engines: {node: '>=6'}
    dev: false

  /pako@1.0.11:
    resolution: {integrity: sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=}
    dev: false

  /parallel-transform@1.2.0:
    resolution: {integrity: sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=}
    dependencies:
      cyclist: 1.0.2
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: false

  /parent-module@1.0.1:
    resolution: {integrity: sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: true

  /parse-asn1@5.1.7:
    resolution: {integrity: sha512-CTM5kuWR3sx9IFamcl5ErfPl6ea/N8IYwiJ+vpeB2g+1iknv7zBl5uPwbMbRVznRVbrNY6lGuDoE5b30grmbqg==}
    engines: {node: '>= 0.10'}
    dependencies:
      asn1.js: 4.10.1
      browserify-aes: 1.2.0
      evp_bytestokey: 1.0.3
      hash-base: 3.0.4
      pbkdf2: 3.1.2
      safe-buffer: 5.2.1
    dev: false

  /pascalcase@0.1.1:
    resolution: {integrity: sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=}
    engines: {node: '>=0.10.0'}

  /path-browserify@0.0.1:
    resolution: {integrity: sha1-5sTd1+06onxoogzE5Q4aTug7vEo=}
    dev: false

  /path-dirname@1.0.2:
    resolution: {integrity: sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=}
    requiresBuild: true
    dev: false
    optional: true

  /path-exists@3.0.0:
    resolution: {integrity: sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=}
    engines: {node: '>=4'}
    dev: false

  /path-exists@4.0.0:
    resolution: {integrity: sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=}
    engines: {node: '>=8'}
    dev: true

  /path-is-absolute@1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=}
    engines: {node: '>=0.10.0'}

  /path-key@3.1.1:
    resolution: {integrity: sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=}
    engines: {node: '>=8'}
    dev: true

  /path-parse@1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=}
    dev: true

  /pathe@0.2.0:
    resolution: {integrity: sha512-sTitTPYnn23esFR3RlqYBWn4c45WGeLcsKzQiUpXJAyfcWkolvlYpV8FLo7JishK946oQwMFUCHXQ9AjGPKExw==}
    dev: true

  /pathe@1.1.0:
    resolution: {integrity: sha512-ODbEPR0KKHqECXW1GoxdDb+AZvULmXjVPy4rt+pGo2+TnjJTIPJQSVS6N63n8T2Ip+syHhbn52OewKicV0373w==}
    dev: true

  /pbf@3.3.0:
    resolution: {integrity: sha512-XDF38WCH3z5OV/OVa8GKUNtLAyneuzbCisx7QUCF8Q6Nutx0WnJrQe5O+kOtBlLfRNUws98Y58Lblp+NJG5T4Q==}
    hasBin: true
    dependencies:
      ieee754: 1.2.1
      resolve-protobuf-schema: 2.1.0
    dev: false

  /pbkdf2@3.1.2:
    resolution: {integrity: sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU=}
    engines: {node: '>=0.12'}
    dependencies:
      create-hash: 1.2.0
      create-hmac: 1.1.7
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11
    dev: false

  /picocolors@1.0.0:
    resolution: {integrity: sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=}

  /picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  /pify@4.0.1:
    resolution: {integrity: sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=}
    engines: {node: '>=6'}
    dev: false

  /pinia@2.0.34(vue@3.4.15):
    resolution: {integrity: sha512-cgOoGUiyqX0SSgX8XelK9+Ri4XA2/YyNtgjogwfzIx1g7iZTaZPxm7/bZYMCLU2qHRiHhxG7SuQO0eBacFNc2Q==}
    peerDependencies:
      '@vue/composition-api': ^1.4.0
      typescript: '>=4.4.4'
      vue: ^2.6.14 || ^3.2.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
      typescript:
        optional: true
    dependencies:
      '@vue/devtools-api': 6.5.0
      vue: 3.4.15
      vue-demi: 0.14.0(vue@3.4.15)
    dev: false

  /pkg-dir@3.0.0:
    resolution: {integrity: sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=}
    engines: {node: '>=6'}
    dependencies:
      find-up: 3.0.0
    dev: false

  /pkg-types@1.0.2:
    resolution: {integrity: sha512-hM58GKXOcj8WTqUXnsQyJYXdeAPbythQgEF3nTcEo+nkD49chjQ9IKm/QJy9xf6JakXptz86h7ecP2024rrLaQ==}
    dependencies:
      jsonc-parser: 3.2.0
      mlly: 1.2.0
      pathe: 1.1.0
    dev: true

  /point-geometry@0.0.0:
    resolution: {integrity: sha1-b8vK16gDtkGCR91uScKFPFhNr/c=}
    deprecated: 'This module has moved: please install @mapbox/point-geometry instead'
    dev: false

  /point-in-polygon-hao@1.1.0:
    resolution: {integrity: sha512-3hTIM2j/v9Lio+wOyur3kckD4NxruZhpowUbEgmyikW+a2Kppjtu1eN+AhnMQtoHW46zld88JiYWv6fxpsDrTQ==}
    dev: false

  /point-in-polygon@1.1.0:
    resolution: {integrity: sha512-3ojrFwjnnw8Q9242TzgXuTD+eKiutbzyslcq1ydfu82Db2y+Ogbmyrkpv0Hgj31qwT3lbS9+QAAO/pIQM35XRw==}
    dev: false

  /polyclip-ts@0.16.5:
    resolution: {integrity: sha512-ZchnG0zGZReHgEo3EYzEUi6UmfQFFzNnj6AFU+gBm+IJJ4qG9gL4CwjtCV6oi/PittUPpJLiLJxcn/AgrCBO+g==}
    dependencies:
      bignumber.js: 9.1.2
      splaytree-ts: 1.0.1
    dev: false

  /polygon-clipping@0.15.7:
    resolution: {integrity: sha512-nhfdr83ECBg6xtqOAJab1tbksbBAOMUltN60bU+llHVOL0e5Onm1WpAXXWXVB39L8AJFssoIhEVuy/S90MmotA==}
    dependencies:
      robust-predicates: 3.0.2
      splaytree: 3.1.2
    dev: false

  /posix-character-classes@0.1.1:
    resolution: {integrity: sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=}
    engines: {node: '>=0.10.0'}

  /postcss-prefix-selector@1.16.0(postcss@5.2.18):
    resolution: {integrity: sha512-rdVMIi7Q4B0XbXqNUEI+Z4E+pueiu/CS5E6vRCQommzdQ/sgsS4dK42U7GX8oJR+TJOtT+Qv3GkNo6iijUMp3Q==}
    peerDependencies:
      postcss: '>4 <9'
    dependencies:
      postcss: 5.2.18
    dev: true

  /postcss-selector-parser@6.0.11:
    resolution: {integrity: sha512-zbARubNdogI9j7WY4nQJBiNqQf3sLS3wCP4WfOidu+p28LofJqDH1tcXypGrcmMHhDk2t9wGhCsYe/+szLTy1g==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: true

  /postcss@5.2.18:
    resolution: {integrity: sha1-ut+hSX1GJE9jkPWLMZgw2RB4U8U=}
    engines: {node: '>=0.12'}
    dependencies:
      chalk: 1.1.3
      js-base64: 2.6.4
      source-map: 0.5.7
      supports-color: 3.2.3
    dev: true

  /postcss@8.4.31:
    resolution: {integrity: sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.0
      source-map-js: 1.0.2
    dev: true

  /postcss@8.4.33:
    resolution: {integrity: sha512-Kkpbhhdjw2qQs2O2DGX+8m5OVqEcbB9HRBvuYM9pgrjEFUg30A9LmXNlTAUj4S9kgtGyrMbTzVjH7E+s5Re2yg==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.0
      source-map-js: 1.0.2

  /posthtml-parser@0.2.1:
    resolution: {integrity: sha1-NdUw3jhnQMK6JP8usvrznM3ycd0=}
    dependencies:
      htmlparser2: 3.10.1
      isobject: 2.1.0
    dev: true

  /posthtml-rename-id@1.0.12:
    resolution: {integrity: sha1-z39us3FGvxr6wx5o8YxswZrmFDM=}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: true

  /posthtml-render@1.4.0:
    resolution: {integrity: sha1-QBFAcMRYgcrLkzR9rj7/U6+8/xM=}
    engines: {node: '>=10'}
    dev: true

  /posthtml-svg-mode@1.0.3:
    resolution: {integrity: sha1-q9VU+s6BIjyrDLNn4Y5O/SpOdLA=}
    dependencies:
      merge-options: 1.0.1
      posthtml: 0.9.2
      posthtml-parser: 0.2.1
      posthtml-render: 1.4.0
    dev: true

  /posthtml@0.9.2:
    resolution: {integrity: sha1-9MBtufZ7Yf0XxOJW5+PZUVv3Jv0=}
    engines: {node: '>=0.10.0'}
    dependencies:
      posthtml-parser: 0.2.1
      posthtml-render: 1.4.0
    dev: true

  /prelude-ls@1.2.1:
    resolution: {integrity: sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=}
    engines: {node: '>= 0.8.0'}
    dev: true

  /prettier@2.8.7:
    resolution: {integrity: sha512-yPngTo3aXUUmyuTjeTUT75txrf+aMh9FiD7q9ZE/i6r0bPb22g4FsE6Y338PQX1bmfy08i9QQCB7/rcUAVntfw==}
    engines: {node: '>=10.13.0'}
    hasBin: true

  /process-nextick-args@2.0.1:
    resolution: {integrity: sha1-eCDZsWEgzFXKmud5JoCufbptf+I=}
    dev: false

  /process@0.11.10:
    resolution: {integrity: sha1-czIwDoQBYb2j5podHZGn1LwW8YI=}
    engines: {node: '>= 0.6.0'}
    dev: false

  /proj4@2.3.15:
    resolution: {integrity: sha1-WtBui8owvg/6OJpJ5FZfUfBtCJ4=}
    dependencies:
      mgrs: 0.0.3
    dev: false

  /promise-inflight@1.0.1(bluebird@3.7.2):
    resolution: {integrity: sha1-mEcocL8igTL8vdhoEputEsPAKeM=}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true
    dependencies:
      bluebird: 3.7.2
    dev: false

  /promise@8.3.0:
    resolution: {integrity: sha512-rZPNPKTOYVNEEKFaq1HqTgOwZD+4/YHS5ukLzQCypkj+OkYx7iv0mA91lJlpPPZ8vMau3IIGj5Qlwrx+8iiSmg==}
    dependencies:
      asap: 2.0.6
    dev: false

  /protocol-buffers-schema@3.6.0:
    resolution: {integrity: sha1-d7x1pIsv8ULBrVtbkMlM0Pou/QM=}
    dev: false

  /proxy-from-env@1.1.0:
    resolution: {integrity: sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=}
    dev: false

  /prr@1.0.1:
    resolution: {integrity: sha1-0/wRS6BplaRexok/SEzrHXj19HY=}
    dev: false

  /public-encrypt@4.0.3:
    resolution: {integrity: sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=}
    dependencies:
      bn.js: 4.12.0
      browserify-rsa: 4.1.0
      create-hash: 1.2.0
      parse-asn1: 5.1.7
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: false

  /pump@2.0.1:
    resolution: {integrity: sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0
    dev: false

  /pump@3.0.2:
    resolution: {integrity: sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0
    dev: false

  /pumpify@1.5.1:
    resolution: {integrity: sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=}
    dependencies:
      duplexify: 3.7.1
      inherits: 2.0.4
      pump: 2.0.1
    dev: false

  /punycode@1.4.1:
    resolution: {integrity: sha1-wNWmOycYgArY4esPpSachN1BhF4=}
    dev: false

  /punycode@2.3.0:
    resolution: {integrity: sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==}
    engines: {node: '>=6'}

  /qs@6.11.1:
    resolution: {integrity: sha512-0wsrzgTz/kAVIeuxSjnpGC56rzYtr6JT/2BwEvMaPhFIoYa1aGO8LbzuU1R0uUYQkLpWBTOj0l/CLAJB64J6nQ==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.0.4
    dev: false

  /qs@6.13.0:
    resolution: {integrity: sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.0.6
    dev: false

  /query-string@4.3.4:
    resolution: {integrity: sha1-u7aTucqRXCMlFbIosaArYJBD2+s=}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-assign: 4.1.1
      strict-uri-encode: 1.1.0
    dev: true

  /querystring-es3@0.2.1:
    resolution: {integrity: sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=}
    engines: {node: '>=0.4.x'}
    dev: false

  /queue-microtask@1.2.3:
    resolution: {integrity: sha1-SSkii7xyTfrEPg77BYyve2z7YkM=}
    dev: true

  /quickselect@1.1.1:
    resolution: {integrity: sha1-hS5BLOQY8jetW2YNcM/6xkeulMI=}
    dev: false

  /quickselect@2.0.0:
    resolution: {integrity: sha1-8ZaApIal7vtYEwPgI+mPqvJd0Bg=}
    dev: false

  /randombytes@2.1.0:
    resolution: {integrity: sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=}
    dependencies:
      safe-buffer: 5.2.1
    dev: false

  /randomfill@1.0.4:
    resolution: {integrity: sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=}
    dependencies:
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: false

  /raphael@2.3.0:
    resolution: {integrity: sha1-6r6wnbqGGh1M7gd+qvuMU/MTH4k=}
    dependencies:
      eve-raphael: 0.5.0
    dev: false

  /rbush@2.0.2:
    resolution: {integrity: sha1-u2AFwnMbe6HVqaA1dykn0WphRgU=}
    dependencies:
      quickselect: 1.1.1
    dev: false

  /rbush@3.0.1:
    resolution: {integrity: sha1-X6+op5s7mv3+UAhAOnIMwd6ILs8=}
    dependencies:
      quickselect: 2.0.0
    dev: false

  /readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2
    dev: false

  /readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  /readdirp@2.2.1:
    resolution: {integrity: sha1-DodiKjMlqjPokihcr4tOhGUppSU=}
    engines: {node: '>=0.10'}
    requiresBuild: true
    dependencies:
      graceful-fs: 4.2.11
      micromatch: 3.1.10
      readable-stream: 2.3.8
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /readdirp@3.6.0:
    resolution: {integrity: sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1

  /regenerate-unicode-properties@10.1.0:
    resolution: {integrity: sha512-d1VudCLoIGitcU/hEg2QqvyGZQmdC0Lf8BqdOMXGFSvJP4bNV1+XqbPQeHHLD51Jh4QJJ225dlIFvY4Ly6MXmQ==}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2
    dev: true

  /regenerate@1.4.2:
    resolution: {integrity: sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=}
    dev: true

  /regenerator-runtime@0.13.11:
    resolution: {integrity: sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==}

  /regenerator-transform@0.15.1:
    resolution: {integrity: sha512-knzmNAcuyxV+gQCufkYcvOqX/qIIfHLv0u5x79kRxuGojfYVky1f15TzZEu2Avte8QGepvUNTnLskf8E6X6Vyg==}
    dependencies:
      '@babel/runtime': 7.21.0
    dev: true

  /regex-not@1.0.2:
    resolution: {integrity: sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2
      safe-regex: 1.1.0

  /regexp.prototype.flags@1.5.0:
    resolution: {integrity: sha512-0SutC3pNudRKgquxGoRGIz946MZVHqbNfPjBdxeOhBrdgDKlRoXmYLQN9xRbrR09ZXWeGAdPuif7egofn6v5LA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      functions-have-names: 1.2.3
    dev: true

  /regexpp@3.2.0:
    resolution: {integrity: sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=}
    engines: {node: '>=8'}
    dev: true

  /regexpu-core@5.3.2:
    resolution: {integrity: sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ==}
    engines: {node: '>=4'}
    dependencies:
      '@babel/regjsgen': 0.8.0
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.1.0
      regjsparser: 0.9.1
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.1.0
    dev: true

  /regjsparser@0.9.1:
    resolution: {integrity: sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==}
    hasBin: true
    dependencies:
      jsesc: 0.5.0
    dev: true

  /remove-trailing-separator@1.1.0:
    resolution: {integrity: sha1-wkvOKig62tW8P1jg1IJJuSN52O8=}
    requiresBuild: true
    dev: false
    optional: true

  /repeat-element@1.1.4:
    resolution: {integrity: sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=}
    engines: {node: '>=0.10.0'}

  /repeat-string@1.6.1:
    resolution: {integrity: sha1-jcrkcOHIirwtYA//Sndihtp15jc=}
    engines: {node: '>=0.10'}

  /resize-detector@0.3.0:
    resolution: {integrity: sha1-/klREuGEaVUAqPUeA4nxV3TLHPw=}
    dev: false

  /resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=}
    dev: false

  /resolve-from@4.0.0:
    resolution: {integrity: sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=}
    engines: {node: '>=4'}
    dev: true

  /resolve-protobuf-schema@2.1.0:
    resolution: {integrity: sha1-nKmp5pzxkrva8QBuwZc5SKpKN1g=}
    dependencies:
      protocol-buffers-schema: 3.6.0
    dev: false

  /resolve-url@0.2.1:
    resolution: {integrity: sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=}

  /resolve@1.22.3:
    resolution: {integrity: sha512-P8ur/gp/AmbEzjr729bZnLjXK5Z+4P0zhIJgBgzqRih7hL7BOukHGtSTA3ACMY467GRFz3duQsi0bDZdR7DKdw==}
    hasBin: true
    dependencies:
      is-core-module: 2.12.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0
    dev: true

  /ret@0.1.15:
    resolution: {integrity: sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=}
    engines: {node: '>=0.12'}

  /reusify@1.0.4:
    resolution: {integrity: sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  /rimraf@2.7.1:
    resolution: {integrity: sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=}
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: false

  /rimraf@3.0.2:
    resolution: {integrity: sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=}
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: true

  /ripemd160@2.0.2:
    resolution: {integrity: sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=}
    dependencies:
      hash-base: 3.1.0
      inherits: 2.0.4
    dev: false

  /robust-predicates@2.0.4:
    resolution: {integrity: sha1-CiNnqTq9mWdtB1mBcH8pz7QCJIs=}
    dev: false

  /robust-predicates@3.0.2:
    resolution: {integrity: sha512-IXgzBWvWQwE6PrDI05OvmXUIruQTcoMDzRsOd5CDvHCVLcLHMTSYvOK5Cm46kWqlV3yAbuSpBZdJ5oP5OUoStg==}
    dev: false

  /rollup@3.29.4:
    resolution: {integrity: sha512-oWzmBZwvYrU0iJHtDmhsm662rC15FRXmcjCk1xD771dFDx5jJ02ufAQQTn0etB2emNk4J9EZg/yWKpsn9BWGRw==}
    engines: {node: '>=14.18.0', npm: '>=8.0.0'}
    hasBin: true
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /run-parallel@1.2.0:
    resolution: {integrity: sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=}
    dependencies:
      queue-microtask: 1.2.3
    dev: true

  /run-queue@1.0.3:
    resolution: {integrity: sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=}
    dependencies:
      aproba: 1.2.0
    dev: false

  /rw@0.1.4:
    resolution: {integrity: sha1-SQPL2AJIrg7eaFv1j9I2p6mymj4=}
    dev: false

  /rw@1.3.3:
    resolution: {integrity: sha1-P4Yt+pGrdmsUiF700BEkv9oHT7Q=}
    dev: false

  /rxjs@7.8.1:
    resolution: {integrity: sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==}
    dependencies:
      tslib: 2.6.2
    dev: false

  /safe-buffer@5.1.2:
    resolution: {integrity: sha1-mR7GnSluAxN0fVm9/St0XDX4go0=}
    dev: false

  /safe-buffer@5.2.1:
    resolution: {integrity: sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=}

  /safe-regex-test@1.0.0:
    resolution: {integrity: sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.0
      is-regex: 1.1.4
    dev: true

  /safe-regex@1.1.0:
    resolution: {integrity: sha1-QKNmnzsHfR6UPURinhV91IAjvy4=}
    dependencies:
      ret: 0.1.15

  /safer-buffer@2.1.2:
    resolution: {integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=}
    dev: false

  /sass@1.62.0:
    resolution: {integrity: sha512-Q4USplo4pLYgCi+XlipZCWUQz5pkg/ruSSgJ0WRDSb/+3z9tXUOkQ7QPYn4XrhZKYAK4HlpaQecRwKLJX6+DBg==}
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      chokidar: 3.5.3
      immutable: 4.3.0
      source-map-js: 1.0.2
    dev: true

  /schema-utils@1.0.0:
    resolution: {integrity: sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=}
    engines: {node: '>= 4'}
    dependencies:
      ajv: 6.12.6
      ajv-errors: 1.0.1(ajv@6.12.6)
      ajv-keywords: 3.5.2(ajv@6.12.6)
    dev: false

  /schema-utils@2.7.1:
    resolution: {integrity: sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=}
    engines: {node: '>= 8.9.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)
    dev: false

  /scule@1.0.0:
    resolution: {integrity: sha512-4AsO/FrViE/iDNEPaAQlb77tf0csuq27EsVpy6ett584EcRTp6pTDLoGWVxCD77y5iU5FauOvhsI4o1APwPoSQ==}
    dev: true

  /semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true
    dev: false

  /semver@6.3.0:
    resolution: {integrity: sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=}
    hasBin: true
    dev: true

  /semver@7.5.0:
    resolution: {integrity: sha512-+XC0AD/R7Q2mPSRuy2Id0+CGTZ98+8f+KvwirxOKIEyid+XSx6HbC63p+O4IndTHuX5Z+JxQ0TghCkO5Cg/2HA==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: 6.0.0
    dev: true

  /serialize-javascript@4.0.0:
    resolution: {integrity: sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=}
    dependencies:
      randombytes: 2.1.0
    dev: false

  /set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
    dev: false

  /set-value@2.0.1:
    resolution: {integrity: sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-extendable: 0.1.1
      is-plain-object: 2.0.4
      split-string: 3.1.0

  /setimmediate@1.0.5:
    resolution: {integrity: sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=}
    dev: false

  /sha.js@2.4.11:
    resolution: {integrity: sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=}
    hasBin: true
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /shebang-command@2.0.0:
    resolution: {integrity: sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: true

  /shebang-regex@3.0.0:
    resolution: {integrity: sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=}
    engines: {node: '>=8'}
    dev: true

  /side-channel@1.0.4:
    resolution: {integrity: sha1-785cj9wQTudRslxY1CkAEfpeos8=}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.0
      object-inspect: 1.12.3

  /side-channel@1.0.6:
    resolution: {integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.2
    dev: false

  /signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}
    dev: true

  /skmeans@0.9.7:
    resolution: {integrity: sha1-cmcM67coUI9W4pwOENEeYjUpzl0=}
    dev: false

  /snapdragon-node@2.1.1:
    resolution: {integrity: sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 1.0.0
      isobject: 3.0.1
      snapdragon-util: 3.0.1

  /snapdragon-util@3.0.1:
    resolution: {integrity: sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /snapdragon@0.8.2:
    resolution: {integrity: sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=}
    engines: {node: '>=0.10.0'}
    dependencies:
      base: 0.11.2
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      map-cache: 0.2.2
      source-map: 0.5.7
      source-map-resolve: 0.5.3
      use: 3.1.1
    transitivePeerDependencies:
      - supports-color

  /sortablejs@1.15.2:
    resolution: {integrity: sha512-FJF5jgdfvoKn1MAKSdGs33bIqLi3LmsgVTliuX6iITj834F+JRQZN90Z93yql8h0K2t0RwDPBmxwlbZfDcxNZA==}
    dev: false

  /source-list-map@2.0.1:
    resolution: {integrity: sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=}
    dev: false

  /source-map-js@1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==}
    engines: {node: '>=0.10.0'}

  /source-map-resolve@0.5.3:
    resolution: {integrity: sha1-GQhmvs51U+H48mei7oLGBrVQmho=}
    dependencies:
      atob: 2.1.2
      decode-uri-component: 0.2.2
      resolve-url: 0.2.1
      source-map-url: 0.4.1
      urix: 0.1.0

  /source-map-support@0.5.21:
    resolution: {integrity: sha1-BP58f54e0tZiIzwoyys1ufY/bk8=}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  /source-map-url@0.4.1:
    resolution: {integrity: sha1-CvZmBadFpaL5HPG7+KevvCg97FY=}

  /source-map@0.5.7:
    resolution: {integrity: sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=}
    engines: {node: '>=0.10.0'}

  /source-map@0.6.1:
    resolution: {integrity: sha1-dHIq8y6WFOnCh6jQu95IteLxomM=}
    engines: {node: '>=0.10.0'}

  /splaytree-ts@1.0.1:
    resolution: {integrity: sha512-B+VzCm33/KEchi/fzT6/3NRHm8k5+Kf37SBQO3meHHS/tK2xBnIm4ZvusQ1wUpHgKMCCqEWgXnwFXAa1nD289g==}
    dev: false

  /splaytree@3.1.2:
    resolution: {integrity: sha512-4OM2BJgC5UzrhVnnJA4BkHKGtjXNzzUfpQjCO8I05xYPsfS/VuQDwjCGGMi8rYQilHEV4j8NBqTFbls/PZEE7A==}
    dev: false

  /split-string@3.1.0:
    resolution: {integrity: sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2

  /ssri@6.0.2:
    resolution: {integrity: sha1-FXk5E08gRk5zAd26PpD/qPdyisU=}
    dependencies:
      figgy-pudding: 3.5.2
    dev: false

  /stable@0.1.8:
    resolution: {integrity: sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=}
    dev: true

  /static-extend@0.1.2:
    resolution: {integrity: sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 0.2.5
      object-copy: 0.1.0

  /stream-browserify@2.0.2:
    resolution: {integrity: sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: false

  /stream-each@1.2.3:
    resolution: {integrity: sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=}
    dependencies:
      end-of-stream: 1.4.4
      stream-shift: 1.0.3
    dev: false

  /stream-http@2.8.3:
    resolution: {integrity: sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=}
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 2.3.8
      to-arraybuffer: 1.0.1
      xtend: 4.0.2
    dev: false

  /stream-shift@1.0.3:
    resolution: {integrity: sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==}
    dev: false

  /strict-uri-encode@1.1.0:
    resolution: {integrity: sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=}
    engines: {node: '>=0.10.0'}
    dev: true

  /string.prototype.trim@1.2.7:
    resolution: {integrity: sha512-p6TmeT1T3411M8Cgg9wBTMRtY2q9+PNy9EV1i2lIXUN/btt763oIfxwN3RR8VU6wHX8j/1CFy0L+YuThm6bgOg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
    dev: true

  /string.prototype.trimend@1.0.6:
    resolution: {integrity: sha512-JySq+4mrPf9EsDBEDYMOb/lM7XQLulwg5R/m1r0PXEFqrV0qHvl58sdTilSXtKOflCsK2E8jxf+GKC0T07RWwQ==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
    dev: true

  /string.prototype.trimstart@1.0.6:
    resolution: {integrity: sha512-omqjMDaY92pbn5HOX7f9IccLA+U1tA9GvtU4JrodiXFfYB7jPzzHpRzpglLAjtUV6bB557zwClJezTqnAiYnQA==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
    dev: true

  /string_decoder@1.1.1:
    resolution: {integrity: sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=}
    dependencies:
      safe-buffer: 5.1.2
    dev: false

  /string_decoder@1.3.0:
    resolution: {integrity: sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=}
    dependencies:
      safe-buffer: 5.2.1

  /strip-ansi@3.0.1:
    resolution: {integrity: sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: true

  /strip-ansi@6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: true

  /strip-bom@3.0.0:
    resolution: {integrity: sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=}
    engines: {node: '>=4'}
    dev: true

  /strip-final-newline@2.0.0:
    resolution: {integrity: sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=}
    engines: {node: '>=6'}
    dev: true

  /strip-json-comments@3.1.1:
    resolution: {integrity: sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=}
    engines: {node: '>=8'}
    dev: true

  /strip-literal@1.0.1:
    resolution: {integrity: sha512-QZTsipNpa2Ppr6v1AmJHESqJ3Uz247MUS0OjrnnZjFAvEoWqxuyFuXn2xLgMtRnijJShAa1HL0gtJyUs7u7n3Q==}
    dependencies:
      acorn: 8.11.2
    dev: true

  /style-inject@0.3.0:
    resolution: {integrity: sha1-0hxHev/skYEcyCNVgypwDSK/jdM=}
    dev: false

  /suntekmap-client@1.0.13:
    resolution: {integrity: sha512-ML1oWRMpRS3eDCWrp+EfAAUKT7vIKRiUwjEbWA+ho0RBgV5sUyfCX01DfmD4xioBSzrG+nLMy+WVSB1f1Ex+pQ==}
    dependencies:
      es6-promise: 4.2.8
      fetch-ie8: 1.5.0
      fetch-jsonp: 1.3.0
      lodash.template: 4.5.0
    dev: false

  /suntekmap@1.1.2(webpack@4.47.0):
    resolution: {integrity: sha512-Cv5/s3l6Nb3j2kzMqjBVr7ZX1H9vpNKjiP1tNMuikiVqf1V3yzULbmZMZqtplidolU1TBhxoBDiIJHzrL2A5LA==}
    dependencies:
      '@esri/arcgis-to-geojson-utils': 1.3.0
      '@geoman-io/leaflet-geoman-free': 2.17.0(leaflet@1.9.4)
      '@tweenjs/tween.js': 18.6.4
      aight: 2.1.1
      bezier-js: 2.6.1
      caniuse-lite: 1.0.30001660
      d3-quadtree: 1.0.7
      dom-to-image: 2.6.0
      es6-map: 0.1.5
      fetch-ie8: 1.5.0
      fetch-jsonp: 1.3.0
      file-loader: 4.3.0(webpack@4.47.0)
      jquery: 1.12.2
      jsts: 2.1.0
      leaflet: 1.9.4
      leaflet-path-drag: 1.9.5
      leaflet.tilelayer.colorfilter: 1.2.5
      leaflet.vectorgrid: 1.3.0(leaflet@1.9.4)
      lodash.clonedeep: 4.5.0
      lodash.debounce: 4.0.8
      lodash.template: 4.5.0
      proj4: 2.3.15
      promise: 8.3.0
      raphael: 2.3.0
      suntekmap-client: 1.0.13
      topojson-client: 3.1.0
      typedarray: 0.0.6
      wicket: 1.3.8
      xmldom: 0.1.31
    transitivePeerDependencies:
      - webpack
    dev: false

  /supports-color@2.0.0:
    resolution: {integrity: sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=}
    engines: {node: '>=0.8.0'}
    dev: true

  /supports-color@3.2.3:
    resolution: {integrity: sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=}
    engines: {node: '>=0.8.0'}
    dependencies:
      has-flag: 1.0.0
    dev: true

  /supports-color@5.5.0:
    resolution: {integrity: sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0
    dev: true

  /supports-color@7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}
    dev: true

  /svg-baker@1.7.0:
    resolution: {integrity: sha1-g2f3jYdVUMUv5HVvcwPVxdfC6ac=}
    dependencies:
      bluebird: 3.7.2
      clone: 2.1.2
      he: 1.2.0
      image-size: 0.5.5
      loader-utils: 1.4.2
      merge-options: 1.0.1
      micromatch: 3.1.0
      postcss: 5.2.18
      postcss-prefix-selector: 1.16.0(postcss@5.2.18)
      posthtml-rename-id: 1.0.12
      posthtml-svg-mode: 1.0.3
      query-string: 4.3.4
      traverse: 0.6.7
    transitivePeerDependencies:
      - supports-color
    dev: true

  /svgo@2.8.0:
    resolution: {integrity: sha1-T/gMzmcQ3CeV8MfHQQHmdkz8zSQ=}
    engines: {node: '>=10.13.0'}
    hasBin: true
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 4.3.0
      css-tree: 1.1.3
      csso: 4.2.0
      picocolors: 1.0.0
      stable: 0.1.8
    dev: true

  /sweepline-intersections@1.5.0:
    resolution: {integrity: sha512-AoVmx72QHpKtItPu72TzFL+kcYjd67BPLDoR0LarIk+xyaRg+pDTMFXndIEvZf9xEKnJv6JdhgRMnocoG0D3AQ==}
    dependencies:
      tinyqueue: 2.0.3
    dev: false

  /systemjs@6.14.1:
    resolution: {integrity: sha512-8ftwWd+XnQtZ/aGbatrN4QFNGrKJzmbtixW+ODpci7pyoTajg4sonPP8aFLESAcuVxaC1FyDESt+SpfFCH9rZQ==}
    dev: true

  /tapable@1.1.3:
    resolution: {integrity: sha1-ofzMBrWNth/XpF2i2kT186Pme6I=}
    engines: {node: '>=6'}
    dev: false

  /terser-webpack-plugin@1.4.6(webpack@4.47.0):
    resolution: {integrity: sha512-2lBVf/VMVIddjSn3GqbT90GvIJ/eYXJkt8cTzU7NbjKqK8fwv18Ftr4PlbF46b/e88743iZFL5Dtr/rC4hjIeA==}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      cacache: 12.0.4
      find-cache-dir: 2.1.0
      is-wsl: 1.1.0
      schema-utils: 1.0.0
      serialize-javascript: 4.0.0
      source-map: 0.6.1
      terser: 4.8.1
      webpack: 4.47.0
      webpack-sources: 1.4.3
      worker-farm: 1.7.0
    dev: false

  /terser@4.8.1:
    resolution: {integrity: sha512-4GnLC0x667eJG0ewJTa6z/yXrbLGv80D9Ru6HIpCQmO+Q4PfEtBFi0ObSckqwL6VyQv/7ENJieXHo2ANmdQwgw==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      acorn: 8.11.3
      commander: 2.20.3
      source-map: 0.6.1
      source-map-support: 0.5.21
    dev: false

  /terser@5.30.3:
    resolution: {integrity: sha512-STdUgOUx8rLbMGO9IOwHLpCqolkDITFFQSMYYwKE1N2lY6MVSaeoi10z/EhWxRc6ybqoVmKSkhKYH/XUpl7vSA==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.11.3
      commander: 2.20.3
      source-map-support: 0.5.21
    dev: true

  /text-table@0.2.0:
    resolution: {integrity: sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=}
    dev: true

  /through2@2.0.5:
    resolution: {integrity: sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=}
    dependencies:
      readable-stream: 2.3.8
      xtend: 4.0.2
    dev: false

  /timers-browserify@2.0.12:
    resolution: {integrity: sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=}
    engines: {node: '>=0.6.0'}
    dependencies:
      setimmediate: 1.0.5
    dev: false

  /tinyqueue@2.0.3:
    resolution: {integrity: sha1-ZNhJLr8554Ade9NAYuKbRbIDXwg=}
    dev: false

  /tippy.js@6.3.7:
    resolution: {integrity: sha512-E1d3oP2emgJ9dRQZdf3Kkn0qJgI6ZLpyS5z6ZkY1DF3kaQaBsGZsndEpHwx+eC+tYM41HaSNvNtLx8tU57FzTQ==}
    dependencies:
      '@popperjs/core': 2.11.8
    dev: false

  /to-arraybuffer@1.0.1:
    resolution: {integrity: sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=}
    dev: false

  /to-fast-properties@2.0.0:
    resolution: {integrity: sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=}
    engines: {node: '>=4'}

  /to-object-path@0.3.0:
    resolution: {integrity: sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /to-regex-range@2.1.1:
    resolution: {integrity: sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      repeat-string: 1.6.1

  /to-regex-range@5.0.1:
    resolution: {integrity: sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0

  /to-regex@3.0.2:
    resolution: {integrity: sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 2.0.2
      extend-shallow: 3.0.2
      regex-not: 1.0.2
      safe-regex: 1.1.0

  /topojson-client@2.1.0:
    resolution: {integrity: sha1-/59784mRGF4LQoTCsGroNPDqxsg=}
    hasBin: true
    dependencies:
      commander: 2.20.3
    dev: false

  /topojson-client@3.1.0:
    resolution: {integrity: sha1-Iuix7QiiuSL+60r29Ttu8JpGe5k=}
    hasBin: true
    dependencies:
      commander: 2.20.3
    dev: false

  /topojson-server@3.0.1:
    resolution: {integrity: sha1-0rPsCVtnMimb52pIQGERsyAaNPU=}
    hasBin: true
    dependencies:
      commander: 2.20.3
    dev: false

  /traverse@0.6.7:
    resolution: {integrity: sha512-/y956gpUo9ZNCb99YjxG7OaslxZWHfCHAUUfshwqOXmxUIvqLjVO581BT+gM59+QV9tFe6/CGG53tsA1Y7RSdg==}
    dev: true

  /tsconfig-paths@3.14.2:
    resolution: {integrity: sha512-o/9iXgCYc5L/JxCHPe3Hvh8Q/2xm5Z+p18PESBU6Ff33695QnCHBEjcytY2q19ua7Mbl/DavtBOLq+oG0RCL+g==}
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0
    dev: true

  /tslib@2.3.0:
    resolution: {integrity: sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==}
    dev: false

  /tslib@2.6.2:
    resolution: {integrity: sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==}
    dev: false

  /tty-browserify@0.0.0:
    resolution: {integrity: sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=}
    dev: false

  /type-check@0.4.0:
    resolution: {integrity: sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
    dev: true

  /type-fest@0.20.2:
    resolution: {integrity: sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=}
    engines: {node: '>=10'}
    dev: true

  /type@2.7.3:
    resolution: {integrity: sha512-8j+1QmAbPvLZow5Qpi6NCaN8FB60p/6x8/vfNqOk/hC+HuvFZhL4+WfekuhQLiqFZXOgQdrs3B+XxEmCc6b3FQ==}
    dev: false

  /typed-array-length@1.0.4:
    resolution: {integrity: sha512-KjZypGq+I/H7HI5HlOoGHkWUUGq+Q0TPhQurLbyrVrvnKTBgzLhIJ7j6J/XTQOi0d1RjyZ0wdas8bKs2p0x3Ng==}
    dependencies:
      call-bind: 1.0.2
      for-each: 0.3.3
      is-typed-array: 1.1.10
    dev: true

  /typedarray@0.0.6:
    resolution: {integrity: sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=}
    dev: false

  /ufo@1.1.1:
    resolution: {integrity: sha512-MvlCc4GHrmZdAllBc0iUDowff36Q9Ndw/UzqmEKyrfSzokTd9ZCy1i+IIk5hrYKkjoYVQyNbrw7/F8XJ2rEwTg==}
    dev: true

  /unbox-primitive@1.0.2:
    resolution: {integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==}
    dependencies:
      call-bind: 1.0.2
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2
    dev: true

  /unicode-canonical-property-names-ecmascript@2.0.0:
    resolution: {integrity: sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=}
    engines: {node: '>=4'}
    dev: true

  /unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=}
    engines: {node: '>=4'}
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.0
      unicode-property-aliases-ecmascript: 2.1.0
    dev: true

  /unicode-match-property-value-ecmascript@2.1.0:
    resolution: {integrity: sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA==}
    engines: {node: '>=4'}
    dev: true

  /unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}
    dev: true

  /unimport@3.0.6:
    resolution: {integrity: sha512-GYxGJ1Bri1oqx8VFDjdgooGzeK7jBk3bvhXmamTIpu3nONOcUMGwZbX7X0L5RA7OWMXpR4vzpSQP7pXUzJg1/Q==}
    dependencies:
      '@rollup/pluginutils': 5.0.2
      escape-string-regexp: 5.0.0
      fast-glob: 3.2.12
      local-pkg: 0.4.3
      magic-string: 0.30.0
      mlly: 1.2.0
      pathe: 1.1.0
      pkg-types: 1.0.2
      scule: 1.0.0
      strip-literal: 1.0.1
      unplugin: 1.3.1
    transitivePeerDependencies:
      - rollup
    dev: true

  /union-value@1.0.1:
    resolution: {integrity: sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      set-value: 2.0.1

  /unique-filename@1.1.1:
    resolution: {integrity: sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=}
    dependencies:
      unique-slug: 2.0.2
    dev: false

  /unique-slug@2.0.2:
    resolution: {integrity: sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=}
    dependencies:
      imurmurhash: 0.1.4
    dev: false

  /universalify@2.0.0:
    resolution: {integrity: sha1-daSYTv7cSwiXXFrrc/Uw0C3yVxc=}
    engines: {node: '>= 10.0.0'}
    dev: true

  /unplugin-auto-import@0.15.3:
    resolution: {integrity: sha512-RLT8SqbPn4bT7yBshZId0uPSofKWnwr66RyDaxWaFb/+f7OTDOWAsVNz+hOQLBWSjvbekr2xZY9ccS8TDHJbCQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@nuxt/kit': ^3.2.2
      '@vueuse/core': '*'
    peerDependenciesMeta:
      '@nuxt/kit':
        optional: true
      '@vueuse/core':
        optional: true
    dependencies:
      '@antfu/utils': 0.7.2
      '@rollup/pluginutils': 5.0.2
      local-pkg: 0.4.3
      magic-string: 0.30.0
      minimatch: 9.0.0
      unimport: 3.0.6
      unplugin: 1.3.1
    transitivePeerDependencies:
      - rollup
    dev: true

  /unplugin-icons@0.16.1:
    resolution: {integrity: sha512-qTunFUkpAyDnwzwV7YV1ZgCWRYfLuURcCurhhXOWMy2ipY88qx1pADvral2hJu4Xymh0X0t3Zcll3BIru2AVLQ==}
    peerDependencies:
      '@svgr/core': '>=7.0.0'
      '@vue/compiler-sfc': ^3.0.2 || ^2.7.0
      vue-template-compiler: ^2.6.12
      vue-template-es2015-compiler: ^1.9.0
    peerDependenciesMeta:
      '@svgr/core':
        optional: true
      '@vue/compiler-sfc':
        optional: true
      vue-template-compiler:
        optional: true
      vue-template-es2015-compiler:
        optional: true
    dependencies:
      '@antfu/install-pkg': 0.1.1
      '@antfu/utils': 0.7.2
      '@iconify/utils': 2.1.5
      debug: 4.3.4
      kolorist: 1.7.0
      local-pkg: 0.4.3
      unplugin: 1.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /unplugin-vue-components@0.24.1(vue@3.4.15):
    resolution: {integrity: sha512-T3A8HkZoIE1Cja95xNqolwza0yD5IVlgZZ1PVAGvVCx8xthmjsv38xWRCtHtwl+rvZyL9uif42SRkDGw9aCfMA==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/parser': ^7.15.8
      '@nuxt/kit': ^3.2.2
      vue: 2 || 3
    peerDependenciesMeta:
      '@babel/parser':
        optional: true
      '@nuxt/kit':
        optional: true
    dependencies:
      '@antfu/utils': 0.7.2
      '@rollup/pluginutils': 5.0.2
      chokidar: 3.5.3
      debug: 4.3.4
      fast-glob: 3.2.12
      local-pkg: 0.4.3
      magic-string: 0.30.0
      minimatch: 7.4.6
      resolve: 1.22.3
      unplugin: 1.3.1
      vue: 3.4.15
    transitivePeerDependencies:
      - rollup
      - supports-color
    dev: true

  /unplugin@1.3.1:
    resolution: {integrity: sha512-h4uUTIvFBQRxUKS2Wjys6ivoeofGhxzTe2sRWlooyjHXVttcVfV/JiavNd3d4+jty0SVV0dxGw9AkY9MwiaCEw==}
    dependencies:
      acorn: 8.11.2
      chokidar: 3.5.3
      webpack-sources: 3.2.3
      webpack-virtual-modules: 0.5.0
    dev: true

  /unset-value@1.0.0:
    resolution: {integrity: sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      has-value: 0.3.1
      isobject: 3.0.1

  /upath@1.2.0:
    resolution: {integrity: sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=}
    engines: {node: '>=4'}
    requiresBuild: true
    dev: false
    optional: true

  /update-browserslist-db@1.0.11(browserslist@4.21.5):
    resolution: {integrity: sha512-dCwEFf0/oT85M1fHBg4F0jtLwJrutGoHSQXCh7u4o2t1drG+c0a9Flnqww6XUKSfQMPpJBRjU8d4RXB09qtvaA==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.21.5
      escalade: 3.1.1
      picocolors: 1.0.0
    dev: true

  /uri-js@4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=}
    dependencies:
      punycode: 2.3.0

  /urix@0.1.0:
    resolution: {integrity: sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=}

  /url@0.11.4:
    resolution: {integrity: sha512-oCwdVC7mTuWiPyjLUz/COz5TLk6wgp0RCsN+wHZ2Ekneac9w8uuV0njcbbie2ME+Vs+d6duwmYuR3HgQXs1fOg==}
    engines: {node: '>= 0.4'}
    dependencies:
      punycode: 1.4.1
      qs: 6.13.0
    dev: false

  /use@3.1.1:
    resolution: {integrity: sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=}
    engines: {node: '>=0.10.0'}

  /util-deprecate@1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=}

  /util@0.10.4:
    resolution: {integrity: sha1-OqASW/5mikZy3liFfTrOJ+y3aQE=}
    dependencies:
      inherits: 2.0.3
    dev: false

  /util@0.11.1:
    resolution: {integrity: sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=}
    dependencies:
      inherits: 2.0.3
    dev: false

  /v-title@2.1.8(vue@3.4.15):
    resolution: {integrity: sha512-SX7RvD26kGoYSvUfp0Yh9IBvXPYmErvTTsTFtSAfnH0YadAGPz1idC6c0j3pajdjFccoUVwlS1psFMXdgDqz+g==}
    peerDependencies:
      vue: ^*
    dependencies:
      core-js: 3.30.1
      style-inject: 0.3.0
      tippy.js: 6.3.7
      vue: 3.4.15
      vue-component-pluggable: 1.0.2
    dev: false

  /vary@1.1.2:
    resolution: {integrity: sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=}
    engines: {node: '>= 0.8'}
    dev: true

  /vector-tile@1.3.0:
    resolution: {integrity: sha1-BtUWqDsGPwTILvU5zxuxrr62lrQ=}
    deprecated: 'This module has moved: please install @mapbox/vector-tile instead'
    dependencies:
      point-geometry: 0.0.0
    dev: false

  /vite-plugin-legacy-qiankun@0.0.7:
    resolution: {integrity: sha512-EWENHoQtwjU8jjobaKSc4NHB1Zx2QvjBKgsq5LxVFnK5hFXWUst8nIw8EcpKuw/e8SSFGVIcFeAj4WcSCilMBw==}
    dev: true

  /vite-plugin-svg-icons@2.0.1(vite@4.5.0):
    resolution: {integrity: sha512-6ktD+DhV6Rz3VtedYvBKKVA2eXF+sAQVaKkKLDSqGUfnhqXl3bj5PPkVTl3VexfTuZy66PmINi8Q6eFnVfRUmA==}
    peerDependencies:
      vite: '>=2.0.0'
    dependencies:
      '@types/svgo': 2.6.4
      cors: 2.8.5
      debug: 4.3.4
      etag: 1.8.1
      fs-extra: 10.1.0
      pathe: 0.2.0
      svg-baker: 1.7.0
      svgo: 2.8.0
      vite: 4.5.0(sass@1.62.0)(terser@5.30.3)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /vite@4.5.0(sass@1.62.0)(terser@5.30.3):
    resolution: {integrity: sha512-ulr8rNLA6rkyFAlVWw2q5YJ91v098AFQ2R0PRFwPzREXOUJQPtFUG0t+/ZikhaOCDqFoDhN6/v8Sq0o4araFAw==}
    engines: {node: ^14.18.0 || >=16.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': '>= 14'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
    dependencies:
      esbuild: 0.18.20
      postcss: 8.4.31
      rollup: 3.29.4
      sass: 1.62.0
      terser: 5.30.3
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /vm-browserify@1.1.2:
    resolution: {integrity: sha1-eGQcSIuObKkadfUR56OzKobl3aA=}
    dev: false

  /vue-component-pluggable@1.0.2:
    resolution: {integrity: sha512-/PT/uOfPC9ES1bF8+dFpgE69qyhYEgYC09y/J1bUgyu1uEVkKVQoBdnbikwDIeG6/Rfp1G15mtkHHjtEUvf51Q==}
    dependencies:
      vue: 2.7.16
    dev: false

  /vue-demi@0.14.0(vue@3.4.15):
    resolution: {integrity: sha512-gt58r2ogsNQeVoQ3EhoUAvUsH9xviydl0dWJj7dabBC/2L4uBId7ujtCwDRD0JhkGsV1i0CtfLAeyYKBht9oWg==}
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
    dependencies:
      vue: 3.4.15
    dev: false

  /vue-demi@0.14.10(vue@3.4.15):
    resolution: {integrity: sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==}
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
    dependencies:
      vue: 3.4.15
    dev: false

  /vue-eslint-parser@9.1.1(eslint@8.38.0):
    resolution: {integrity: sha512-C2aI/r85Q6tYcz4dpgvrs4wH/MqVrRAVIdpYedrxnATDHHkb+TroeRcDpKWGZCx/OcECMWfz7tVwQ8e+Opy6rA==}
    engines: {node: ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '>=6.0.0'
    dependencies:
      debug: 4.3.4
      eslint: 8.38.0
      eslint-scope: 7.2.0
      eslint-visitor-keys: 3.4.0
      espree: 9.5.1
      esquery: 1.5.0
      lodash: 4.17.21
      semver: 7.5.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /vue-observe-visibility@2.0.0-alpha.1(vue@3.4.15):
    resolution: {integrity: sha1-Hk7aexJWIWHViYS34N6mdtg72xM=}
    peerDependencies:
      vue: ^3.0.0
    dependencies:
      vue: 3.4.15
    dev: false

  /vue-resize@2.0.0-alpha.1(vue@3.4.15):
    resolution: {integrity: sha1-Q+63nnT+vpMrmyDFxX4OvBTi3zo=}
    peerDependencies:
      vue: ^3.0.0
    dependencies:
      vue: 3.4.15
    dev: false

  /vue-router@4.1.6(vue@3.4.15):
    resolution: {integrity: sha512-DYWYwsG6xNPmLq/FmZn8Ip+qrhFEzA14EI12MsMgVxvHFDYvlr4NXpVF5hrRH1wVcDP8fGi5F4rxuJSl8/r+EQ==}
    peerDependencies:
      vue: ^3.2.0
    dependencies:
      '@vue/devtools-api': 6.5.0
      vue: 3.4.15
    dev: false

  /vue-virtual-scroller@2.0.0-beta.8(vue@3.4.15):
    resolution: {integrity: sha512-b8/f5NQ5nIEBRTNi6GcPItE4s7kxNHw2AIHLtDp+2QvqdTjVN0FgONwX9cr53jWRgnu+HRLPaWDOR2JPI5MTfQ==}
    peerDependencies:
      vue: ^3.2.0
    dependencies:
      mitt: 2.1.0
      vue: 3.4.15
      vue-observe-visibility: 2.0.0-alpha.1(vue@3.4.15)
      vue-resize: 2.0.0-alpha.1(vue@3.4.15)
    dev: false

  /vue@2.7.16:
    resolution: {integrity: sha512-4gCtFXaAA3zYZdTp5s4Hl2sozuySsgz4jy1EnpBHNfpMa9dK1ZCG7viqBPCwXtmgc8nHqUsAu3G4gtmXkkY3Sw==}
    dependencies:
      '@vue/compiler-sfc': 2.7.16
      csstype: 3.1.3
    dev: false

  /vue@3.4.15:
    resolution: {integrity: sha512-jC0GH4KkWLWJOEQjOpkqU1bQsBwf4R1rsFtw5GQJbjHVKWDzO6P0nWWBTmjp1xSemAioDFj1jdaK1qa3DnMQoQ==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@vue/compiler-dom': 3.4.15
      '@vue/compiler-sfc': 3.4.15
      '@vue/runtime-dom': 3.4.15
      '@vue/server-renderer': 3.4.15(vue@3.4.15)
      '@vue/shared': 3.4.15

  /watchpack-chokidar2@2.0.1:
    resolution: {integrity: sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=}
    requiresBuild: true
    dependencies:
      chokidar: 2.1.8
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /watchpack@1.7.5:
    resolution: {integrity: sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=}
    dependencies:
      graceful-fs: 4.2.11
      neo-async: 2.6.2
    optionalDependencies:
      chokidar: 3.5.3
      watchpack-chokidar2: 2.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /webpack-sources@1.4.3:
    resolution: {integrity: sha1-7t2OwLko+/HL/plOItLYkPMwqTM=}
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1
    dev: false

  /webpack-sources@3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==}
    engines: {node: '>=10.13.0'}
    dev: true

  /webpack-virtual-modules@0.5.0:
    resolution: {integrity: sha512-kyDivFZ7ZM0BVOUteVbDFhlRt7Ah/CSPwJdi8hBpkK7QLumUqdLtVfm/PX/hkcnrvr0i77fO5+TjZ94Pe+C9iw==}
    dev: true

  /webpack@4.47.0:
    resolution: {integrity: sha512-td7fYwgLSrky3fI1EuU5cneU4+pbH6GgOfuKNS1tNPcfdGinGELAqsb/BP4nnvZyKSG2i/xFGU7+n2PvZA8HJQ==}
    engines: {node: '>=6.11.5'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
      webpack-command: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
      webpack-command:
        optional: true
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-module-context': 1.9.0
      '@webassemblyjs/wasm-edit': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
      acorn: 6.4.2
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)
      chrome-trace-event: 1.0.4
      enhanced-resolve: 4.5.0
      eslint-scope: 4.0.3
      json-parse-better-errors: 1.0.2
      loader-runner: 2.4.0
      loader-utils: 1.4.2
      memory-fs: 0.4.1
      micromatch: 3.1.10
      mkdirp: 0.5.6
      neo-async: 2.6.2
      node-libs-browser: 2.2.1
      schema-utils: 1.0.0
      tapable: 1.1.3
      terser-webpack-plugin: 1.4.6(webpack@4.47.0)
      watchpack: 1.7.5
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /whatwg-fetch@2.0.4:
    resolution: {integrity: sha1-3eal3zFfnTmZGqF2IYU9cguFVm8=}
    dev: false

  /which-boxed-primitive@1.0.2:
    resolution: {integrity: sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=}
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4
    dev: true

  /which-typed-array@1.1.9:
    resolution: {integrity: sha512-w9c4xkx6mPidwp7180ckYWfMmvxpjlZuIudNtDf4N/tTAUB8VJbX25qZoAsrtGuYNnGw3pa0AXgbGKRB8/EceA==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.0
      is-typed-array: 1.1.10
    dev: true

  /which@2.0.2:
    resolution: {integrity: sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /wicket@1.3.8:
    resolution: {integrity: sha1-b/mBD5TuCTMQ1o9msggDb9+XTfo=}
    dev: false

  /word-wrap@1.2.3:
    resolution: {integrity: sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=}
    engines: {node: '>=0.10.0'}
    dev: true

  /worker-farm@1.7.0:
    resolution: {integrity: sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=}
    dependencies:
      errno: 0.1.8
    dev: false

  /wrappy@1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=}

  /wujie-polyfill@1.0.11:
    resolution: {integrity: sha512-3U3x4UONr+Ne2NfVqknmQ031j6kI0HZDgmHPngeNBHxR462Qkdoydj4EvENEyFLh5gwd6fb4sCb820yNuGeB0Q==}
    dev: false

  /wujie@1.0.22:
    resolution: {integrity: sha512-gzx13fp9hgTHdV9XetkVmp794uDSR93Zs9jLr891RaWRuMiLFoxh3Pe4qbmW604SxI8nMTHeIRydbgC7YxQ50Q==}
    dependencies:
      '@babel/runtime': 7.21.0
    dev: false

  /xml-name-validator@4.0.0:
    resolution: {integrity: sha1-eaAG4uYxSahgDxVDDwpHJdFSSDU=}
    engines: {node: '>=12'}
    dev: true

  /xmldom@0.1.31:
    resolution: {integrity: sha1-t2yaG9nwqXN+WnLcNyMc84N14v8=}
    engines: {node: '>=0.1'}
    dev: false

  /xtend@4.0.2:
    resolution: {integrity: sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=}
    engines: {node: '>=0.4'}
    dev: false

  /y18n@4.0.3:
    resolution: {integrity: sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=}
    dev: false

  /yallist@3.1.1:
    resolution: {integrity: sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=}

  /yallist@4.0.0:
    resolution: {integrity: sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=}
    dev: true

  /yocto-queue@0.1.0:
    resolution: {integrity: sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=}
    engines: {node: '>=10'}
    dev: true

  /zrender@5.5.0:
    resolution: {integrity: sha512-O3MilSi/9mwoovx77m6ROZM7sXShR/O/JIanvzTwjN3FORfLSr81PsUGd7jlaYOeds9d8tw82oP44+3YucVo+w==}
    dependencies:
      tslib: 2.3.0
    dev: false
