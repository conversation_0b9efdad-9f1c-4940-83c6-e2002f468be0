<template>
    <app-business-panel class="expire-returned-bar-line-chart" title="领用/借用到期分析" mode="card">
        <template #header-append>
            <ecp-tag v-model="tagType" :options="tagTypeOptions" @change="changeMonth" />
        </template>
        <template #default>
            <div class="expire-returned-bar-line-chart__content" v-loading="loading">
                <template v-if="graphData?.source?.length">
                    <ecp-chart-bar-line v-bind="chartProps" :key="`${graphTimestamp}_bar_line`" />
                </template>
                <ecp-empty v-else />
            </div>
        </template>
    </app-business-panel>
</template>

<script setup>
import { OfficeAssetAnalysis } from '@api/asset-analysis/office-asset-analysis';
import { CHART_COLOR_LIST } from '@constants/enum-config';
import dayjs from 'dayjs';
import { formatXAxis } from '@utils/format';

const loading = ref(false);

const tagType = ref('currentMonth');

const tagTypeOptions = [
    { label: '本月到期', value: 'currentMonth' },
    { label: '下月到期', value: 'nextMonth' }
];

const legend = ref({
    top: 20,
    icon: 'emptyCircle'
});

const graphData = ref({
    dimensions: [],
    source: []
});

const graphTimestamp = ref(Date.now());

const unit = '个';

const defaultOptions = {
    color: CHART_COLOR_LIST,
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    grid: {
        top: 40,
        right: 0,
        bottom: 0
    },
    xAxis: {
        axisLabel: {
            textStyle: {
                color: 'rgba(0, 0, 0, 0.45)'
            },
            rotate: 60,
            formatter: (value) => formatXAxis(value,6),
        }
    },
    yAxis: [
        {
            nameTextStyle: {
                padding: [0, 0, 0, -20],
                align: 'left'
            }
        },
        {
            show: false
        }
    ],
    series: [
        {
            type: 'bar',
            itemStyle: {
                borderRadius: 10
            }
        },
        {
            type: 'line',
            lineStyle: {
                width: 0
            },
            symbol: 'emptyCircle',
            symbolSize: 6
        }
    ],
    tooltip: {
        formatter: (params) => {
            const xName = params[0].name;
            let html = '';
            for (const k in params) {
                const { color, value, seriesName } = params[k];
                if (value[seriesName] !== null) {
                    html += `
                            <div class="ecp-chart-tooltip-item">
                               <span class="ecp-chart-tooltip-label" style="--color: ${color}">${seriesName}</span>
                               <span class="ecp-chart-tooltip-value">
                                   <i class="ecp-chart-tooltip-value-num">${value[seriesName]}</i>
                                   <i class="ecp-chart-tooltip-value-unit">${unit}</i>
                               </span>
                            </div>
                        `;
                }
            }
            return `
        <div class="ecp-chart-tooltip-wrapper is-white">
            <div class="ecp-chart-tooltip-head"><span>${xName}</span></div>
            ${html}
        </div>
        `;
        }
    }
};

const chartProps = computed(() => {
    const defaultProps = {
        theme: 'whiteTheme',
        data: graphData.value,
        legend: legend.value,
        yName: '单位：个',
        unit
    };
    return {
        ...defaultProps,
        option: {
            ...defaultOptions,
            barWidth: 10
        }
    };
});

const getChartData = async () => {
    loading.value = true;
    graphData.value.source = [];

    try {
        const date = tagType.value === 'currentMonth' ? dayjs().format('YYYY-MM') : dayjs().add(1, 'month').format('YYYY-MM');
        const expireParams = { createDate: date };
        const [expireData, returnedData] = await Promise.all([
            OfficeAssetAnalysis.getUnitExpireDevice(expireParams),
            OfficeAssetAnalysis.getUnitReturnedDevice(expireParams)
        ]);
        const dimensions = ['所属单位', '本月到期数量', '已还数量'];
        // 取expireData和returnedData的DataName的并集
        const dataNameList = [...new Set([...(expireData?.Data || []).map(item => item.DataName), ...(returnedData?.Data || []).map(item => item.DataName)])];
        const source = (dataNameList || []).map(item => {
            const expireDataItem = (expireData?.Data || []).find(e => e.DataName === item);
            const returnedDataItem = (returnedData?.Data || []).find(e => e.DataName === item);
            return {
                所属单位: item,
                本月到期数量: expireDataItem ? expireDataItem.DataCount : null,
                已还数量: returnedDataItem ? returnedDataItem.DataCount : null
            };
        });

        graphData.value = {
            dimensions,
            source
        };
    } catch (error) {
        console.log('%c getTransferStatDataTrend Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    }

    loading.value = false;
};
const changeMonth = (value) => {
    // console.log('tagType', value);
    getChartData();
};

onMounted(() => {
    getChartData();
});

</script>

<style scoped lang="scss">
.expire-returned-bar-line-chart {
    overflow: hidden;

    &__content {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
}
</style>
