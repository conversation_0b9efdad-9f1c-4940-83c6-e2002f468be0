<template>
    <app-business-panel class="sporadic-land-bar-chart" title="零星用地资源面积" mode="card" v-loading="loading">
        <template #default>
            <div class="sporadic-land-bar-chart__content">
                <template v-if="graphData?.source?.length">
                    <ecp-chart-base-bar v-bind="chartProps" :key="`${graphTimestamp}_bar`" />
                </template>
                <ecp-empty v-else />
            </div>
        </template>
    </app-business-panel>
</template>

<script setup>
import { LandAssetApi } from '@api/asset-panorama/land-asset';
import { CHART_COLOR_LIST } from '@constants/enum-config';

const loading = ref(false);

const legend = ref({
    show: false
});

const graphData = ref({
    dimensions: [],
    source: []
});

const graphTimestamp = ref(Date.now());

const defaultOptions = {
    color: CHART_COLOR_LIST,
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    grid: {
        top: 40,
        right: 0,
        bottom: 0
    },
    xAxis: {
        axisLabel: {
            textStyle: {
                color: 'rgba(0, 0, 0, 0.45)'
            }
        }
    },
    yAxis: {
        nameTextStyle: {
            padding: [0, 0, 0, -20],
            align: 'left'
        }
    },
    series: [
        {
            type: 'bar',
            itemStyle: {
                borderRadius: 10
            }
        }
    ],
    barWidth: 15
};

const chartProps = computed(() => {
    const defaultProps = {
        theme: 'whiteTheme',
        data: graphData.value,
        legend: legend.value,
        yName: '单位：亩',
        unit: '亩'
    };
    return {
        ...defaultProps,
        option: {
            ...defaultOptions
        }
    };
});

const getChartData = async () => {
    console.log('%c getChartData', 'font-size:18px;color:gold;font-weight:700;');

    loading.value = true;
    graphData.value.source = [];

    try {
        const res = await LandAssetApi.getSporadicArea();

        const dimensions = ['类目名称', '面积'];
        const source = (res || []).map(item => ({
            类目名称: item.DataName,
            面积: item.Percentage
        }));
        graphData.value = {
            dimensions,
            source
        };
    } catch (error) {
        console.log('%c getTransferStatDataTrend Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    }

    loading.value = false;
};

onActivated(() => {
    getChartData();
});

</script>

<style scoped lang="scss">
.sporadic-land-bar-chart {
    overflow: hidden;

    &__content {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
}
</style>
