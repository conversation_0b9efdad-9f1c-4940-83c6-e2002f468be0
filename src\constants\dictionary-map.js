import * as Api from '@api/index';

import { formatDict } from '@utils/helper';

/**
 * 字典列表Kind配置
 */
export const DICTIONARY_MAP = {
    /**
     * ↓↓↓↓↓↓↓↓ 公服字典 ↓↓↓↓↓↓↓↓
     * 格式 [`${KEY}`]: KIND, 例如: DISCTRICT: 1
     */

    /**
     * ↑↑↑↑↑↑↑↑ 公服字典 ↑↑↑↑↑↑↑↑
     */

    /**
     * ↓↓↓↓↓↓↓↓ 自定义枚举字典 ↓↓↓↓↓↓↓↓
     * [`CUSTOM_${KEY}`]: KIND, 例如: CUSTOM_DISCTRICT: 'asdasd'
     */

    /**
     * ↑↑↑↑↑↑↑↑ 自定义枚举字典 ↑↑↑↑↑↑↑↑
     */
};

/**
 * 获取自定义枚举字典方法枚举
 * 注意：能缓存的才在这里加
 */
export const CUSTOM_DICT_MAP = {
    // [DICTIONARY_MAP.CUSTOM_VISIT_ENTERPRISE]:
    //     Api.PassBooking.getDictionaryVisitEnterprise

};
