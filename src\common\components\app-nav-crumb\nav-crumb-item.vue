<!--
<docs>
# app-nav-crumb-item
- 面包屑标题栏子项

## props
- tag 子项标识，可用于回调回传
- to 跳转的路径
- isCurrent 判断是否是当前项

## event
- onCrumbItemClick 监听标题点击

</docs>
 -->

<template>
    <div class="app-nav-crumb-item">
        <component :is="isSubModule?'a':'router-link'" :class="{ 'app-crumb-link': !isCurrent }" v-bind="{ ...navProps }"
                   @click.stop="onClickRouterLink" v-if="isRouterLink">
            <slot></slot>
        </component>
        <component :is="isCurrent?'span':'a'" :class="{ 'app-crumb-link': !isCurrent }" @click.stop="onLinkClick" v-else>
            <slot></slot>
        </component>
        <span v-if="!isCurrent" class="app-nav-crumb-delimiter">/</span>
    </div>
</template>

<script>
import { MicroUtils } from '@ecp/ecp-ui-plus';
export default {
    name: 'app-nav-crumb-item',
    props: {
        tag: Number || String,
        isRoute: {
            type: Boolean,
            default: true
        },
        to: {
            type: String,
            default: ''
        },
        isCurrent: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        isSubModule () {
            return window.__POWERED_BY_QIANKUN__;
        },
        navProps () {
            return this.isSubModule
                ? {}
                : {
                    to: this.to,
                    replace: true
                };
        },
        isRouterLink () {
            return (
                this.isRoute &&
                this.to &&
                (this.isSubModule || this.$route.fullPath !== this.to)
            );
        }
    },
    methods: {
        onClickRouterLink () {
            if (!this.isSubModule) {
                return;
            }
            // eslint-disable-next-line
            const baseUrl = (PACKAGE_NAME || '').replace(/\/$/, '');
            const path = MicroUtils.formatRoute(
                `${baseUrl}/#${this.to}`,
                '/s-'
            );
            history.pushState({}, '', path);
        },
        onLinkClick () {
            const self = this;
            if (self.isCurrent) return;
            self.$emit('onCrumbItemClick', self.tag);
        }
    }
};
</script>

<style lang="scss" scoped>
.app-nav-crumb-item {
    display: inline-flex;
    font-size: var(--font-size-base);
    line-height: var(--font-line-height-primary);
    align-items: center;

    &:is(.single > .app-nav-crumb-item) {
        font-size: var(--font-size-medium);
        line-height: var(--font-line-height-medium);
    }

    .app-crumb-link {
        color: var(--color-text-secondary);
        cursor: pointer;
        text-decoration: none;

        &:hover {
            color: var(--color-primary-light-4);
        }

        &:focus {
            outline: none;
            color: var(--color-primary-light-4);
        }
    }

    .app-nav-crumb-delimiter {
        display: inline-block;
        margin-left: var(--spacer-medium-2);
        margin-right: var(--spacer-medium-2);
        font-size: var(--font-size-small);
        line-height: var(--font-line-height-small);
        align-self: flex-start;
    }
}
</style>
