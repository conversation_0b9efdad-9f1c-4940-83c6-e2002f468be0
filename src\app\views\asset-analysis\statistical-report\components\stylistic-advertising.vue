<template>
    <div class="asset-change">
        <ecp-layout-pagination>
            <template #head>
                <div class="header-toolbar">
                    <el-date-picker v-model="_createDate" type="month" placeholder="选择年月"
                        :disabled-date="disabledFutureDates" style="width: 150px" :clearable="false" />
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading" @click="ExportAssetChangeTable" />
                </div>
            </template>
            <template #content>
                <app-dynamic-table :loading="loading" :table-data="tableData" :table-config="tableConfig" />
            </template>
        </ecp-layout-pagination>
    </div>
</template>

<script setup>
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import { STYLISTIC_ADVERTISING_TABLE_CONFIG } from '../constants';
import { StatisticalReport } from '@api/asset-analysis/statistical-report';

const props = defineProps({
    disabledFutureDates: {
        type: Function,
        default: () => true
    }
});

const _createDate = ref(new Date());
const loading = ref(false);
const tableData = ref([]);
const firstCol = ref([]);
const timeSpan = computed(() => {
    const arr = [];
    const list = tableData.value.map(item => item.Source);
    const copy = new Set(list);
    copy.forEach((item) => {
        const count = list.filter(i => i === item).length;
        arr.push(...[count, ...Array.from({ length: count - 1 }, () => 0)]);
    });
    return arr;
});
const tableConfig = computed(() => ({
    ...STYLISTIC_ADVERTISING_TABLE_CONFIG,
    props: { spanMethod }
}));

const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
    const span = timeSpan.value;
    if (columnIndex === 1 && rowIndex === 1) {
        return {
            rowspan: span[rowIndex],
            colspan: 5
        };
    }
    if (columnIndex === 2 && rowIndex === 1) {
        return {
            rowspan: span[rowIndex],
            colspan: 3
        };
    }
    if (columnIndex >= 3 && rowIndex === 1 && columnIndex !== 9) {
        return {
            rowspan: 0,
            colspan: 0
        };
    }

    if (columnIndex === 9 && rowIndex === 1) {
        return {
            rowspan: 1,
            colspan: 1
        };
    }
    return {
        rowspan: 1,
        colspan: 1
    };
};
const GetAssetChangeTable = async () => {
    try {
        loading.value = true;
        const currentMonth = dayjs(_createDate.value).format('YYYY-MM');
        const response = await StatisticalReport.ShowAdvertisingStats({ createDate: currentMonth });
        const data = response.Data;

        const detailedData = data[1]['数量,面积'];
        const trainDisplayBoard = data[2]['列车展板'];
        const fieldName = Object.keys(data[2])[0];
        const processedData = [
            {
                Source: '广告设置位',
                totalAdvertisingResources_count: detailedData[3][0],
                totalAdvertisingResources_area: detailedData[3][1],
                publicServiceAdvertisement_count: detailedData[0][0],
                publicServiceAdvertisement_area: detailedData[0][1],
                commercialAdvertisementsAreRented_count: detailedData[1][0],
                commercialAdvertisementsAreRented_value: detailedData[1][1],
                vacantCommercialAdvertisement_count: detailedData[2][0],
                vacantCommercialAdvertisement_area: detailedData[2][1],
                LettingRate: detailedData[4][0]
            },
            {
                Source: fieldName,
                totalAdvertisingResources_count: trainDisplayBoard[0],
                totalAdvertisingResources_area: trainDisplayBoard[1]
            }
        ];

        tableData.value = processedData;
    } catch (e) {
        console.log('%c GetAssetChangeTable', 'font-size:18px;color:green;font-weight:700;', e);
    } finally {
        loading.value = false;
    }
};

const ExportAssetChangeTable = async () => {
    try {
        if (tableData.value.length > 0) {
            const response = await StatisticalReport.ExportAdvertisingStats({ createDate: dayjs(_createDate.value).format('YYYY-MM') });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (e) {
        console.log('%c ExportAssetChangeTable', 'font-size:18px;color:green;font-weight:700;', e);
        ElMessage.error('导出失败');
    }
};

watch(_createDate, GetAssetChangeTable, { immediate: true });
</script>

<style scoped lang="scss">
.asset-change {
    height: 100%;
    width: 100%;

    :deep(.ecpp-layout-pagination) {
        display: flex;
        flex-direction: column;

        .ecpp-layout-pagination-content {
            flex: 1;
            display: flex;

            .content-main {
                flex: 1;
                display: flex;

                .elp-table {
                    flex: 1;
                }
            }
        }
    }
}
</style>
