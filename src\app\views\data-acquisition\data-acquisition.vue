<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder v-loading="loading">
        <template #content>
            <app-business-panel mode="card" class="data-acquisition-wrapper">
                <template #header>
                    <div class="data-acquisition-header_left">
                        <el-select v-model="uploadType" placeholder="选择上传数据表类型" filterable>
                            <el-option v-for="item in uploadTypeList" :key="item.Id" :label="item.UploadTypeName"
                                :value="item.Id"></el-option>
                        </el-select>
                        <el-date-picker v-model="selectedDate" type="month" value-format="YYYY-MM"
                            placeholder="选择数据表所属月份" @change="handleDateChange" :disabled-date="disabledFutureDates"
                            :clearable="false" style="width:150px" />
                    </div>
                </template>
                <template #header-append>
                    <div class="data-acquisition-header_right">
                        <ecp-button text="下载模板" icon="ecp-icon-download" @click="downloadTemplate"
                            :loading="downloadLoading"></ecp-button>
                        <ecp-button text="历史记录" icon="ecp-icon-detail" @click="showHistoryRecord"></ecp-button>
                    </div>
                </template>
                <template #content>
                    <el-upload class="data-acquisition-content" drag accept=".xls,.xlsx" :show-file-list="false"
                        :action="action" :data="uploadParams" :before-upload="handleBeforeUpload"
                        :on-success="handleUploadSuccess" :on-error="handleUploadError" v-loading="uploadLoading">
                        <ecp-icon class="upload__icon" icon="ecp-icon-document-excel"></ecp-icon>
                        <div class="upload__text">
                            将文件拖至此处，或<sapn class="link">点击上传</sapn>
                        </div>
                    </el-upload>
                    <el-dialog v-model="resultDetailVisible" width="500" title="导入结果" align-center
                        :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
                        <div v-loading="downloadErorLoading">
                            <div>本次导入【{{ currentFileName}}】文件</div>
                            <div>{{ resultDetail.Desc }}</div>
                            <span v-if="resultDetail.Path">下载：<span class="link"
                                    @click="downloadErrorDetail(resultDetail.Path)">点击查看失败文件</span></span>
                        </div>
                    </el-dialog>
                    <HistoryRecordDialog v-model:visible="historyRecordVisible" :uploadTypeList="uploadTypeList" />
                </template>
            </app-business-panel>
        </template>
    </app-form-page>
</template>

<script setup>
import { DataAcquisition } from '@api/data-acquisition';
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import { downloadBlobData } from '@utils/download';
import HistoryRecordDialog from './_components/history-record-dialog.vue';

const name = 'data-acquisition';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '数据采集'
        }
    ];
});

const loading = ref(false);
const downloadLoading = ref(false);
const uploadLoading = ref(false);
const downloadErorLoading = ref(false);

const uploadTypeList = ref([]);
const uploadType = ref('');

const selectedDate = ref(dayjs().format('YYYY-MM'));

const action = '/api/data-assets-analysis-service/uploadRecord/uploadData';
const uploadParams = computed(() => {
    return {
        uploadTypeId: uploadType.value,
        month: selectedDate.value
    };
});
const resultDetail = ref({
    Desc: '',
    Path: ''
});
const currentFileName = ref('');

const resultDetailVisible = ref(false);
const historyRecordVisible = ref(false);

// 禁用未来日期
const disabledFutureDates = (date) => {
    return date.getTime() > new Date().getTime();
};

// 日期选择
const handleDateChange = (date) => {
    if (date) {
        selectedDate.value = dayjs(date).format('YYYY-MM');
    } else {
        // 如果清空日期，设置为当前日期
        selectedDate.value = dayjs().format('YYYY-MM');
    }
};

// 获取上传类型列表
const getUploadTypeList = async () => {
    try {
        loading.value = true;
        const res = await DataAcquisition.getUploadTypeList();
        uploadTypeList.value = res?.Data || [];
        if (uploadTypeList.value.length > 0) {
            uploadType.value = uploadTypeList.value[0].Id;
        }
    } catch (error) {
        console.error(error);
    } finally {
        loading.value = false;
    }
};

// 下载模板
const downloadTemplate = async () => {
    if (!uploadType.value) {
        ElMessage.warning('请选择上传类型');
        return;
    }
    try {
        downloadLoading.value = true;
        const res = await DataAcquisition.downloadTemplate({ uploadTypeId: uploadType.value });
        downloadBlobData(res);
    } catch (error) {
        console.log('%c exportEvent error', 'color: red', error);
    } finally {
        downloadLoading.value = false;
    }
};

// 上传前校验excel格式
const handleBeforeUpload = (file) => {
    const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    if (!isExcel) {
        ElMessage.warning('仅支持excel表文件格式');
    } else {
        currentFileName.value = file.name;
    }
    uploadLoading.value = isExcel;
    return isExcel;
};

// 上传成功
const handleUploadSuccess = (res) => {
    uploadLoading.value = false;
    if (res && res.OpCode === 0) {
        resultDetail.value = {
            Path: res?.Data?.Path || '',
            Desc: (res?.Data?.Desc || '').split('\n').join('，')
        };
        resultDetailVisible.value = true;
    } else {
        ElMessage.error(res?.OpDesc || '上传失败');
    }
};

// 上传失败
const handleUploadError = (error) => {
    uploadLoading.value = false;
    console.error(error);
};

// 下载失败文件
const downloadErrorDetail = async (path) => {
    if (!path) {
        ElMessage.warning('无失败文件');
        return;
    }
    try {
        downloadErorLoading.value = true;
        const res = await DataAcquisition.downloadErrorDetail({ fileName: path });
        downloadBlobData(res);
    } catch (error) {
        console.log('%c exportEvent error', 'color: red', error);
    } finally {
        downloadErorLoading.value = false;
    }
};

const showHistoryRecord = () => {
    historyRecordVisible.value = true;
};

onMounted(() => {
    getUploadTypeList();
});

onActivated(() => {
    selectedDate.value = dayjs().format('YYYY-MM');
});

</script>

<style lang="scss" scoped>
$page-name: data-acquisition;

.#{$page-name} {
    &-wrapper {
        height: 100%;
        width: 100%;
    }

    &-header {
        &_left {
            display: flex;

            .elp-select {
                width: 200px;
                margin-right: var(--spacer);
            }
        }

        &_right {
            .elp-button {
                margin-left: var(--spacer);
            }
        }
    }

    &-content {
        margin-top: var(--spacer-large);

        :deep(.elp-upload) {
            --elp-upload-dragger-padding-horizontal: calc((100vh - 250px)/2);
        }

        .upload__icon {
            font-size: 64px;
        }

        .upload__text {
            margin-top: var(--spacer-large);
        }
    }
}
</style>
