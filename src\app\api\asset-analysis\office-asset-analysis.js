import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

export const OfficeAssetAnalysis = {
     /**
     * 部门下拉框数据
     */
     getOfficeAssetUnitQueryData(params) {
        return request({
            url: `${AssetsAnalysisService}/GetOfficeAssetUnitQueryData`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    },


     /**
     * 获取资产类型结构分布数据
     * @param {Object} params
     * @param {string} params.assetUnit - 选择单位
     * @param {string} params.createDate - 年月（格式: YYYY-MM）
     */
     getAssetTypeStructureData(params) {
        return request({
            url: `${AssetsAnalysisService}/assetTypeStructure`,
            method: 'get',
            params: params, 
        }).then(response => response.data)
          .catch(error => {
              console.error('请求失败', error);
              throw error;
          });
    },

    /**
     * 各类使用占用率-资产使用情况
     * @param {Object} params
     * @param {string} params.assetUnit 
     * @param {string} params.createDate
     */
       getUsageRatio(params) {
        return request({
            url: `${AssetsAnalysisService}/UsageRatio`,
            method: 'get',
            params: params, 
        }).then(({ data }) => data);
    },

    /**
     * 办公资产详细信息表
     * @param {Object} params
     * @param {array} params.assetUnits 
     * @param {string} params.createDate 
     * @param {string} params.pageNum
     * @param {string} params.pageSize
     */
      getOffAssetInfo (data) {
        return request({
            url: `${AssetsAnalysisService}/GetOffAssetInfo`,
            method: 'post',
            data,
        }).then(({ data }) => data);
    },

   /**
     * 导出
     */
   exportOffAssetInfo (data) {
    return request.post(`${AssetsAnalysisService}/ExportOffAssetInfo`, data, { responseType: 'blob' });},

    /**
     * 资产数量
     */
          getNumberOfAssets(params) {
            return request({
                url: `${AssetsAnalysisService}/NumberOfAssets`,
                method: "get",
                params: params
            }).then(({ data }) => data);
        },

    /**
     * 员工数量
     */
        getNumberOfEmployees(params) {
            return request({
                url: `${AssetsAnalysisService}/NumberOfEmployees`,
                method: "get",
                params: params
            }).then(({ data }) => data);
        },

    /**
     * 本月到期数量
     */
        getUnitExpireDevice(params) {
            return request({
                url: `${AssetsAnalysisService}/UnitExpireDevice`,
                method: "get",
                params: params
            }).then(({ data }) => data);
        },

    /**
     * 已还数量
     */
        getUnitReturnedDevice(params) {
            return request({
                url: `${AssetsAnalysisService}/UnitReturnedDevice`,
                method: "get",
                params: params
            }).then(({ data }) => data);
        },



};




