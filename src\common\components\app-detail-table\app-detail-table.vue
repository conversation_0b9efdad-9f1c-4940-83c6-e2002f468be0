<!--
<docs>
# 详情展示

## props
- rawDetail <Object> 详情
- cols <Array> 详情配置项
- labelWidth <Number|String> label的宽度, 默认200px
- column <Number> 列数
- simple <Boolean> 是否简易型详情, 默认 false
- patterned <Boolean> 是否放置于图案背景上, 默认 false
- orderAsCol <Boolean> 是否优先按垂直方向排列, 默认 false

### cols Array
- [col]

#### col
- key 标识
- label 标题
- labelHidden 是否隐藏当前项的标题
- labelClass 当前项的标题class
- valueClass 当前项的内容class
- span 项内容所占的列数
- rowSpan 项内容所占的行数
- slot 自定义内容 slotLabel 自定义标题
- ignoreKey 是否忽略key
- targetKey Array 忽略key 取targetKey对应key的值
- delimiter 文本分隔符
- formatterLabel 文本标题格式化函数
    params:
    - rawDetail 详情
    - config 当前配置项
    - cellValue 当前项的值
- formatter 文本格式化函数
    params:
    - rawDetail 详情
    - config 当前配置项
    - cellValue 当前项的值

</docs> -->

<template>
    <component :is="simple ? 'div' : 'table'" class="app-detail-table"
        :class="{ simple, patterned, 'with-divider': withDivider }" v-bind="getTableAttrs()"
        :style="{ '--app-detail-table-max-width': maxWith, '--detail-columns': column }">
        <template v-if="!simple">
            <colgroup>
                <template v-for="index in column" :key="`colgroup_${index}_label`">
                    <col class="app-detail-colgroup-item-label" :width="tableLabelWidth" />
                    <col class="app-detail-colgroup-item-value" width="auto" />
                </template>
            </colgroup>
            <tr class="app-detail-row" v-for="(row, rowIndex) in tableConfigList" v-bind="getTableRowAttrs()"
                :key="rowIndex">
                <template v-for="(col, colIndex) in getRowList(row)">
                    <template v-if="col && col['key']">
                        <td class="app-detail-column app-detail-column-label" :class="col['labelClass']"
                            :key="`${col['key'] || colIndex}_label`" v-bind="getTableCellLabelAttrs(col)"
                            v-if="!col['labelHidden']">
                            <template v-if="!!col['slotLabel']">
                                <slot :name="getSlotName(col['slotLabel'], true)" v-bind="{ col, rawDetail }" />
                            </template>
                            <span class="app-detail-column-text" v-else v-html="formatLabel(col)"></span>
                        </td>
                        <td class="app-detail-column app-detail-column-value" :class="col['valueClass']"
                            v-bind="getTableCellContentAttrs(col)" :key="col['key'] || colIndex">
                            <template v-if="!!col['slot'] && $slots[col['slot']]">
                                <slot :name="getSlotName(col['slot'])" v-bind="{ col, rawDetail }" />
                            </template>
                            <span :class="['app-detail-column-text', {
                                'empty-cell': isEmpty(formatData(col, true))
                            }]" v-else>{{ formatData(col) }}</span>
                        </td>
                    </template>
                </template>
            </tr>
        </template>
        <template v-else>
            <el-row class="app-detail-row" type="flex" v-bind="getTableRowAttrs()">
                <template v-for="(col, colIndex) in tableConfigList">
                    <template v-if="col && col['key']">
                        <el-col class="app-detail-column col"
                            :class="[col['cellClass'], { 'full-cell': col['fullCell'] && colIndex == tableConfigList.length - 1 }]"
                            v-bind="getTableCellAttrs(col)" :key="`${col['key'] || colIndex}`"
                            :style="getCellStyle(colIndex)">
                            <template v-if="!col['labelHidden']">
                                <template v-if="!!col['slotLabel']">
                                    <slot :name="getSlotName(col['slotLabel'], true)" v-bind="{ col, rawDetail }" />
                                </template>
                                <!-- <span class="app-detail-column-text label" :class="[{ 'has-colon': colon }]" v-else><span
                                          class="app-detail-column-text-word-list"><span class="app-detail-column-text-word"
                                              v-for="(item, index) in formatLabel(col)" :key="index">{{ item
                                              }}</span></span></span> -->
                                <span class="app-detail-column-text label"
                                    :class="[{ 'has-colon': colon, 'is-right': labelAlign === 'right' }]"
                                    :style="{ width: labelAlign === 'left' || labelAlign === 'right' ? getColLabelWidth(colIndex) : null }"
                                    v-else>{{
                                        formatLabel(col) }}</span>
                            </template>
                            <template v-if="!!col['slot'] && $slots[col['slot']]">
                                <slot :name="getSlotName(col['slot'])" v-bind="{ col, rawDetail }" />
                            </template>
                            <span :class="['app-detail-column-text', {
                                'empty-cell': isEmpty(formatData(col, true))
                            }]" v-else>{{ formatData(col) }}</span>
                        </el-col>
                    </template>
                </template>
            </el-row>
        </template>
    </component>
</template>

<script>
import _ from 'lodash-es';
import { isEmptyValue } from '@utils/helper';

import { estimateWidth } from '@utils/format';

export default {
    name: 'app-detail-table',
    props: {
        rawDetail: {
            type: Object,
            default: () => ({})
        },
        cols: {
            type: Array,
            default: () => []
        },
        labelWidth: {
            type: [String, Number],
            default: '200px'
        },
        labelAlign: {
            type: String,
            default: 'auto'
        },
        column: {
            type: Number,
            default: 2
        },
        autoWidth: Boolean,
        simple: Boolean,
        patterned: Boolean,
        withDivider: Boolean,
        orderAsCol: Boolean,
        colon: {
            type: Boolean,
            default: true
        }
    },
    data () {
        return {
            tableConfigList: []
        };
    },
    computed: {
        configLength () {
            return (this.tableConfigList || [])?.length || 0;
        },
        maxWith () {
            return this.autoWidth ? `${this.column * 25}%` : '';
        },
        tableLabelWidth () {
            const self = this;
            const labelWidth = self.labelWidth;

            if (typeof labelWidth === 'number') {
                return `${labelWidth}px`;
            } else {
                return labelWidth;
            }
        },
        colLabelWidthList () {
            if (!this.simple || !this.labelAlign || this.labelAlign === 'auto' || this.labelAlign === 'none') {
                return null;
            }
            const fontSize = 14;
            const labelWidthList = [];
            for (let colIndex = 0; colIndex < this.column; colIndex++) {
                const targetCol = colIndex % this.column;
                labelWidthList[colIndex] = (this.tableConfigList || []).map(col => {
                    if (col.labelHidden || col.slotLabel) {
                        return 0;
                    }
                    const labelText = this.formatLabel(col);
                    const labelEstimatedWidth = (estimateWidth(labelText) || 0) + 1.5;
                    return labelEstimatedWidth * fontSize / 2;
                }).filter((item, index) => index % this.column === targetCol).reduce((prev, curr) => Math.max(prev, curr), 0);
            }
            return labelWidthList;
        }
    },
    watch: {
        cols: {
            handler (newVal) {
                const self = this;

                self.tableConfigList = self.generateConfigRule(newVal);
            },
            deep: true,
            immediate: true
        },
        column: {
            handler () {
                const self = this;

                self.tableConfigList = self.generateConfigRule(this.cols);
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        getColLabelWidth (colIndex) {
            const targetCol = colIndex % this.column;

            if (!this.simple || !this.labelAlign || this.labelAlign === 'auto' || this.labelAlign === 'none' || Number.isNaN(targetCol)) {
                return null;
            }

            const labelWidth = this.colLabelWidthList[targetCol];

            return labelWidth ? `${labelWidth}px` : '';
        },
        getCellStyle (colIndex) {
            // 优先按垂直方向排列时，需要调整flex的order
            if (!this.orderAsCol) {
                return;
            }
            const orderRows = Math.ceil(this.configLength / (this.column || 2));
            return {
                '--cell-order': (colIndex + 1) % orderRows || orderRows
            };
        },
        getTableAttrs () {
            return this.simple
                ? {}
                : {
                    border: 1
                };
        },
        getTableRowAttrs () {
            return this.simple
                ? {
                    gutter: this.patterned ? 48 : 20
                }
                : {};
        },
        getTableCellLabelAttrs (col) {
            return this.simple
                ? {}
                : {
                    colspan: 1
                };
        },
        getTableCellContentAttrs (col) {
            return this.simple
                ? {}
                : {
                    colspan: this.getColSpan(col),
                    rowspan: this.getRowSpan(col)
                };
        },
        getTableCellAttrs (col) {
            return this.simple
                ? {
                    span: col.span || (24 / this.column)
                }
                : {};
        },
        getColSpan (col) {
            return +(col.span || 1);
        },
        getRowSpan (col) {
            return +(col.rowSpan || 1);
        },
        getRowList (row) {
            return Array.isArray(row.cols) ? row.cols : [];
        },
        getSimpleCols (tableConfigList) {
            return tableConfigList.reduce(
                (prev, row) => [
                    ...prev,
                    ...(Array.isArray(row.cols) ? row.cols : [])
                ],
                []
            );
        },
        getSlotName (slotName, isLabel = false) {
            return (isLabel ? `${slotName}_label` : slotName);
        },
        isEmpty (value) {
            return isEmptyValue(value);
        },
        formatLabel (config) {
            let label = '';
            if (typeof config?.formatterLabel === 'function') {
                label = config.formatterLabel(
                    this.rawDetail,
                    config,
                    this.rawDetail?.[config?.key]
                );
            } else {
                label = config.label;
            }
            return !isEmptyValue(label) ? label : null;
        },
        formatData (config, isRaw) {
            let value = '';
            if (typeof config?.formatter === 'function') {
                value = config.formatter(
                    this.rawDetail,
                    config,
                    this.rawDetail?.[config?.key]
                );
            } else if (
                config.ignoreKey &&
                Array.isArray(config.targetKey) &&
                config.targetKey.length > 0
            ) {
                value = config.targetKey
                    .map(key =>
                        this.rawDetail?.[key] === '' ||
                            this.rawDetail?.[key] === undefined
                            ? ''
                            : this.rawDetail?.[key]
                    )
                    .join(config.delimiter || '/');
            } else {
                value = this.rawDetail?.[config.key];
            }
            return !isEmptyValue(value) ? value : isRaw ? '' : null;
        },
        generateConfigRule (list) {
            const self = this;
            if (!Array.isArray(list)) return [];

            // 简单label:value布局
            if (this.simple) {
                const simpleList = _.cloneDeep(list);
                if (this.orderAsCol) {
                    const configLength = (simpleList || [])?.length || 0;
                    const column = this.column || 2;
                    const orderRows = Math.ceil(configLength / column);
                    const maxItems = orderRows * column;
                    // 优先按照垂直方形排列时，需要根据缺失项数手动塞缺失位置
                    if (configLength < maxItems - 1) {
                        const missingItems = maxItems - configLength;
                        for (
                            let startMissingIndex = column - missingItems;
                            startMissingIndex <= missingItems;
                            startMissingIndex++
                        ) {
                            simpleList.splice(
                                orderRows * (startMissingIndex + 1) - 1,
                                0,
                                {}
                            );
                        }
                    }
                }
                return simpleList;
            }

            // table布局

            const erRowColNum = self.column * 2; // 一行总列数

            // 格式化配置，转换为行列二维数组
            const { target } = list
                .filter(item => item?.key)
                .reduce(
                    ({ target, rowIndex, colCount }, curr) => {
                        if (!target[rowIndex]) {
                            target.push({
                                cols: []
                            });
                        }
                        target[rowIndex].cols.push({
                            ...curr,
                            rowIndex
                        });
                        const span = curr.span || 1;

                        // 计算项标题所占列数
                        const labelSpan =
                            curr.labelSpan || Number(!curr.labelHidden);

                        colCount = colCount + labelSpan + span;
                        if (colCount >= erRowColNum) {
                            colCount = 0;
                            rowIndex++;
                        }
                        return {
                            target,
                            rowIndex,
                            colCount
                        };
                    },
                    {
                        target: [],
                        rowIndex: 0,
                        colCount: 0
                    }
                );

            const endItem = target[target.length - 1] || {};
            const cols = endItem.cols;

            if (cols && cols.length > 0) {
                const endColCount = cols.reduce(
                    (pre, cur) => pre + self.getColSpan(cur) + 1,
                    0
                );

                if (endColCount < erRowColNum) {
                    const cols = endItem.cols;
                    cols[cols.length - 1] &&
                        !cols[cols.length - 1].span &&
                        (cols[cols.length - 1].span =
                            erRowColNum - endColCount + 1);
                    target[target.length - 1] = {
                        ...endItem,
                        cols
                    };
                }
            }

            return target;
        }
    }
};
</script>

<style lang="scss" scoped>
.app-detail-table {
    width: var(--app-detail-table-max-width, 100%);
    border-color: var(--border-color-light);
    table-layout: fixed;

    :deep(.app-detail-row) {
        &.elp-row {
            &.elp-row--flex {
                flex-wrap: wrap;
                row-gap: var(--spacer-small);
            }
        }

        .app-detail-column {
            padding: var(--spacer) var(--spacer-large);
            font-size: var(--font-size-base);
            white-space: pre-wrap;
            word-break: break-all;
            vertical-align: top;

            &.app-detail-column-label {
                text-align: right;
                background-color: var(--color-black-opacity-0-2);
                // width: 200px;
            }

            &.app-detail-column-value {
                // width: 584px;
            }

            .app-detail-column-text {
                // display: inline-block;
                font-size: var(--font-size-base);
                word-break: break-all;

                &.label {
                    white-space: nowrap;

                    &.is-right {
                        justify-content: flex-end;
                    }

                    &.has-colon::after {
                        content: ' : ';
                    }

                    &:not(.has-colon) {
                        margin-right: var(--spacer-medium);
                    }
                }
            }

            &.elp-col {
                padding: 0;
                display: flex;
                flex-direction: row;
                flex-wrap: nowrap;
                white-space: pre-wrap;

                &.col {
                    order: var(--cell-order);

                    &.full-cell {
                        width: 100%;
                    }
                }

                .app-detail-column-text-wrapper {
                    width: auto;
                    max-height: calc(var(--font-line-height-medium) * 2);
                    flex: 0 1 auto;
                    overflow: auto;
                }

                .app-detail-column-text {
                    // min-width: 70px;
                    // flex: 1 1 70px;
                    flex: 1 1 auto;
                    text-align: left;
                    color: var(--color-text-regular);
                    white-space: pre-wrap;

                    &.label {
                        flex: 0 0 auto;
                        color: var(--color-text-primary);
                        display: flex;
                    }

                    .app-detail-column-text-word-list {
                        flex: 1 1 auto;
                        display: inline-flex;
                        justify-content: space-between;
                    }

                    &.empty-cell {
                        color: var(--color-text-placeholder);
                    }
                }
            }
        }
    }
}

:deep(.app-detail-table) {

    &.patterned {
        --detail-seperator-color: rgba(61, 138, 255, 15%);
    }

    &.patterned {

        height: 100%;
        align-content: center;

        &.with-divider {
            background-image: linear-gradient(to right, transparent 0px, var(--detail-seperator-color) 0px, var(--detail-seperator-color) 1px, transparent 1px);
            background-size: calc((100% + 48px) / var(--detail-columns)) 100%;
            background-position: -24px center;
            background-repeat: repeat-x;
        }

        .app-detail-row {
            row-gap: var(--spacer-medium-2);

            .app-detail-column {
                &.elp-col {
                    .app-detail-column-text {
                        color: var(--color-text-regular);

                        &.label {
                            color: var(--color-text-secondary);
                        }

                        // &.empty-cell {
                        //     color: var(--color-text-mark);
                        // }
                    }

                    .main-item-text {
                        color: var(--color-text-primary);
                        font-size: var(--font-size-large);
                        line-height: var(--font-line-height-medium);
                        transform: translateY(-8px);
                    }
                }
            }
        }
    }
}
</style>
