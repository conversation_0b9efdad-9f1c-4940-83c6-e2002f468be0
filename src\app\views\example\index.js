// // 不推荐注掉的这种写法
// export { default as Menu1 } from './menu1/menu1.vue';
// export { default as Menu2 } from './menu2/menu2.vue';
// export { default as Menu3 } from './menu3/menu3.vue';

// // 请使用异步 ()=>import(xxxxx) 这种引入方式，以实现懒加载！！！
export const Menu1 = () => import('./menu1/menu1.vue');
export const Menu2 = () => import('./menu2/menu2.vue');
export const Menu3 = () => import('./menu3/menu3.vue');
export const Menu4 = () => import('./menu4/menu4.vue');
