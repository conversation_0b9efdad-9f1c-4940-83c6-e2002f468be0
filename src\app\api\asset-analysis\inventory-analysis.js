import request from '@api/request';
import {AssetsAnalysisService} from '@api/prefix.config';

const prefix = `${AssetsAnalysisService}/check`;

export const InventoryAnalysis = {
    /**
     * 资产分析-盘点分析
     * @method getCheckTrendData 盘点分析-盘盈盘亏趋势
     */
    getCheckTrendData () {
        return request.post(`${prefix}/getCheckTrendData`).then(({ data }) => data);
    },
    /**
     * 资产分析-盘点分析
     * @method getCheckDataType 盘点分析-盘盈资产分布下拉列表
     */
    getCheckDataType (params) {
        return request.post(`${prefix}/getCheckDataType`, null, { params }).then(({ data }) => data);
    },
    /**
     * 资产分析-盘点分析
     * @method getCheckSurplusData 盘点分析-盘盈资产分布
     */
    getCheckSurplusData (params) {
        return request.post(`${prefix}/getCheckSurplusData`, null, { params }).then(({ data }) => data);
    },
    /**
     * 资产分析-盘点分析
     * @method getCheckLossData 盘点分析-盘亏资产分布
     */
    getCheckLossData (params) {
        return request.post(`${prefix}/getCheckLossData`, null, { params }).then(({ data }) => data);
    },
    /**
     * 资产分析-盘点分析
     * @method getCheckDetailData 盘点分析-资产盘点明细
     */
    getCheckDetailData (data) {
        return request.post(`${prefix}/getCheckDetailData`, data).then(({ data }) => data);
    },
    /**
     * 资产分析-盘点分析
     * @method getCheckDetailType 盘点分析-资产盘点明细下拉列表
     * 1-资产大类
     * 2-资产中类
     * 3-资产小类
     * 4-所属线路
     * 5-资产位置
     * 6-使用部门
     * 7-盘点结果
     */
    getCheckDetailType (params) {
        return request.post(`${prefix}/getCheckDetailType`, null, { params }).then(({ data }) => data);
    },
    /**
     * 资产分析-盘点分析
     * @method exportCheckDetailData 盘点分析-资产盘点明细导出
     */
    exportCheckDetailData (data) {
        return request.post(`${prefix}/exportCheckDetailData`, data, { responseType: 'blob' });
    }
};
