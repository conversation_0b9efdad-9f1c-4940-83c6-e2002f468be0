import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

const prefix = `${AssetsAnalysisService}/assetDetails`;
export const OtherAsset = {
    /**
     * @method getLineDesc 所属线路
     * */
    getLineDesc () {
        return request.get(`${prefix}/getLineDesc`).then(
            ({ data }) => data
        );
    },
    /**
     * @method getParkingLotTypeList 获取停车场类型
     * */
    getParkingLotTypeList () {
        return request.get(`${prefix}/getResourceType`).then(({ data }) => data);
    },
    /**
     * @method getParkingLotDetailsList 停车场明细
     * */
    getParkingLotDetailsList (params) {
        return request.post(`${prefix}/getParkingDetailTable`, params).then(
            ({ data }) => data
        );
    },
    /**
     * @method exportParkingLotDetailsList 导出停车场明细
     * */
    exportParkingLotDetailsList (params) {
        return request.post(`${prefix}/exportParkingDetailTable`, params, { responseType: 'blob' });
    },
    /**
     * @method getStoreDetailsList 商铺明细
     * */
    getStoreDetailsList (params) {
        return request.post(`${prefix}/getShopsDetailsPage`, params).then(
            ({ data }) => data
        );
    },
    /**
     * @method getStoreObjectType 商铺项目类型
     * */
    getStoreObjectType () {
        return request.get(`${prefix}/getShopsObjectType`).then(
            ({ data }) => data
        );
    },
    /**
     * @method exportStoreDetailList 导出商铺明细
     * */
    exportStoreDetailList (params) {
        return request.post(`${prefix}/exportShopsDetail`, params, { responseType: 'blob' });
    },
    /**
     * @method getAdvertisingDetailList 广告明细
     * */
    getAdvertisingDetailList (params) {
        return request.post(`${prefix}/getAdvertisingDetails`, params).then(
            ({ data }) => data
        );
    },
    /**
     * @method getAdvertisingType 广告类型
     * */
    getAdvertisingType () {
        return request.get(`${prefix}/getAdvertisingDetailsTypeList`).then(
            ({ data }) => data
        );
    },
    /**
     * @method exportAdvertisingDetailList 导出广告资源明细
     * */
    exportAdvertisingDetailList (params) {
        return request.post(`${prefix}/exportAdvertisingDetails`, params, { responseType: 'blob' });
    },
    /**
     * @method getCorrespondenceDetailList 通信资源明细
     * */
    getCorrespondenceDetailsLit (params) {
        return request.post(`${prefix}/getCorrespondenceDetails`, params).then(
            ({ data }) => data
        );
    },
    /**
     * @method exportCorrespondenceDetailList 导出通信资源明细
     * */
    exportCorrespondenceDetailList (params) {
        return request.post(`${prefix}/exportCorrespondenceDetails`, params, { responseType: 'blob' });
    }

};
