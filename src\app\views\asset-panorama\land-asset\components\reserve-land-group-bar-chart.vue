<template>
    <app-business-panel class="reserve-land-group-bar-chart" title="储备土地资源面积" mode="card" v-loading="loading">
        <template #default>
            <div class="reserve-land-group-bar-chart__content">
                <template v-if="graphData?.source?.length">
                    <ecp-chart-group-bar v-bind="chartProps" :key="`${graphTimestamp}_bar`" />
                </template>
                <ecp-empty v-else />
            </div>
        </template>
    </app-business-panel>
</template>

<script setup>
import { LandAssetApi } from '@api/asset-panorama/land-asset';
import { CHART_COLOR_LIST } from '@constants/enum-config';
import { formatXAxis } from '@utils/format';

const loading = ref(false);

const legend = ref({
    icon: 'circle',
    right: 0
});

const graphData = ref({
    dimensions: [],
    source: []
});

const graphTimestamp = ref(Date.now());

const defaultOptions = {
    color: CHART_COLOR_LIST,
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    grid: {
        top: 40,
        right: 0,
        bottom: 0
    },
    xAxis: {
        axisLabel: {
            rotate: 60,
            color: 'rgba(0, 0, 0, 0.45)', // 设置标签颜色
            formatter: (value) => formatXAxis(value,6),
        }
    },
    yAxis: {
        nameTextStyle: {
            padding: [0, 0, 0, -20],
            align: 'left'
        }
    },
    series: [
        {
            type: 'bar',
            itemStyle: {
                borderRadius: 10
            }
        },
        {
            type: 'bar',
            itemStyle: {
                borderRadius: 10
            }
        }
    ]
};

const chartProps = computed(() => {
    const defaultProps = {
        theme: 'whiteTheme',
        data: graphData.value,
        legend: legend.value,
        yName: '单位：平方米',
        unit: '平方米'
    };
    return {
        ...defaultProps,
        barWidth: 10,
        option: {
            ...defaultOptions
        }
    };
});

const getChartData = async () => {
    console.log('%c getChartData', 'font-size:18px;color:gold;font-weight:700;');

    loading.value = true;
    graphData.value.source = [];

    try {
        const [toBeSoldData, transferredData] = await Promise.all([
            LandAssetApi.getReserveAreaToBeSold(),
            LandAssetApi.getReserveAreaTransferred()
        ]);

        const dimensions = ['类目名称', '待出让面积', '已出让面积'];
        const source = (toBeSoldData || []).map((item, index) => {
            return {
                类目名称: item.DataName,
                待出让面积: item.Percentage,
                已出让面积: (transferredData || []).find(e => e.DataName === item.DataName)?.Percentage || 0
            };
        });

        graphData.value = {
            dimensions,
            source
        };
    } catch (error) {
        console.log('%c getTransferStatDataTrend Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    }

    loading.value = false;
};

onActivated(() => {
    getChartData();
});

</script>

<style scoped lang="scss">
.reserve-land-group-bar-chart {
    overflow: hidden;

    &__content {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
}
</style>
