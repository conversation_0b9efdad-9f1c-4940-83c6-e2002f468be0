<template>
    <component :is="direction === 'horizontal' ? 'div' : 'el-scrollbar'" class="layout-menu" :class="[direction]"
        ref="LayoutMenuRef">
        <el-menu class="layout-menu-content" :popper-class="joinStr(['layout-menu-popper'], ' ')" :mode="direction"
            :collapse="collapse" :collapse-transition="false" menu-trigger="hover" :default-active="activeIndex"
            ellipsis ref="menuRef" unique-opened>
            <template v-for="child in menu" :key="getUniqKey(menuProps, child)">
                <LayoutMenuItem v-bind="{ menuItem: child, menuProps, direction }" @select="onSelect" />
            </template>
        </el-menu>
    </component>
</template>

<script setup>
import { useRoute } from 'vue-router';

import { MENU_PROPS } from '@constants/menu-config';

import LayoutMenuItem from './layout-menu-item.vue';

import { useMenu } from './use-menu';

import { joinStr } from '@utils/format';

defineComponent({
    name: 'layout-menu'
});

const props = defineProps({
    direction: {
        type: String,
        default: 'vertical'
    },
    menu: {
        type: Array,
        default: () => ([])
    },
    menuProps: {
        type: Object,
        default: () => MENU_PROPS
    }
});

const route = useRoute();

const emits = defineEmits(['select']);

const LayoutMenuRef = ref();

const activeIndex = ref(null);

const { getMenuValueByKey, getUniqKey, activeMenu, collapse } = useMenu();

const onSelect = (options) => {
    const { menuItem: targetMenuItem } = options || {};
    activeIndex.value = getUniqKey(props.menuProps, targetMenuItem);
    activeMenu.value = targetMenuItem;
    emits('select', options);
};

const findMatchedMenuItem = (menuList, matcher) => {
    if (!menuList?.length || typeof matcher !== 'function') {
        return null;
    }
    let findResult = null;
    for (const currMenuItem of menuList) {
        const children = getMenuValueByKey(props.menuProps.children, currMenuItem);
        if (children?.length) {
            findResult = findMatchedMenuItem(children, matcher);
        } else {
            if (matcher(currMenuItem)) {
                findResult = currMenuItem;
            };
        }

        if (findResult) {
            break;
        }
    }
    return findResult;
};

const setActiveIndex = () => {
    const href = window.location.href;
    const contextPath = `${href}`.replace(window.location.origin, '');
    const matchedItem = findMatchedMenuItem(props.menu, (currMenuItem) => {
        const targetRoute = getMenuValueByKey(props.menuProps.route, currMenuItem);
        const targetUrl = getMenuValueByKey(props.menuProps.url, currMenuItem);

        return contextPath.match(new RegExp(`^${targetRoute}`)) || targetUrl === href;
    });
    if (matchedItem) {
        activeIndex.value = getUniqKey(props.menuProps, matchedItem);
        activeMenu.value = matchedItem;
    } else {
        activeIndex.value = null;
        activeMenu.value = null;
    }
};

watch(() => route?.fullPath,
    (newVal) => {
        props.menu?.length && setActiveIndex();
    }
);

watch(() => props.menu,
    () => {
        setActiveIndex();
    },
    {
        immediate: true,
        deep: true
    }
);

</script>

<style lang="scss" scoped>
.layout-menu {
    --elp-menu-bg-color: transparent;
    --elp-menu-hover-bg-color: transparent;
    --elp-menu-border-color: transparent;
    --elp-menu-text-color: var(--color-white-opacity-6-5);
    --elp-menu-hover-text-color: var(--color-white-opacity-6-5);
    --elp-menu-active-color: var(--color-white);

    &.vertical {
        --elp-menu-item-height: 48px;
        --elp-menu-sub-item-height: 40px;
        --elp-menu-base-level-padding: var(--spacer-large-3);
        --elp-menu-level-padding: 28px;
        --elp-menu-text-color: var(--color-white-opacity-6-5);
    }
}

.layout-menu {

    &.horizontal {
        box-sizing: border-box;
        width: auto;
        height: 100%;
        flex: 1 1 auto;
        display: flex;
        overflow: hidden;

        .layout-menu-content {
            box-sizing: border-box;
            width: auto;
            height: 100%;
            flex: 1 1 auto;
            overflow: hidden;
        }

        :deep(.layout-menu-content) {

            .elp-menu-item,
            .elp-sub-menu .elp-sub-menu__title {
                user-select: none;

                &:not(.is-active):hover {
                    border-bottom-color: var(--elp-menu-hover-text-color);
                }
            }
        }
    }

    &.vertical {
        box-sizing: border-box;
        width: 100%;
        height: auto;
        flex: 1 1 auto;

        .layout-menu-content {
            box-sizing: border-box;
            width: 100%;
            height: auto;
            flex: 0 1 auto;
            backdrop-filter: blur(2px);
        }

        :deep(.layout-menu-content) {
            > .elp-scrollbar__bar {
                display: none !important;
            }

            .elp-sub-menu {
                &.is-opened {
                    .elp-sub-menu__title {

                        .layout-menu-item-icon,
                        .layout-menu-item-text,
                        .elp-sub-menu__icon-arrow {
                            color: var(--elp-menu-active-color);
                        }
                    }
                }

                &.is-active {
                    .elp-sub-menu__title {

                        .layout-menu-item-icon,
                        .layout-menu-item-text {
                            color: var(--elp-menu-active-color);
                        }
                    }
                }

                &.collapse {

                    .elp-sub-menu__title {
                        justify-content: center;

                        .layout-menu-item-icon {
                            margin-right: 0;
                        }
                    }
                }
            }

            .elp-menu-item {
                height: 48px;
                user-select: none;
                justify-content: center;

                .layout-menu-item-icon {
                    margin-right: 0;
                }

                &.is-active {
                    color: var(--elp-menu-active-color);

                    .layout-menu-item-icon {
                        z-index: 1;
                        color: var(--elp-menu-active-color);
                    }

                    &::before {
                        content: '';
                        position: absolute;
                        top: var(--spacer-small);
                        left: 0;
                        bottom: var(--spacer-small);
                        right: 0;
                        background-color: #48ACD2;
                    }
                }

                &:not(.is-active):hover {
                    &::before {
                        content: '';
                        position: absolute;
                        top: var(--spacer-small);
                        left: 0;
                        bottom: var(--spacer-small);
                        right: 0;
                        background-color: var(--color-white-opacity-0-9);
                    }
                }
            }

            &:not(.elp-menu--collapse) {
                .elp-menu-item {
                    justify-content: unset;

                    .layout-menu-item-icon {
                        margin-right: var(--spacer);
                    }
                }

                &:not(.elp-menu--popup-container) {

                    .elp-menu-item,
                    .elp-sub-menu__title,
                    .elp-menu-item-group__title {
                        padding-left: calc(var(--elp-menu-base-level-padding) + var(--elp-menu-level)* var(--elp-menu-level-padding));
                    }
                }

                .elp-sub-menu__title {
                    padding-right: calc(var(--spacer-large) + var(--elp-menu-icon-width));
                }
            }

            .elp-menu--inline {
                padding: var(--spacer-small) 0;
                background-color: rgba(#000000, 0.2);
            }
        }
    }
}
</style>

<style lang="scss">
.layout-menu-popper {
    --elp-menu-hover-bg-color: var(--color-primary-assist-1);

    .elp-menu-item {
        height: 40px;

        &.is-disabled {
            opacity: 0.5;
        }
    }
}
</style>
