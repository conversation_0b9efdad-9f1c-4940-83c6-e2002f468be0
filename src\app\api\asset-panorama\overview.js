import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';
import qs from 'qs';

export const Overview = {
    /**
     * 概览
     * @method getBaseDetail 基础详情
     */
    getBaseDetail () {
        return request.get(`${AssetsAnalysisService}/getBaseDetail`).then(
            ({ data }) => data
        );
    },
    /**
     * 概览
     * @method getAreaDetail 地图数据
     */
    getAreaDetail (params, cancelToken) {
        return request.post(`${AssetsAnalysisService}/getAreaDetail`, qs.stringify(params), {
            headers: {
                'content-type': 'application/x-www-form-urlencoded'
            },
            cancelToken
        });
    },
    /**
     * 概览
     * @method getHouseAreaData 房产数据
     */
    getHouseAreaData () {
        return request.post(`${AssetsAnalysisService}/getHouseAreaData`).then(({ data }) => data);
    },
    /**
     * 概览
     * @method getAdvertData 文体广告数据
     */
    getAdvertData () {
        return request.post(`${AssetsAnalysisService}/getAdvertData`).then(({ data }) => data);
    },
    /**
     * 概览
     * @method getLandArea 土地数据
     */
    getLandArea () {
        return request.post(`${AssetsAnalysisService}/getLandArea`).then(({ data }) => data);
    },
    /**
     * 概览
     * @method getPropertyArea 物业数据
     */
    getPropertyArea () {
        return request.post(`${AssetsAnalysisService}/getPropertyArea`).then(({ data }) => data);
    },
    /**
     * 概览
     * @method getWorkAsset 办公资产数据
     */
    getWorkAsset () {
        return request.post(`${AssetsAnalysisService}/getWorkAsset`).then(({ data }) => data);
    },
    /**
     * 概览
     * @method getOperationAsset 运营资产数据
     */
    getOperationAsset (params) {
        return request.post(`${AssetsAnalysisService}/getOperationAsset`, qs.stringify(params), {
            headers: {
                'content-type': 'application/x-www-form-urlencoded'
            }
        }).then(
            ({ data }) => data
        );
    },
    /**
     * 概览
     * @method getOperationAssetList 运营资产列表
     */
    getOperationAssetList () {
        return request.post(`${AssetsAnalysisService}/getOperationAssetList`).then(({ data }) => data);
    },
    /**
     * 概览
     * @method getLineList 线路列表
     */
    getLineList () {
        return request.post(`${AssetsAnalysisService}/getLineList`).then(({ data }) => data);
    }
};
