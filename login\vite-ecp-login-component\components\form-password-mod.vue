<!-- <docs>
# 修改密码表单
- 提供两种类型的修改密码功能：首次登录的重置密码 & 账户密码已过有效期的修改密码

## props传参
- visible[Boolean]：弹窗显隐
- pwModType[Number]：修改密码类型 - 1001为首次登录需重置密码； 1002为账户密码已过有效期需修改密码
- userCode[String]：用户账号
- RegExpPasswordForMod[String]: 修改密码时密码的校验正则表达式，默认为 /^((?=.*[A-Za-z])(?=.*\d)|(?=[A-Za-z])(?=.*[#@!~%^&*])|(?=.*\d)(?=.*[#@!~%^&*]))[A-Za-z\d#@!~%^&*]{8,16}$/
- ErrorNotePasswordForMod[String]: 修改密码时密码的校验错误提示，默认为 '密码必须包含字母、数字、符号中至少2种，且长度为8-16个，不包括空格'
- passwordVerify[Array]：修改密码时的校验正则表达式列表，优先级比RegExpPasswordForMod高，只有当该项的值为空（空数组）时才使用RegExpPasswordForMod
- pwdMinLength[Number]: 密码最小长度
</docs> -->

<template>
    <el-dialog :title="dialogTitle" class="ecpp-login-component dialog-password-mod"
        custom-class="ecpp-login-component dialog-password-mod" width="450px" top="25vh" :close-on-click-modal="false"
        :close-on-press-escape="false" :show-close="false" v-model="dialogVisible" append-to-body @opened="init"
        @closed="init">
        <!--修改密码表单组件-->
        <div class="ecpp-password-mod">
            <el-form class="ecpp-password-mod-form app-form" :model="pwForm" :rules="pwRules" ref="pwFormRef">
                <el-form-item label="原密码：" prop="OldPassword">
                    <el-input placeholder="请输入原密码" show-password autocomplete="new-password"
                        v-model="pwForm.OldPassword" @blur="$refs.pwFormRef.validateField('Password')" />
                </el-form-item>
                <el-form-item label="新密码：" prop="Password">
                    <el-input ref="newPassword" placeholder="请输入新密码" show-password autocomplete="new-password"
                        v-model="pwForm.Password" />
                </el-form-item>
                <el-form-item label="确认密码：" prop="ConfirmPassword">
                    <el-input placeholder="请重新输入新密码" show-password autocomplete="new-password"
                        v-model="pwForm.ConfirmPassword" />
                </el-form-item>
            </el-form>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="onClose">取 消</el-button>
                <el-button type="primary" @click="passwordMod" :loading="loading">确 定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, computed, defineComponent, defineProps, provide, onMounted, getCurrentInstance, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

import { LoginApi } from '../api';
import * as Utils from '../utils';

defineComponent({
    name: 'ecp-password-mod'
});

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: String,
    pwModType: {
        type: Number,
        default: null
    },
    userCode: {
        type: String,
        default: ''
    },
    RegExpPasswordForMod: {
        type: String,
        default: undefined
    },
    ErrorNotePasswordForMod: {
        type: String,
        default: undefined
    },
    encryptionType: {
        type: String,
        default: 'AES'
    },
    passwordVerify: {
        type: Array
    },
    pwdMinLength: Number,
    safeRequest: Boolean
});

const emits = defineEmits([
    'close'
]);

const onClose = () => {
    emits('close');
};

// 默认校验规则
const validatePatterns = {
    password: [
        /^((?=.*[A-Za-z])(?=.*\d)|(?=[A-Za-z])(?=.*[#@!~%^&*])|(?=.*\d)(?=.*[#@!~%^&*]))[A-Za-z\d#@!~%^&*]{8,16}$/,
        '密码必须包含字母、数字、符号中至少2种，且长度为8-16个，不包括空格'
    ]
};
// 修改密码表单默认字段值
const pwFormDef = reactive({
    OldPassword: '',
    Password: '',
    ConfirmPassword: ''
});
const pwForm = reactive({});
const pwFormRef = ref();

const loading = ref(false);

const dialogVisible = computed({
    get () {
        return props.visible;
    },
    set () {
        onClose();
    }
});
const dialogTitle = computed(() => {
    if (props.title) {
        return props.title;
    }
    let rs = '修改密码';
    props.pwModType === 1001 && (rs = '首次登录请重置密码');
    props.pwModType === 1002 && (rs = '账户密码已过有效期请修改密码');
    return rs;
});
// 获取密码校验规则
const validatePassword = computed(() => {
    const reg =
        props.RegExpPasswordForMod || validatePatterns.password[0];
    const note =
        props.ErrorNotePasswordForMod ||
        validatePatterns.password[1];
    return [reg, note];
});
// 密码校验规则
const passwordValidator = (rule, value, callback) => {
    if (value === '' || value === undefined || value === null) {
        if (rule.required) {
            callback(new Error('密码不能为空'));
        } else {
            callback();
        }
    } else if (value === pwForm.OldPassword) {
        callback(new Error('新密码不能跟原密码相同'));
    } else if (value.length < props.pwdMinLength || value.length > 20) {
        callback(
            new Error(
                '密码长度不能小于' +
                props.pwdMinLength +
                '个字符，且不能超过20个字符'
            )
        );
    } else if (props.passwordVerify && props.passwordVerify.length) {
        if (
            !props.passwordVerify.reduce(
                (p, c) => {
                    let result = true;
                    if (c.pattern instanceof RegExp) {
                        result = !!c.pattern.test(value);
                    } else if (typeof c.validator === 'function') {
                        result = !!c.validator(rule, value);
                    }
                    return p && result;
                },
                true
            )
        ) {
            callback(
                new Error(
                    '密码需包含' +
                    props.passwordVerify
                        .map(item => item.desc)
                        .join('、')
                )
            );
        } else {
            callback();
        }
    } else {
        callback();
    }
};
// 确认密码校验
const confirmPasswordValidator = (rule, value, callback) => {
    if (value === '' || value === undefined || value === null) {
        if (rule.required) {
            callback(new Error('确认密码不能为空'));
        } else {
            callback();
        }
    } else if (value !== pwForm.Password) {
        callback(new Error('新密码和确认密码必须相同'));
    } else {
        callback();
    }
};
const pwRules = {
    OldPassword: [{ required: true, message: '原密码不能为空' }],
    Password: [
        {
            required: true,
            trigger: 'blur',
            validator: passwordValidator
        }
    ],
    ConfirmPassword: [
        {
            required: true,
            trigger: 'blur',
            validator: confirmPasswordValidator
        }
    ]
};

const init = () => {
    Object.entries(JSON.parse(JSON.stringify(pwFormDef))).forEach(([key, value]) => {
        pwForm[key] = value;
    });
    pwFormRef.value.resetFields();
};
// 确认提交
const passwordMod = async () => {
    try {
        const valid = await pwFormRef.value.validate();
        if (!valid) {
            throw new Error(valid);
        }
        loading.value = true;
        const time = Date.parse(new Date());
        const oldPassword = Utils.Encrypt(
            pwForm.OldPassword,
            `PCI${time}`
        );
        const password = Utils.Encrypt(
            pwForm.Password,
            `PCI${time}`
        );
        const userCode = Utils.Encrypt(props.userCode, `PCI${time}`);
        const params = {
            UserCode: userCode,
            OldPassword: oldPassword,
            Password: password,
            Time: time
        };

        const data = await LoginApi.modifyPasswordPost(params);
        if (data.OpCode !== 0) {
            ElMessage.error(data.OpDesc);
            throw new Error(data);
        }
        onClose();
        const note =
            props.pwModType === 1001 ? '重置密码成功!' : '修改密码成功!';
        ElMessageBox.alert(note, {
            showClose: false,
            confirmButtonText: '知道了，关闭重新登录'
        });
    } catch (error) {
        console.log(
            '%c [ECP-LOGIN] passwordMod Caught error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    } finally {
        loading.value = false;
    }
};
</script>
