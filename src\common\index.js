import Utils from './utils';
import Components from './components';
import Directives from './directives';
export default {
    install: (app) => {
        app.config.globalProperties.$utils = Utils;
        // Components.forEach(component => {
        //   app.Components
        // })
        Reflect.ownKeys(Components).forEach(key => {
            app.component(key, Components[key]);
        });
        Reflect.ownKeys(Directives).forEach(key => {
            app.directive(key, Directives[key]);
        });
        // Components.forEach(component => {
        //   console.log('%c [ component ]-7', 'font-size:13px; background:#bc3371; color:#ff77b5;', component)
        // });
    }
};
