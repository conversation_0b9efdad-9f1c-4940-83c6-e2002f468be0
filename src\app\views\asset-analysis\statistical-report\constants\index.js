const _formatter = (row, type) => row[type] || 0;

export const CHANGES_IN_ASSETS_TABLE_CONFIG = {
    props: {},
    cols: [
        {
            label: '来源',
            prop: 'Source'
        },
        {
            label: '类别',
            prop: 'AssetType'
        },
        {
            label: '上月',
            align: 'center',
            cols: [
                {
                    label: '数量',
                    align: 'right',
                    formatter: (row) => _formatter(row, 'lastMonth_count')
                },
                {
                    label: '价值(万元)',
                    align: 'right',
                    formatter: (row) => _formatter(row, 'lastMonth_value')
                }
            ]
        },
        {
            label: '本月',
            align: 'center',
            cols: [
                {
                    label: '数量',
                    align: 'right',
                    formatter: (row) => _formatter(row, 'thisMonth_count')
                },
                {
                    label: '价值(万元)',
                    align: 'right',
                    formatter: (row) => _formatter(row, 'thisMonth_value')
                }
            ]
        }
    ]
};

export const ASSET_USAGE_TABLE_CONFIG = {
    props: {},
    cols: [
        {
            label: '来源',
            prop: 'Source'
        },
        {
            label: '类别',
            prop: 'AssetType'
        },
        {
            label: '使用(含在库)',
            prop: 'UseCount',
            align: 'right'
        },
        {
            label: '停用',
            prop: 'DeactivatedCount',
            align: 'right'
        },
        {
            label: '闲置(含待报废)',
            prop: 'IdleCount',
            align: 'right'
        },
        {
            label: '报废',
            prop: 'ScrapCount',
            align: 'right'
        },
        {
            label: '合计',
            prop: 'Total',
            align: 'right'
        }
    ]
};

export const REAL_ESTATE_RESOURCES_TABLE_CONFIG = {
    props: {},
    cols: [
        {
            label: '类型',
            prop: 'TypeName'
        },
        {
            label: '可租赁面积(m²)',
            prop: 'RentableArea',
            align: 'right'
        },
        {
            label: '已租赁面积(m²)',
            prop: 'LeasedArea',
            align: 'right'
        },
        {
            label: '可租赁数量',
            prop: 'RentableAmount',
            align: 'right'
        },
        {
            label: '已租赁数量',
            prop: 'LeasedAmount',
            align: 'right'
        },
        {
            label: '出租率',
            prop: 'RentalRate',
            align: 'right'
            // formatter: (row) => `${row.RentalRate * 100}%`
        }
    ]
};

export const WARRANT_PROCESSING_TABLE_CONFIG = {
    props: {},
    cols: [
        {
            label: '权证类型',
            prop: 'CertificateType'
        },
        {
            label: '序号',
            prop: 'Order',
            align: 'right'
        },
        {
            label: '土地/项目类别',
            prop: 'LandType'
        },
        {
            label: '所属线路',
            prop: 'LineDesc',
            align: 'right'
        },
        {
            label: '已办理数量（项）',
            prop: 'AlreadyHandleAmount',
            align: 'right'
        },
        {
            label: '已办理面积（万m²）',
            prop: 'AlreadyHandleArea',
            align: 'right'
        },
        {
            label: '注销核减数量（项）',
            prop: 'CanceledAndReduceAmount',
            align: 'right'
        },
        {
            label: '注销核减面积（万m²）',
            prop: 'CanceledAndReduceArea',
            align: 'right'
        }
    ]
};

export const STYLISTIC_ADVERTISING_TABLE_CONFIG = {
    props: {},
    cols: [
        {
            label: '轨道交通线路',
            prop: 'Source'
        },
        {
            label: '总广告资源',
            align: 'center',
            cols: [
                {
                    label: '数量',
                    align: 'right',
                    formatter: (row) => _formatter(row, 'totalAdvertisingResources_count')
                },
                {
                    label: '面积m²',
                    align: 'right',
                    formatter: (row) => _formatter(row, 'totalAdvertisingResources_area')
                }
            ]
        },
        {
            label: '公益广告',
            align: 'center',
            cols: [
                {
                    label: '数量',
                    align: 'right',
                    formatter: (row) => _formatter(row, 'publicServiceAdvertisement_count')
                },
                {
                    label: '面积m²',
                    align: 'right',
                    formatter: (row) => _formatter(row, 'publicServiceAdvertisement_area')
                }
            ]
        },
        {
            label: '已招租商业广告',
            align: 'center',
            cols: [
                {
                    label: '数量',
                    align: 'right',
                    formatter: (row) => _formatter(row, 'commercialAdvertisementsAreRented_count')
                },
                {
                    label: '面积m²',
                    align: 'right',
                    formatter: (row) => _formatter(row, 'commercialAdvertisementsAreRented_value')
                }
            ]
        }, {
            label: '空置商业广告',
            align: 'center',
            cols: [
                {
                    label: '数量',
                    align: 'right',
                    formatter: (row) => _formatter(row, 'vacantCommercialAdvertisement_count')
                },
                {
                    label: '面积m²',
                    align: 'right',
                    formatter: (row) => _formatter(row, 'vacantCommercialAdvertisement_area')
                }
            ]
        },
        {
            label: '出租率（包含公益广告实际出租率）',
            prop: 'LettingRate',
            align: 'right'
        }
    ]
};
