<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <template #head-right>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template>
        <template #content>
            <div :class="[`${name}__wrapper`]">
                <div :class="[`${name}__wrapper-left`]">
                    <!-- 土地面积趋势 -->
                    <LandAreaTrendLineChart />
                    <!-- 储备土地资源面积 -->
                    <ReserveLandGroupBarChart />
                    <!-- 土地大类比例 -->
                    <LandCategoriesPieChart />
                    <!-- 零星用地资源面积 -->
                    <SporadicLandBarChart />
                </div>
                <app-business-panel mode="card" :class="[`${name}__wrapper-right`]" title="存量土地分析">
                    <div class="stock-land-wrapper">
                        <!-- 土地面积趋势 -->
                        <StockLandLineChart />
                        <!-- 按性质占比分析 -->
                        <StockLandPieChart />
                    </div>
                </app-business-panel>
            </div>
        </template>
    </app-form-page>
</template>

<script setup>
import dayjs from 'dayjs';

import SporadicLandBarChart from './components/sporadic-land-bar-chart.vue';
import LandAreaTrendLineChart from './components/land-area-trend-line-chart.vue';
import ReserveLandGroupBarChart from './components/reserve-land-group-bar-chart.vue';
import LandCategoriesPieChart from './components/land-categories-pie-chart.vue';
import StockLandLineChart from './components/stock-land-line-chart.vue';
import StockLandPieChart from './components/stock-land-pie-chart.vue';

const name = 'asset-panorama-land-asset';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '土地'
        }
    ];
});

onMounted(() => {

});

</script>

<style lang="scss" scoped>
$page-name: asset-panorama-land-asset;

.#{$page-name} {

    &__wrapper {
        flex: 1;
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: var(--spacer-large);

        &-left {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: var(--spacer-large);

            :deep(.app-business-panel.card-panel) {
                height: calc(50vh - 48px);
                min-height: 270px;
            }
        }

        &-right {
            .stock-land-wrapper {
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                gap: var(--spacer-large);
            }

            :deep(.app-business-panel.normal-panel) {
                height: calc(50vh - 80px);
                min-height: 235px;
            }

            :deep(.app-business-panel.normal-panel:not(.nested-panel):nth-last-child(1)) {
                padding-bottom: 0;
            }

            :deep(.app-business-panel .app-business-panel-header) {
                padding: var(--spacer-large) 0;

                .app-business-panel-title {
                    padding-left: var(--spacer-small);
                }
            }
        }
    }

    :deep(.app-form-page-content) {
        min-width: 1100px;
    }
}
</style>
