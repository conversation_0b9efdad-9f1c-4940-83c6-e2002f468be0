<template>
    <app-business-panel class="statistic-map" mode="card" :showHeader="false">
        <div class="statistic-map__wrapper" ref="mapRef" v-resize="onResize"></div>
        <div class="statistic-map__actions at-bottom at-right">
            <div class="statistic-map__actions-item" @click="toggleMaximunModule">
                <ecp-icon class="statistic-map__actions-item__icon clickable"
                    :icon="maximumModule !== moduleName ? 'ecp-icon-full' : 'ecp-icon-exit-full'"></ecp-icon>
            </div>
            <div class="statistic-map__actions-item">
                <ecp-icon class="statistic-map__actions-item__icon clickable" icon="ecp-icon-plus"
                    @click="onZoom('zoomIn')"></ecp-icon>
                <el-divider class="statistic-map__actions-item__divider" />
                <ecp-icon class="statistic-map__actions-item__icon clickable" icon="ecp-icon-minus"
                    @click="onZoom('zoomOut')"></ecp-icon>
            </div>
        </div>
    </app-business-panel>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();
import _ from 'lodash-es';

import L from 'suntekmap';

import * as turf from '@turf/turf';

import useGlobalStore from '@store/global-config';

import { DEFAULT_PATTERN, validLineString, useMetro } from '@composables/use-metro';

import { joinStr } from '@utils/format';
import * as Api from '@api/index';
import { createVNode, render } from 'vue';
import MapPop from '@views/asset-panorama/overview/_components/map-pop.vue';

const props = defineProps({
    maximumModule: String
});

const emits = defineEmits(['toggleMaximumModule']);
const moduleName = 'StatisticMap';
// 地图详细数据请求
let response = null;
let timer = null;
const source = [];
const CancelToken = axios.CancelToken;
let lineMap = {};

const toggleMaximunModule = () => {
    emits('toggleMaximumModule', moduleName);
};

const simple = ref(true);

const globalStore = useGlobalStore();

const onZoom = (type) => {
    mapInstance?.[type]?.();
};

const {
    lineGeoJSON,
    lineSimpleGeoJSON,
    stationGeoJSON,
    stationSimpleGeoJSON,
    drawMetroLine,
    drawMetroStation,
    isSameStation,
    drawMetroLineMark,
    drawMetroTextMark
} = useMetro();

const mapRef = ref();
let mapInstance = null;

let layerMetroLine = null;
let layerMetroStation = null;

const activeLineGeoJSON = computed(() => {
    return simple.value ? lineSimpleGeoJSON.value : lineGeoJSON.value;
});

const activeStationGeoJSON = computed(() => {
    return simple.value ? stationSimpleGeoJSON.value : stationGeoJSON.value;
});

const onResize = () => {
    mapInstance?.invalidateSize?.();
    mapInstance && layerMetroStation && mapInstance?.fitBounds?.(layerMetroStation?.getBounds?.());
};

const loadMap = async () => {
    return new Promise((resolve, reject) => {
        const IMPORT_CONFIGS = globalStore?.globalConfigs?.IMPORT_CONFIGS;

        const tiledLayerBase = !simple.value ? decodeURIComponent(IMPORT_CONFIGS?.tiledLayerBase || '') : '';

        const maxZoom = !simple.value && !Number.isNaN(+IMPORT_CONFIGS?.mapMaxZoom) ? IMPORT_CONFIGS?.mapMaxZoom : 2;
        const minZoom = !simple.value && !Number.isNaN(+IMPORT_CONFIGS?.mapMinZoom) ? IMPORT_CONFIGS?.mapMinZoom : -2;

        mapInstance = L.map(mapRef.value, {
            crs: tiledLayerBase ? L.CRS.EPSG4326 : L.CRS.Simple,
            zoomControl: false,
            maxZoom,
            minZoom,
            attributionControl: false
        });

        if (!tiledLayerBase) {
            !simple.value && console.log('%c tiledLayerBase Not Found', 'font-size:18px;color:gold;font-weight:700;');
            resolve();
        } else {
            L.tileLayer(tiledLayerBase, {
                tileSize: 256,
                zoomOffset: 1
            }).addTo(mapInstance);
            mapInstance.once('loadEnd', () => {
                resolve();
            });
        }
    });
};

const initMetroLine = async () => {
    try {
        layerMetroLine = await drawMetroLine({
            mapInstance,
            simple: simple.value,
            style: {
                opacity: 1
            }
        });
    } catch (error) {
        console.log('%c getLayerMetroLine Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    }
};

const initMetroStation = async () => {
    try {
        layerMetroStation = await drawMetroStation({
            mapInstance,
            simple: simple.value,
            customClass: (feature, options, markerType) => {
                return ['marker-center-color', markerType === 'transfer' ? 'rtbigdata-iconfont rtbigdata-icon-transfer-station' : `color_${feature.properties.lineNo}`].filter(Boolean).join(' ');
            }
        });
        layerMetroStation.on('mouseover', (e) => {
            timer && clearTimeout(timer); // 清除定时器
            timer = setTimeout(() => {
                const { lineNo, lineName } = e.layer.feature.properties;
                if (response) source.forEach(item => item.cancel());
                const lineObject = lineMap.find(item => {
                    const tempLineName = lineName.replace('轨道交通', '地铁');
                    return item.LineName.startsWith(tempLineName) || tempLineName.includes(item.LineName);
                });
                if (lineNo !== tempMapPopData.lineNo) {
                    const tempSource = CancelToken.source();
                    source.push(tempSource);
                    response = Api.AssetPanorama.getAreaDetail({ lineCode: lineObject.Code }, tempSource.token);
                    response.then(res => {
                        tempMapPopData.data = res.data.Data;
                        tempMapPopData.lineNo = lineNo;
                        addPop(e.latlng, tempMapPopData.data);
                    }).catch(() => {
                        tempMapPopData.lineNo = '';
                    }).finally(() => {
                        source.splice(source.findIndex(item => item.token === tempSource.token), 1);
                        response = null;
                    });
                    return;
                }
                addPop(e.latlng, tempMapPopData.data);
            }, 300);
        });
        layerMetroStation.on('mouseout', (e) => {
            timer && clearTimeout(timer);
        });

        const bounds = layerMetroStation.getBounds();

        mapInstance.fitBounds(bounds);
    } catch (error) {
        console.log('%c getLayerMetroLine Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    }
};

const initMetroLineMark = async () => {
    await drawMetroLineMark({ mapInstance, simple: simple.value });
    await drawMetroTextMark({ mapInstance, simple: simple.value });
};

const tempMapPopData = reactive({
    lineNo: '',
    data: {}
});

const addPop = (latlng, popData) => {
    // 创建并挂载多个 MapPop 组件实例
    const fragment = document.createDocumentFragment();

    // 将当前组件的上下文传递给 createVNode 第三个参数
    const vnode = createVNode(MapPop, 
        { 
            ...popData,
            router // 将 router 作为 prop 传入
        }, null, 0, null, getCurrentInstance()?.appContext);

    // 渲染虚拟节点到文档片段
    render(vnode, fragment);

    return L.popup({
        className: 'map-popper',
        offset: L.point(0, 0)
    }).setLatLng(latlng).setContent(fragment).openOn(mapInstance);
};

const getLineList = async () => {
    const response = await Api.AssetPanorama.getLineList();
    lineMap = response.Data;
};

const init = async () => {
    await loadMap();
    await Promise.allSettled([
        initMetroLine(),
        initMetroStation(),
        initMetroLineMark()
    ]);
    await getLineList();
};

onMounted(() => {
    const IMPORT_CONFIGS = globalStore?.globalConfigs?.IMPORT_CONFIGS;

    simple.value = Object.hasOwnProperty.call(IMPORT_CONFIGS, 'useGIS') ? !IMPORT_CONFIGS?.useGIS : true;
    init();
});

onUnmounted(() => {

});

</script>

<style lang="scss" scoped>
.statistic-map {
    padding: 0 !important;
    position: relative;

    &__wrapper {
        height: 100%;
        flex: 1 1 auto;
        position: relative;
        z-index: 1;
        overflow: hidden;

        :deep {
            .app-metro-map-station {
                &.marker-center-color {

                    .app-metro-map-station-icon {
                        border: 1px solid var(--color-text-light-darken);
                        background: none;
                        background-color: var(--station-color, var(--color-text-light-darken));
                        filter: drop-shadow(0 0 2px var(--box-shadow-color-base));
                    }

                    &:hover {
                        .app-metro-map-station-icon {
                            background-color: #2A61FF;
                            border: 1px solid var(--color-text-light-darken);
                        }

                        .app-metro-map-station-label {
                            color: #2A61FF;
                        }
                    }
                }

                &.color_01 {
                    .app-metro-map-station-icon {
                        border: 1px solid #3080B7;
                    }
                }

                &.color_02 {
                    .app-metro-map-station-icon {
                        border: 1px solid #EB81B9;
                    }
                }

                &.color_03 {
                    .app-metro-map-station-icon {
                        border: 1px solid #DAC17D;
                    }
                }

                &.color_04 {
                    .app-metro-map-station-icon {
                        border: 1px solid #86B81C;
                    }
                }

                &.color_05 {
                    .app-metro-map-station-icon {
                        border: 1px solid #B85A4E;
                    }
                }

                &.color_06 {
                    .app-metro-map-station-icon {
                        border: 1px solid #018237;
                    }
                }

                &.color_07 {
                    .app-metro-map-station-icon {
                        border: 1px solid #EE782E;
                    }
                }

                &.color_08 {
                    .app-metro-map-station-icon {
                        border: 1px solid #99ADAC;
                    }
                }

                &.color_09 {
                    .app-metro-map-station-icon {
                        border: 1px solid #FCD600;
                    }
                }

                &.color_10 {
                    .app-metro-map-station-icon {
                        border: 1px solid #D10195;
                    }
                }

                &.color_11 {
                    .app-metro-map-station-icon {
                        border: 1px solid #00AD8E;
                    }
                }

                &.color_12 {
                    .app-metro-map-station-icon {
                        border: 1px solid #D10195;
                    }
                }

                &.rtbigdata-icon-transfer-station {

                    &::before {
                        width: 20px;
                        font-size: 14px;
                        line-height: 20px;
                        // color: var(--color-white);
                        color: var(--station-color);
                        text-align: center;
                        position: absolute;
                        top: 0;
                        left: 0;
                        z-index: 2;
                        transform: scale(var(--station-marker-zoom, 1));
                    }

                    &.marker-center-color {

                        .app-metro-map-station-icon {
                            border: 2px solid var(--station-color);
                            background-image: url('@assets/map/transfer-station.png');
                            background-size: contain;
                            filter: drop-shadow(0 0 2px var(--box-shadow-color-base));
                        }
                    }
                }
            }

            .app-metro-map-tooltip {
                min-width: 150px;

                &__wrapper {
                    // gap: var(--spacer);
                    padding: 0 var(--spacer-small);
                }

                &--item {
                    gap: var(--spacer-large);
                    justify-content: space-between;
                    align-items: baseline;
                }

                &--text {
                    &.custom-color {
                        color: var(--custom-tag-color, var(--color-text-regular));
                    }

                    &.station-name {
                        color: #2A61FF;
                    }
                }
            }
        }
    }

    &__actions {
        display: flex;
        flex-direction: column;
        gap: var(--spacer-small);
        position: absolute;
        z-index: 2;

        &.at-bottom {
            bottom: var(--spacer-large);
        }

        &.at-left {
            left: var(--spacer-large);
        }

        &.at-right {
            right: var(--spacer-large);
        }

        .clickable {
            cursor: pointer;

            &:active {
                opacity: 0.5;
            }
        }

        &-item {
            width: 32px;
            min-height: 32px;
            padding: var(--spacer);
            border-radius: var(--border-radius-base);
            // background-color: #EFF1F6;
            background-color: var(--background-color-page);
            display: flex;
            flex-direction: column;

            &__divider {
                margin: var(--spacer) 0;
                border-top-color: var(--color-text-mark);
            }

            &__icon {
                flex: 1 0 auto;
                align-self: center;
                text-align: center;
                font-size: var(--font-size-medium);
                line-height: var(--font-line-height-extra-small);
                color: var(--color-text-regular);
            }

            &.legend-wrapper {
                width: auto;
                padding: var(--spacer-large);
                row-gap: var(--spacer-medium-2);

                .legend {
                    display: flex;

                    &-icon {
                        width: 16px;
                        height: 16px;
                        margin-right: var(--spacer-large);
                        flex: 0 0 auto;
                        border-radius: var(--border-radius-capsule);
                        background-color: var(--station-color);
                    }

                    &-text {
                        // color: var(--color-text-primary);
                        color: var(--color-text-regular);
                        font-size: var(--font-size-small);
                        line-height: var(--font-line-height-extra-small);
                    }
                }
            }
        }

    }
}
</style>

<style lang="scss">
.map-popper {

    .leaflet-popup-close-button,
    .leaflet-popup-tip-container {
        display: none;
    }

    .leaflet-popup-content-wrapper {
        background-color: transparent;
        box-shadow: none;

        .leaflet-popup-content {
            margin: 0;
            width: unset !important;
        }
    }
}
</style>
