<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <template #head-right>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template>
        <template #content>
            <div class="chart-container" v-resize="onResize">
                <div class="chart-left">
                    <app-business-panel mode="card" v-loading="loading.chart1">
                        <template #header>
                            <span class="app-business-panel-title">运营资产-按类型</span>
                        </template>
                        <template #header-append>
                            <div class="app-business-panel-tools">
                                <el-select v-model="operationalAssets.line">
                                    <el-option label="全部路线" value="all" />
                                    <el-option v-for="(item, index) in operationalAssets.lineArray" :key="index"
                                        :label="item.ShowName" :value="item.Code" />
                                </el-select>
                                <el-select v-model="operationalAssets.asset">
                                    <el-option v-for="(item, index) in operationalAssets.assetArray" :key="index"
                                        :label="item.Name" :value="item.Code" />
                                </el-select>
                            </div>
                        </template>
                        <template v-if="!empty.chart1">
                            <div class="chart">
                                <ecp-chart-base-bar theme="whiteTheme" :data="chartData1" linear
                                    :option="chartData1.option" :legend="chartData1.legend" />
                            </div>
                        </template>
                        <template v-else>
                            <ecp-empty class="empty-chart" />
                        </template>
                    </app-business-panel>
                    <app-business-panel mode="card" v-loading="loading.chart2">
                        <template #header>
                            <span class="app-business-panel-title">运营资产-按路线</span>
                        </template>
                        <template #header-append>
                            <div class="app-business-panel-tools">
                                <el-select v-model="routeAssets.line">
                                    <el-option label="全部类型" value="all" />
                                    <el-option v-for="(item, index) in routeAssets.lineArray" :key="index"
                                        :label="item.Name" :value="item.Code" />
                                </el-select>
                                <el-select v-model="routeAssets.asset">
                                    <el-option v-for="(item, index) in routeAssets.assetArray" :key="index"
                                        :label="item.Name" :value="item.Code" />
                                </el-select>
                            </div>
                        </template>
                        <template v-if="!empty.chart2">
                            <div class="chart">
                                <ecp-chart-base-bar theme="whiteTheme" :data="chartData2" linear
                                    :option="chartData2.option" :legend="chartData2.legend" yName="单位：项" />
                            </div>
                        </template>
                        <template v-else>
                            <ecp-empty class="empty-chart" />
                        </template>
                    </app-business-panel>
                </div>
                <div class="chart-right">
                    <app-business-panel mode="card" v-loading="loading.chart3">
                        <template #header>
                            <span class="app-business-panel-title">运营资产多指标分析</span>
                        </template>
                        <template #header-append>
                            <div class="app-business-panel-tools">
                                <el-select v-model="scatterAssets.line">
                                    <el-option label="全部路线" value="all" />
                                    <el-option v-for="(route, index) in scatterAssets.lineArray" :key="index"
                                        :label="route.ShowName" :value="route.Code" />
                                </el-select>
                                <el-select v-model="scatterAssets.type">
                                    <el-option label="全部类型" value="all" />
                                    <el-option v-for="(type, index) in scatterAssets.typeArray" :key="index"
                                        :label="type.Name" :value="type.Code" />
                                </el-select>
                                <el-select v-model="scatterAssets.status" placeholder="选择状态">
                                    <el-option v-for="(status, index) in scatterAssets.statusArray" :key="index"
                                        :label="status.Name" :value="status.Code" />
                                </el-select>
                            </div>
                        </template>
                        <!-- </div> -->
                        <div v-show="!empty.chart3" style="width: 100%;height: 100%;">
                            <div class="one" :data="scatterData" type="scatter" ref="chart3Ref" v-resize="onResize">
                            </div>
                        </div>
                        <div v-show="empty.chart3" style="width: 100%;height: 100%;">
                            <ecp-empty class="empty-chart" />
                        </div>
                        <!-- </div> -->
                    </app-business-panel>
                </div>
            </div>
        </template>
    </app-form-page>
</template>
<script setup>
import { reactive, ref, computed, onMounted, watch, nextTick, onActivated } from 'vue';
import { OperatingAsset } from '@api/asset-panorama/operating-asset';
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import { formatXAxis } from '@utils/format';

const name = 'asset-panorama-operating-asset';

defineOptions({
    name
});

const commonOption = {
    xAxis: {
        axisLabel: {
            color: 'rgba(0, 0, 0, 0.45)'
        }
    }
};

const handleStatusChange = (newStatus) => {
    getScatterData();
};

const title = computed(() => {
    return [
        {
            label: '运营资产'
        }
    ];
});

const loading = reactive({
    chart1: false,
    chart2: false,
    chart3: false
});

const empty = reactive({
    chart1: false,
    chart2: false,
    chart3: false
});

const disabledFutureDates = (date) => {
    return date.getTime() > new Date().getTime();
};

// 在页面被激活时重新调用
onActivated(() => {
    fetchChartData1();
    fetchChartData2();
    getScatterData();
});

const tooltipFrame = (html, title, theme = 'light') => {
    return `
        <div class="ecp-chart-tooltip-wrapper ${theme === 'light' ? 'is-white' : 'is-black'}" >
             <div class="ecp-chart-tooltip-head">${title}</div>
             ${html}
        </div>
        `;
};
const tooltipItem = (params, name, unit) => {
    return `
        <div class="ecp-chart-tooltip-item">
            <span class="ecp-chart-tooltip-label" style="--color: ${params.color}">${name}</span>
            <span class="ecp-chart-tooltip-value">
                <i class="ecp-chart-tooltip-value-num">${params.value}</i>
                <i class="ecp-chart-tooltip-value-unit">${unit}</i>
            </span>
       </div>
   `;
};
const tooltipItem1 = (params, name, value, unit) => {
    return `
        <div class="ecp-chart-tooltip-item">
            <span class="ecp-chart-tooltip-label" style="--color: ${params.color}">${name}</span>
            <span class="ecp-chart-tooltip-value">
                <i class="ecp-chart-tooltip-value-num">${value}</i>
                <i class="ecp-chart-tooltip-value-unit">${unit}</i>
            </span>
       </div>
   `;
};

const chartData1 = reactive({
    ...commonOption,
    dimensions: ['类目名称', '数量'],
    source: [],
    legend: {
        show: false
    },
    option: {
        tooltip: {
            formatter (params) {
                let title = '';
                let html = '';
                for (const param of params) {
                    title = param.axisValue;
                    html += tooltipItem(param, '数量', '项');
                }
                return tooltipFrame(html, title);
            }
        },
        xAxis: {
            axisLabel: {
                rotate: 60,
                interval: 'auto', // 自动调整标签间隔
                color: 'rgba(0, 0, 0, 0.45)',
                formatter: (value) => formatXAxis(value, 6)
            }
        },
        yAxis: {
            name: '单位：项',
            nameTextStyle: {
                padding: [0, -20, 0, 0]
            },
            axisLabel: {
                color: 'rgba(0, 0, 0, 0.45)'
            }

        },
        series: [
            {
                name: '数量',
                yAxisIndex: 0,
                data: [], // 这里将存放数量数据
                type: 'bar',
                barWidth: 15,
                itemStyle: {
                    borderRadius: [10, 10, 10, 10],
                    color: '#0367fc'
                }
            }
        ],
        grid: {
            top: 40,
            bottom: 0,
            left: 0,
            right: 0
        }
    }
});

const operationalAssets = reactive({
    line: '',
    asset: '',
    lineArray: [],
    assetArray: [],
    options: {
        series: []
    }
});
// const formatLineCode = (lineCode) => {
//     const strLineCode = String(lineCode);
//     if (strLineCode.length > 2) {
//         return strLineCode;
//     } else {
//         return strLineCode.padStart(2, '0');
//     }
// };
const formatLineCode = (code) => {
    if (!code || code === 'all') return code;
    return code.toString().padStart(2, '0');
};

const fetchChartData1 = async () => {
    if (isInitialLoad.value) {
        return;
    }
    loading.chart1 = true; // 开始加载
    try {
        const response = await OperatingAsset.getOperatingAssetType({
            assetStatus: parseInt(operationalAssets.asset) || '01',
            // udLine: formatLineCode(operationalAssets.line) || '01'
            // udLine: operationalAssets.line || '01'
            // udLine: operationalAssets.line === 'all' ? '' : formatLineCode(operationalAssets.line)
            udLine: formatLineCode(operationalAssets.line) || '01'
        });
        console.log('第一个柱状图返回的数据', response);
        if (response && response.Data && response.Data.length) {
            empty.chart1 = false;
            chartData1.option.xAxis.data = response.Data.map(item => item.AssetName);
            chartData1.option.series[0].data = response.Data.map(item => item.AssetValue);
            // 确保更新图表
        } else {
            console.error('接口返回的数据为空或不满足条件:', response);
            empty.chart1 = true;
        }
    } catch (error) {
        console.error('获取运营资产数据失败:', error);
        empty.chart1 = true;
    } finally {
        loading.chart1 = false; // 结束加载
    }
};

const chartData2 = reactive({
    ...commonOption,
    dimensions: ['类目名称', '数量'],
    source: [],
    legend: {
        show: false
    },
    option: {
        tooltip: {
            formatter (params) {
                let title = '';
                let html = '';
                for (const param of params) {
                    title = param.axisValue;
                    html += tooltipItem(param, '数量', '项');
                }
                return tooltipFrame(html, title);
            }
        },
        xAxis: {
            axisLabel: {
                interval: 'auto', // 自动调整标签间隔
                color: 'rgba(0, 0, 0, 0.45)' // 设置标签颜色
            }
        },
        yAxis: {
            name: '单位：项',
            nameTextStyle: {
                padding: [0, -20, 0, 0]
            },
            axisLabel: {
                color: 'rgba(0, 0, 0, 0.45)' // 设置标签颜色
            }
        },
        series: [
            {
                name: '数量',
                data: [], // 这里将存放数量数据
                type: 'bar',
                barWidth: 15,
                itemStyle: {
                    borderRadius: [10, 10, 10, 10],
                    color: '#0367fc'
                }
            }
        ],
        grid: {
            top: 40,
            bottom: 0,
            left: 0,
            right: 0
        }
    }
});

const routeAssets = reactive({
    line: '',
    asset: '',
    lineArray: [],
    assetArray: [],
    options: {
        series: []
    }
});

const fetchChartData2 = async () => {
    if (isInitialLoad.value) {
        return;
    }
    loading.chart2 = true;
    try {
        const params = {
            // assetNum: routeAssets.line || '011',
            // assetStatus: routeAssets.asset || '01'
            // assetNum: routeAssets.line === 'all' ? '' : routeAssets.line || '011',
            assetNum: routeAssets.line === 'all' ? '' : routeAssets.line,
            assetStatus: routeAssets.asset === 'all' ? '' : parseInt(routeAssets.asset)
        };
        const response = await OperatingAsset.getOperatingAssetRoute(params);
        console.log('第2个柱状图返回的数据', response);

        if (response && response.Data && response.Data.length) {
            empty.chart2 = false;
            chartData2.option.xAxis.data = response.Data.map(item => item.LineName);
            chartData2.option.series[0].data = response.Data.map(item => item.Amount);
        } else {
            console.error('接口返回的数据为空或不满足条件:', response);
            empty.chart2 = true;
        }
    } catch (error) {
        console.error('获取运营资产路线数据失败:', error);
        empty.chart2 = true;
    } finally {
        loading.chart2 = false;
    }
};

watch(() => [operationalAssets.line, operationalAssets.asset], ([newLine, newAsset], [oldLine, oldAsset]) => {
    if (newLine !== oldLine || newAsset !== oldAsset) {
        if (newLine !== '' && newAsset !== '') {
            fetchChartData1();
        }
    }
});

// 监听 routeAssets 的变化
watch(() => [routeAssets.line, routeAssets.asset], ([newLine, newAsset], [oldLine, oldAsset]) => {
    if (newLine !== oldLine || newAsset !== oldAsset) {
        if (newLine !== '' && newAsset !== '') {
            fetchChartData2();
        }
    }
}
);

onMounted(() => {
    scatterAssets.line = 'all'; // 全部路线
    scatterAssets.type = 'all'; // 全部类型
    scatterAssets.status = '1'; // 利用率
    operationalAssets.line = 'all'; // 运营资产-按类型-全部路线
    routeAssets.asset = 'all'; // 运营资产-按路线-全部类型

    // 获取所有线路
    OperatingAsset.getLineCode().then(response => {
        if (response.Data && response.Data.length) {
            console.log('获取所有线路', response.Data);
            const lines = response.Data.map(item => ({
                Code: parseInt(item.LineCode),
                ShowName: item.LineName
            }));
            operationalAssets.lineArray = lines;
            scatterAssets.lineArray = lines;
            if (lines.length > 0 && operationalAssets.line === '') {
                operationalAssets.line = 'all';
            }
        } else {
            console.log('没有线路数据返回');
        }
    }).catch(error => {
        console.error('获取线路代码集合失败:', error);
    });
    // 获取资产类型数据
    OperatingAsset.getAssetList().then(response => {
        if (response.Data && response.Data.length) {
            console.log('获取资产类型数据:', response.Data);
            const assets = response.Data.map(item => ({
                Name: item.AssetName,
                Code: item.AssetCode
            }));
            routeAssets.lineArray = assets;
            scatterAssets.typeArray = assets;
            if (assets.length > 0) {
                // (routeAssets.line) = assets[0].Code;
                routeAssets.line = 'all';
            }
            if (!routeAssets.asset) {
                routeAssets.asset = 'all';
            }
        } else {
            console.log('没有资产类型数据返回');
        }
    }).catch(error => {
        console.error('获取资产类型数据失败:', error);
    });
    // 获取运营资产状态信息集合
    OperatingAsset.getAssetStatusList().then(response => {
        if (response.Data && response.Data.length) {
            const status = response.Data.map(item => ({
                Name: item.AssetStatusName,
                Code: item.AssetStatusCode
            }));
            operationalAssets.assetArray = status;
            routeAssets.assetArray = status;
            if (status.length > 0) {
                operationalAssets.asset = status[0].Code; // 默认选中第一个资产类型的CODE
                routeAssets.asset = status[0].Code;
            }
        } else {
            console.log('没有资产类型数据返回');
        }
    }).catch(error => {
        console.error('获取资产类型数据失败:', error);
    });
    nextTick(() => {
        isInitialLoad.value = false;
    });
});

const scatterData = ref({
    ...commonOption,
    dimensions: ['X', 'Y'],
    source: []
});
const myChart = ref(null);

const scatterAssets = reactive({
    line: '全部路线', // 用于绑定全部路线下拉框
    type: '全部类型', // 用于绑定全部类型下拉框
    status: '',
    lineArray: [],
    typeArray: [],
    statusArray: [
        { Name: '利用率', Code: '1' },
        { Name: '待利用率', Code: '2' }
    ],
    source: []
});

const isInitialLoad = ref(true);

const getScatterData = () => {
    loading.chart3 = true;
    const params = {
        exploitRateSituation: scatterAssets.status
    };
    // 是否选择了“全部类型”和“全部路线”
    if (scatterAssets.type !== 'all') {
        params.assetNum = scatterAssets.type;
    }
    if (scatterAssets.line !== 'all') {
        // params.udLine = scatterAssets.line;
        params.udLine = formatLineCode(scatterAssets.line);
    }
    console.log('scatterAssets.line:', scatterAssets.line);
    console.log('getScatterData params:', params); // 添加日志，检查 params 的值
    OperatingAsset.getAnalyseByLine(params).then(response => {
        console.log('散点图返回的数据', response);
        if (response && response.Data && response.Data.length > 0) {
            empty.chart3 = false;
            const formattedData = response.Data.map(item => [Number(item.Amount), Number(item.Rate), item.AssetName]);
            scatterData.value.source = formattedData;
            // 根据scatterAssets.status更新Y轴名称
            // const yAxisName = scatterAssets.status === '1' ? '待利用率' : '利用率';
            const yAxisName = scatterAssets.status === '1' ? '利用率' : '待利用率';
            updateScatterChart(yAxisName);
        } else {
            console.error('接口返回的数据为空', response);
            scatterData.value.source = []; // 设置为空数组，表示没有数据
            empty.chart3 = true;
        }
    }).catch(error => {
        console.error('运营资产多指标分析-车站:', error);
        empty.chart3 = true;
    }).finally(() => {
        loading.chart3 = false;
    });
};

// 更新散点图的方法
const updateScatterChart = (yAxisName) => {
    const chartDom = document.querySelector('.one');
    if (chartDom && myChart3) {
        myChart3.setOption({
            yAxis: {
                name: `${yAxisName}%`,
                nameTextStyle: {
                    padding: [0, -12, 0, 0],
                    color: 'rgba(0, 0, 0, 0.45)'
                },
                axisLabel: {
                    color: 'rgba(0, 0 0, 0.45)',
                    formatter: function (value) {
                        return (value * 100).toFixed(0) + '%';
                    }
                }
            },
            series: [
                {
                    symbolSize: 20,
                    data: scatterData.value.source,
                    type: 'scatter',
                    itemStyle: {
                        color: '#0367FC'
                    }
                }
            ]
        });
    }
};

// 监听 scatterData 的变化并更新图表
watch(() => scatterData.value.source, (newSource) => {
    nextTick(() => {
        const chartDom = document.querySelector('.one');
        if (chartDom) {
            const myChart = echarts.getInstanceByDom(chartDom) || echarts.init(chartDom);
            myChart.setOption({
                series: [{ data: newSource }]
            });
        }
    });
}, { deep: true });

const chart3Ref = ref(null);
let myChart3 = null;

// 定义onResize方法
const onResize = () => {
    nextTick(() => {
        if (myChart3) {
            myChart3.resize();
        }
    });
};

// onMounted生命周期钩子
onMounted(() => {
    if (chart3Ref.value) {
        myChart3 = echarts.init(chart3Ref.value);
        // 设置图表选项
        const option = {
            xAxis: {
                type: 'value',
                splitLine: { show: false },
                name: '资产数量',
                nameTextStyle: {
                    color: 'rgba(0, 0, 0, .45)' // 保持x轴标题颜色不变
                },
                axisTick: { show: false },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: 'rgba(29, 33, 55, .1)'
                    }
                },
                axisLabel: {
                    show: true,
                    color: 'rgba(0, 0, 0, 0.45)',
                    fontFamily: 'D-DIN'
                }
            },
            yAxis: {
                type: 'value',
                name: '利用率%',
                nameTextStyle: {
                    padding: [0, -12, 0, 0],
                    color: 'rgba(0, 0 0, 0.45)'
                },
                axisLine: { show: false },
                axisTick: { show: false },
                axisLabel: {
                    color: 'rgba(0, 0, 0, 0.45)',
                    fontFamily: 'D-DIN',
                    formatter: function (value) {
                        return (value * 100).toFixed(0) + '%';
                    }
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        type: 'dashed' // 设置为虚线
                    }
                }
            },
            tooltip: {
                trigger: 'item',
                enterable: true,
                textStyle: {
                    fontSize: 10
                },
                extraCssText:
                    'padding: 0; border-radius: 0.4em; border: 0; overflow: hidden;',
                backgroundColor: '#fff',
                formatter: (params) => {
                    // 根据散点图的 status 状态来决定显示待利用率还是利用率
                    const unit = scatterAssets.status === '1' ? '待利用率' : '利用率';
                    // 构建 tooltip 的 HTML 结构
                    const assetQuantity = params.value[0]; // 资产数量
                    const utilizationRate = params.value[1]; // 利用率
                    const assetName = params.value[2];
                    // 创建待利用率的 HTML
                    const utilizationHtml = tooltipItem1(params, unit, (utilizationRate * 100).toFixed(2), '%');
                    const quantityHtml = tooltipItem1(params, '资产数量', assetQuantity, '项');
                    return tooltipFrame(utilizationHtml + quantityHtml, assetName);
                }
            },
            series: [
                {
                    symbolSize: 20,
                    data: scatterData.value.source,
                    type: 'scatter',
                    itemStyle: {
                        color: '#0367FC'
                    }
                }
            ]
        };
        myChart3.setOption(option);
    }
});

watch(() => [scatterAssets.line, scatterAssets.type, scatterAssets.status], ([line, type, status]) => {
    if (!isInitialLoad.value) {
        getScatterData();
    }
});
</script>

<style scoped lang="scss">
$page-name: asset-panorama-operating-asset;

.#{$page-name} {
    .chart-container {
        display: grid;
        grid-template-columns: 1fr 1.5fr;
        /* grid-template-rows: 1fr 1fr; */
        /* grid-template-columns: repeat(2, minmax(440px, 1fr)); */
        /* grid-template-columns: repeat(2, minmax(320px, 1fr)); */
        gap: 16px;
        /* height: 100vh; */
        height: 100%;
        width: 100%;
        flex: 1 1 0;

        .chart-left {
            grid-column: 1 / 2;
            display: flex;
            flex-direction: column;
            gap: 16px;

            .app-business-panel {
                flex: 1 1 0;

                .app-business-panel-tools {
                    display: flex;
                    justify-content: space-between;
                    gap: 8px;

                    .elp-select {
                        width: 100px;
                        height: 32px;
                    }
                }

                .chart {
                    width: 100%;
                    height: 100%;
                }

                .empty-chart {
                    width: 100%;
                    height: 100%;
                }
            }

            :deep(.app-business-panel.card-panel) {
                height: calc(50vh - 48px);
                min-height: 270px;
            }
        }

        .chart-right {
            grid-column: 2 / 3;
            display: flex;
            flex-direction: column;

            .app-business-panel {

                .app-business-panel-tools {
                    display: flex;
                    justify-content: space-between;
                    gap: 8px;

                    .elp-select {
                        width: 100px;
                        height: 32px;
                    }
                }

                .one {
                    width: 100%;
                    height: 100%;
                }

                .empty-chart {
                    width: 100%;
                    height: 100%;
                }
            }

            :deep(.app-business-panel.card-panel) {
                height: calc(100vh - 90px);
                min-height: 540px;
            }
        }
    }
}
</style>
