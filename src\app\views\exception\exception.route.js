export const Redirect = () => import('./redirect.jsx');

/* 默认路由 */
export const ComingSoon = () => import('./coming-soon.vue');

export function ExceptionRoutes () {
    return [
        {
            path: '/redirect/:url(.*)',
            label: '重定向',
            name: 'redirect',
            component: Redirect
        },
        {
            path: '/:pathMatch(.*)*',
            label: '暂未开放',
            name: 'ComingSoon',
            component: ComingSoon
        }
    ];
}
