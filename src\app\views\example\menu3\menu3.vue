<template>
    <app-business-panel class="Menu3">
        <template #header>
            <div class="app-business-panel-title">Menu 3 Load As：{{ loadAs }}</div>

            <slot name="header"></slot>
        </template>

        <el-form :model="form" class="app-form app-form-inline filter">
            <el-form-item label="名称：">
                <el-input v-model="form.name" placeholder="请输入名称" clearable />
            </el-form-item>
            <el-form-item label="状态：">
                <el-input v-model="form.status" placeholder="请输入状态" clearable />
            </el-form-item>
            <el-form-item label="时间：">
                <el-date-picker v-model="form.time" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                                end-placeholder="结束日期" value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>
            </el-form-item>
        </el-form>

        <slot></slot>

        <el-button type="primary" @click="handleConfirm">提 交</el-button>
    </app-business-panel>
</template>

<script>
import { unref } from 'vue';

export default {
    name: 'Menu3',
    props: {
        detailInfo: {
            type: Object
        },
        callback: Function
    },
    data: function () {
        let detailInfo = this.detailInfo || this.$route.query.detailInfo;
        console.log('%c detailInfo', 'font-size:18px;color:blue;font-weight:700;', detailInfo);
        if (typeof detailInfo === 'string') {
            try {
                detailInfo = JSON.parse(detailInfo);
            } catch (error) {
                detailInfo = {};
            }
        }
        return {
            form: {
                name: detailInfo?.name || '',
                status: detailInfo?.status || '',
                time: detailInfo?.time || null
            }
        };
    },
    computed: {
        loadAs () {
            if (this.$route.path.match(/menu3/i)) {
                return 'Route';
            }
            return this?.$attrs?.query?.loadAs
                ? this.$attrs.query.loadAs
                : 'Component';
        }
    },
    methods: {
        handleConfirm () {
            console.log(JSON.stringify(this.form, null, 4), this.$route?.query);

            if (typeof this.callback === 'function') {
                this.callback('confirm', this.form);
            }
            this.$emit('confirm', 'confirm', unref(this.form));
        }
    }
};
</script>

<style scoped lang="scss">
.Menu3 {
    // border: 3px solid $--color-primary;
    padding: 0 0 var(--spacer-large-3);
}
</style>
