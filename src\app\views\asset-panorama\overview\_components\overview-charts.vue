<script setup>
import { reactive } from 'vue';
import { cloneDeep } from 'lodash-es';
import * as Api from '@api/index';
import { useRouter } from 'vue-router';
import { echarts } from 'ecp-chart';
import usePermissionStore from '@store/permission';
import { ElMessage } from 'element-plus';

const router = useRouter();
const color = ['rgba(14,99,242,1)', 'rgba(14,99,242,1)'];
const _color = ['#0367FC', '#11B062', '#487690'];
const hiddenLegend = { show: false };
const commonOption = {
    xAxis: {
        axisLabel: {
            color: 'rgba(29,33,55,0.45)'
        }
    }
};
const commonTitleOption = {
    textStyle: {
        fontSize: 18,
        fontWeight: 400,
        fontFamily: '"SourceHanSansSC", "Microsoft Yahei", Tahoma, "Helvetica Neue", Helvetica',
        color: 'rgba(0,0,0,0.85)'
    }
};

const tooltipFrame = (html, title, theme = 'light') => {
    return `
        <div class="ecp-chart-tooltip-wrapper ${theme === 'light' ? 'is-white' : 'is-black'}">
             <div class="ecp-chart-tooltip-head">${title}</div>
             ${html}
        </div>
        `;
};
const tooltipItem = (params, name, unit) => {
    return `
        <div class="ecp-chart-tooltip-item">
            <span class="ecp-chart-tooltip-label" style="--color: ${params.color}">${name}</span>
            <span class="ecp-chart-tooltip-value">
                <i class="ecp-chart-tooltip-value-num">${params.value}</i>
                <i class="ecp-chart-tooltip-value-unit">${unit || ''}</i>
            </span>
       </div>
   `;
};
const getOperationAssetList = async () => {
    const response = await Api.AssetPanorama.getOperationAssetList();
    const typeList = response.Data || [];
    const totalType = [{ Code: 'total', Name: '全部类型' }];
    operationalAssets.assetArray = [...totalType, ...typeList];
    operationalAssets.asset = 'total';
};
const getLineList = async () => {
    const response = await Api.AssetPanorama.getLineList();
    operationalAssets.lineArray = response.Data;
    operationalAssets.line = response.Data[0].Code;
};

const loading = reactive({
    operationAsset: false,
    ads: false,
    land: false,
    property: false,
    officeAssets: false
});
const empty = reactive({
    operationAsset: false,
    realEstate: false,
    ads: false,
    land: false,
    property: false,
    officeAssets: false
});
const getOperationAsset = async () => {
    try {
        loading.operationAsset = true;
        const data = {
            assetCode: operationalAssets.asset === 'total' ? '' : operationalAssets.asset,
            lineCode: operationalAssets.line
        };
        const response = await Api.AssetPanorama.getOperationAsset(data);
        empty.operationAsset = !(Array.isArray(response.Data) && response.Data.length);
        operationalAssets.options.series = [
            {
                type: 'treemap',
                width: '100%',
                height: '90%',
                itemStyle: {
                    borderWidth: 1,
                    borderColor: 'transparent'
                },
                breadcrumb: {
                    show: false
                },
                data: response.Data?.map(item => ({
                    name: item.StatDim,
                    value: item.DataCount
                }))
            }
        ];
    } catch (e) {
        console.log('%c getOperationAsset error', 'color: red', e);
        empty.operationAsset = true;
    } finally {
        loading.operationAsset = false;
    }
};
const getHouseAreaPercentData = async () => {
    try {
        loading.realEstate = true;
        const response = await Api.AssetPanorama.getHouseAreaData();
        const { TotalArea, UseArea } = response.Data;
        const value = Math.floor((UseArea / TotalArea) * 100) || 1;
        realEstateValue.value = value;
        realEstate.series[0].data[0].value = value;
        realEstate.series[1].data[0].value = value;
    } catch (e) {
        console.log('%c getHouseAreaPercentData error', 'color: red', e);
        empty.realEstate = true;
    } finally {
        loading.realEstate = false;
    }
};
const getAdvertData = async () => {
    try {
        loading.ads = true;
        const response = await Api.AssetPanorama.getAdvertData();
        empty.ads = !(response.Data && Object.keys(response.Data).length);
        const { LegendData, SeriesData, XAxis } = response.Data;
        ads.dimensions = LegendData;
        ads.xAxis.data = XAxis;
        ads.series = SeriesData.map(item => {
            return {
                type: 'line',
                name: item.Name,
                data: item.Data,
                symbolSize: 8,
                showSymbol: true,
                lineStyle: {
                    width: 4
                },
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 2
                }
            };
        });
    } catch (e) {
        console.log('%c getAdvertData error', 'color: red', e);
        empty.ads = true;
    } finally {
        loading.ads = false;
    }
};
const getLandAreaData = async () => {
    try {
        loading.land = true;
        const response = await Api.AssetPanorama.getLandArea();
        empty.land = !(response.Data && Object.keys(response.Data).length);
        const { Axis, SeriesData } = response.Data;
        land.xAxis.data = Axis;
        land.series = {
            type: 'line',
            data: SeriesData,
            itemStyle: {
                borderColor: '#fff',
                borderWidth: 2
            },
            lineStyle: {
                width: 4
            },
            smooth: true,
            symbolSize: 8,
            showSymbol: false,
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(3,103,252,0.2)' },
                    { offset: 1, color: 'rgba(3,103,252,0)' }
                ])
            }
        };
        land.temp.source = Axis.map((item, index) => ({
            面积: item,
            num: SeriesData[index]
        }));
    } catch (e) {
        console.log('%c getLandAreaData error', 'color: red', e);
        empty.land = true;
    } finally {
        loading.land = false;
    }
};
const getPropertyAreaData = async () => {
    try {
        loading.property = true;
        const response = await Api.AssetPanorama.getPropertyArea();
        empty.property = !(response.Data && Object.keys(response.Data).length);
        const { XAxis, SeriesData, LegendData } = response.Data;
        property.legend.data = LegendData;
        property.temp.dimensions = ['line', ...LegendData];
        property.temp.source = XAxis.map((item, index) => (SeriesData.reduce((acc, seriesDatum) => {
            acc.line = item;
            acc[seriesDatum.Name] = seriesDatum.Name === '出租率' ? seriesDatum.Data[index] * 100 : seriesDatum.Data[index];
            return acc;
        }, {})));
        property.xAxis.data = XAxis;
        property.series = SeriesData.map(item => {
            const data = {
                name: item.Name,
                type: item.Name !== '出租率' ? 'bar' : 'line',
                yAxisIndex: item.Name !== '出租率' ? 0 : 1
            };
            if (item.Name !== '出租率') {
                data.itemStyle = {
                    borderRadius: [10, 10, 10, 10] // 设置柱状图顶部圆角
                };
            } else {
                data.showSymbol = true;
                data.symbolSize = 6;
                data.lineStyle = {
                    type: 'dashed',
                    smooth: true
                };
            }
            return data;
        });
    } catch (e) {
        console.log('%c getPropertyAreaData error', 'color: red', e);
        empty.property = true;
    } finally {
        loading.property = false;
    }
};
const getWorkAsset = async () => {
    try {
        loading.officeAssets = true;
        const response = await Api.AssetPanorama.getWorkAsset();
        empty.officeAssets = !(response.Data && Object.keys(response.Data).length);
        const { Axis, SeriesData } = response.Data;
        officeAssets.xAxis.type = 'category';
        officeAssets.xAxis.data = Axis;
        officeAssets.xAxis.axisLabel = { interval: 0, rotate: 0, color: 'rgba(29,33,55,0.45)' };
        officeAssets.series = [
            {
                type: 'bar',
                barWidth: '15px',
                itemStyle: {
                    borderRadius: [10, 10, 10, 10] // 设置柱状图顶部圆角
                }
            }
        ];
        officeAssets.temp.source = Axis.map((item, index) => ({
            类目: item,
            数量: SeriesData[index]
        }));
    } catch (e) {
        console.log('%c getWorkAsset error', 'color: red', e);
        empty.officeAssets = true;
    } finally {
        loading.officeAssets = false;
    }
};

const initChart = () => {
    Promise.allSettled([getOperationAssetList(), getLineList()]).then(() => getOperationAsset());
    getHouseAreaPercentData();
    getAdvertData();
    getLandAreaData();
    getPropertyAreaData();
    getWorkAsset();
};
onMounted(initChart);

const realEstateValue = ref(0);
const realEstate = reactive({
    series: [
        {
            type: 'gauge',
            radius: '100%',
            progress: {
                show: true,
                width: 20,
                roundCap: true,
                itemStyle: {
                    color: '#0367FC'
                }
            },
            axisLine: {
                roundCap: true,
                lineStyle: {
                    width: 20
                }
            },
            axisTick: {
                show: false
            },
            splitLine: {
                show: false
            },
            axisLabel: {
                show: false
            },
            pointer: {
                show: false
            },
            detail: {
                valueAnimation: true,
                fontSize: 40,
                fontFamily: 'D-DIN-BOLD',
                fontWeight: '700',
                formatter: '{value}%',
                offsetCenter: [0, '-5%'],
                color: '#1D2137'
            },
            title: {
                fontSize: 14,
                offsetCenter: [0, 30],
                color: '#1D2137',
                opacity: 0.65
            },
            data: [
                {
                    value: 0,
                    name: '房产使用率'
                }
            ]
        },
        {
            type: 'gauge',
            progress: {
                show: false
            },
            pointer: {
                icon: 'circle',
                width: 15,
                offsetCenter: [-5, '-91%'],
                itemStyle: {
                    color: '#ffffff'
                }
            },
            splitLine: {
                show: false
            },
            axisTick: {
                show: false
            },
            axisLine: {
                show: false
            },
            axisLabel: {
                show: false
            },
            data: [
                {
                    value: 0
                }
            ],
            detail: {
                show: false
            },
            zlevel: 100
        }
    ]
});
const ads = reactive({
    ...cloneDeep(commonOption),
    tooltip: {
        formatter (params) {
            let title = '';
            let html = '';
            for (const param of params) {
                title = param.axisValue;
                html += tooltipItem(param, param.seriesName);
            }
            return tooltipFrame(html, title);
        }
    },
    color: _color,
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    grid: {
        top: '25%'
    },
    title: {
        text: '文体广告',
        left: -5,
        top: 2,
        ...commonTitleOption
    },
    series: []
});
const land = reactive({
    ...cloneDeep(commonOption),
    tooltip: {
        formatter (params) {
            let title = '';
            let html = '';
            for (const param of params) {
                title = param.axisValue;
                html += tooltipItem(param, '面积', '万亩');
            }
            return tooltipFrame(html, title);
        }
    },
    grid: {
        top: '15%'
    },
    yAxis: {
        nameTextStyle: {
            padding: [0, -30, 0, 0]
        }
    },
    color: _color,
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    series: [],
    temp: {
        dimensions: ['面积', 'num'],
        source: []
    }
});
const operationalAssets = reactive({
    line: undefined,
    asset: undefined,
    lineArray: [],
    assetArray: [],
    options: {
        series: []
    }
});
const property = reactive({
    color: _color,
    grid: {
        top: '25%'
    },
    title: {
        text: '物业',
        left: -5,
        ...commonTitleOption
    },
    legend: {
        x: 'right',
        top: 0,
        itemWidth: 8,
        itemHeight: 8,
        data: []
    },
    yAxis: [
        {
            type: 'value',
            name: '单位:万平方',
            position: 'left',
            nameTextStyle: {
                padding: [0, -30, 0, 0],
                color: 'rgba(29,33,55,0.45)'
            }
        },
        {
            type: 'value',
            position: 'right',
            axisLabel: {
                formatter: '{value} %',
                color: 'rgba(29,33,55,0.45)',
                fontSize: 10
            },
            splitLine: {
                show: false
            }
        }
    ],
    xAxis: {
        axisTick: {
            show: false
        },
        axisLine: {
            lineStyle: {
                color: 'rgba(29,33,55,0.45)'
            }
        },
        axisLabel: {
            color: 'rgba(29,33,55,0.45)',
            fontSize: 12 // 设置字体大小
        }
    },
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    series: [],
    temp: {
        dimensions: ['name', 'num'],
        source: []
    }
});
const officeAssets = reactive({
    color: _color,
    grid: {
        top: '15%',
        left: 0
    },
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    yAxis: {
        nameTextStyle: {
            padding: [0, -20, 0, 0]
        }
    },
    xAxis: {},
    series: [],
    temp: {
        dimensions: ['类目', '数量'],
        source: []
    }
});

watch(() => operationalAssets.line, (value, oldValue) => {
    if (oldValue === undefined) return;
    getOperationAsset();
});
watch(() => operationalAssets.asset, (value, oldValue) => {
    if (oldValue === undefined) return;
    getOperationAsset();
});
const context = 'asset-panorama';
const routerMap = {
    房产: `${context}-house-asset`,
    文体广告: `${context}-advertisement-asset`,
    土地: `${context}-land-asset`,
    运营资产: `${context}-operating-asset`,
    物业: `${context}-property-asset`,
    办公资产: `${context}-office-asset`
};
const routeRedirects = (name) => {
    if (!tempTitle[name].startsWith('暂未开放')) return router.push({ name: routerMap[name] });
    ElMessage.info({ message: '暂未开放，敬请期待~', showClose: true, grouping: true });
};

const permissionStore = usePermissionStore();
const tempTitle = {};
const tempMenu = permissionStore.navMenu.find(menu => menu.FunTag === 'ASSET_FULL_VIEW')?.ChildNodes || [];
const getTempTitle = (name) => {
    if (tempTitle[name]) return tempTitle[name];
    const temp = tempMenu.find(menu => menu.Text === name);
    tempTitle[name] = `跳转到${temp.Text}页面`;
    return tempTitle[name];
};
</script>

<template>
    <div class="overview-charts">
        <app-business-panel mode="card" title="房产" v-loading="loading.realEstate">
            <ecp-chart-simple-gauge v-if="!empty.realEstate" :title="getTempTitle('房产')" :option="realEstate"
                :data="realEstateValue" :decimalNum="0" @click="routeRedirects('房产')" />
            <ecp-empty v-else :title="getTempTitle('房产')" @click="routeRedirects('房产')" />
        </app-business-panel>
        <app-business-panel mode="card" title="文体广告" :show-header="empty.ads" v-loading="loading.ads">
            <ecp-chart-base-line v-if="!empty.ads" theme="whiteTheme" :title="getTempTitle('文体广告')" :data="ads"
                :option="ads" @click="routeRedirects('文体广告')" />
            <ecp-empty v-else :title="getTempTitle('文体广告')" @click="routeRedirects('文体广告')" />
        </app-business-panel>
        <app-business-panel mode="card" title="土地" v-loading="loading.land">
            <ecp-chart-base-line v-if="!empty.land" theme="whiteTheme" :title="getTempTitle('土地')" :data="land.temp"
                :legend="hiddenLegend" :option="land" yName="单位：万亩" linear area smooth @click="routeRedirects('土地')" />
            <ecp-empty v-else :title="getTempTitle('土地')" @click="routeRedirects('土地')" />
        </app-business-panel>
        <app-business-panel mode="card" title="运营资产" v-loading="loading.operationAsset">
            <template #header>
                <span class="app-business-panel-title">运营资产</span>
                <div class="app-business-panel-tools">
                    <el-select v-model="operationalAssets.line">
                        <el-option v-for="(item, index) in operationalAssets.lineArray" :key="index"
                            :label="item.ShowName" :value="item.Code" />
                    </el-select>
                    <el-select v-model="operationalAssets.asset">
                        <el-option v-for="(item, index) in operationalAssets.assetArray" :key="index" :label="item.Name"
                            :value="item.Code" />
                    </el-select>
                </div>
            </template>
            <app-chart-treemap v-if="!empty.operationAsset" :title="getTempTitle('运营资产')" text="运营资产"
                :options="operationalAssets.options" @click="routeRedirects('运营资产')" />
            <ecp-empty v-else :title="getTempTitle('运营资产')" @click="routeRedirects('运营资产')" />
        </app-business-panel>
        <app-business-panel mode="card" title="物业" :show-header="empty.property" v-loading="loading.property">
            <ecp-chart-bar-line v-if="!empty.property" theme="whiteTheme" :title="getTempTitle('物业')"
                :unit="['万平方', '%']" :option="property" :data="property.temp" @click="routeRedirects('物业')" />
            <ecp-empty v-else :title="getTempTitle('物业')" @click="routeRedirects('物业')" />
        </app-business-panel>
        <app-business-panel mode="card" title="办公资产" v-loading="loading.officeAssets">
            <ecp-chart-base-bar v-if="!empty.officeAssets" theme="whiteTheme" :title="getTempTitle('办公资产')"
                :data="officeAssets.temp" :legend="hiddenLegend" yName="单位：项" :option="officeAssets" :bar-width="15"
                linear @click="routeRedirects('办公资产')" />
            <ecp-empty v-else :title="getTempTitle('办公资产')" @click="routeRedirects('办公资产')" />
        </app-business-panel>
    </div>
</template>

<style scoped lang="scss">
.overview-charts {
    display: grid;
    grid-template-areas:
        "header header"
        "main main"
        "footer footer";
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    gap: var(--spacer-large);
    height: 100%;
    width: 100%;

    :deep(.app-business-panel) {
        width: 400px;
        height: 264px;

        .app-business-panel-header-left {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .app-business-panel-tools {
                display: inline-flex;
                gap: var(--spacer);

                .elp-select {
                    width: 100px;
                    height: 32px;
                }
            }
        }
    }

    :deep(.app-business-panel-content) {
        height: 100%;
        display: flex;
    }

    :deep(.ecp-chart-simple-gauge) {
        margin: 0 auto;

        img {
            display: none;
        }

        .label-content {
            display: none;
        }
    }

    :deep(.app-business-panel-content, .ecp-chart-container, .app-chart-treemap) {
        cursor: pointer;
    }
}
</style>
