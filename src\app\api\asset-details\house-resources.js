import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

const prefix = `${AssetsAnalysisService}/assetDetails`;

export const HouseResources = {
    // 房产明细
    getRealEstateDetail (params) {
        return request.post(`${prefix}/getPropertyDetailsPage`, params).then(
            ({ data }) => data
        );
    },

    // 获取路线信息
    getRouteInfo () {
        return request.get(`${prefix}/getLineDesc`).then(({ data }) => data);
    },

    // 获取房产项目类型
    getProjectType () {
        return request.get(`${prefix}/getPropertyType`).then(({ data }) => data);
    },

    // 导出房产项目明细
    exportRealEstateDetail (params) {
        return request.post(`${prefix}/exportPropertyDetail`, params, { responseType: 'blob' });
    }
};
