<!-- <docs>
# 模板下载+批量导入
</docs> -->

<template>
    <el-dialog class="app-file-import" custom-class="app-file-import-custom" :visible="visible" :title="title || '批量上传'"
        append-to-body :close-on-click-modal="false" :before-close="handleClose">
        <div class="app-file-import-step">
            <div class="app-file-import-step-item">
                <div class="app-file-import-step-item-label font-medium">步骤一</div>
                <div class="app-file-import-step-item-content">
                    <div class="app-file-import-step-item-content-text">下载模板文件</div>
                    <ecp-button class="app-file-import-upload-btn" text="下载模板" icon="ecp-icon-download"
                        @click="downloadTemplate" />
                </div>
            </div>
            <div class="app-file-import-step-item">
                <div class="app-file-import-step-item-label font-medium">步骤二</div>
                <div class="app-file-import-step-item-content">
                    <template v-if="$slots.tip || $slots.tip">
                        <slot name="tip" />
                    </template>
                    <template v-else>
                        <div class="app-file-import-step-item-content-text">编辑文件</div>
                        <div class="app-file-import-step-item-content-text text-mark text-required">按照模板格式填写导入数据。</div>
                    </template>
                </div>
            </div>
            <div class="app-file-import-step-item">
                <div class="app-file-import-step-item-label font-medium">步骤三</div>
                <div class="app-file-import-step-item-content">
                    <div class="app-file-import-step-item-content-text">选择导入文件</div>
                    <el-upload class="app-file-import-upload" ref="uploader" drag v-bind="uploadProps">
                        <template #trigger>
                            <ecp-button class="app-file-import-upload-btn" text="选择文件" icon="ecp-icon-link" />
                        </template>
                        <!-- <div slot="tip" class="el-upload__tip">
                            <span class="require-asterisk danger">*</span>
                            请先下载模板，按照模板格式填写数据后导入
                        </div> -->
                    </el-upload>
                </div>
            </div>
        </div>
        <template #footer>
            <ecp-button text="取 消" @click="handleClose" />
            <ecp-button text="上 传" type="primary" @click="upload" :disabled="fileList.length === 0" />
        </template>
    </el-dialog>
</template>

<script>
import Axios from 'axios';

const pureAxios = Axios.create();

export default {
    name: 'app-file-import',
    props: {
        action: {
            type: String,
            default: ''
        },
        templatePath: {
            type: String,
            default: ''
        },
        templateName: {
            type: String,
            default: ''
        },
        visible: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: '批量上传'
        },
        otherParams: {
            type: Object,
            default: () => ({})
        },
        beforeUpload: Function,
        customRequest: Function
    },
    data () {
        return {
            fileList: []
        };
    },
    computed: {
        uploadUrl () {
            return (
                this.action || this.$api.AttachmentService.attachmentUpload()
            );
        },
        uploaderInstance () {
            return this.$refs.uploader;
        },
        httpRequestFunc () {
            return typeof this.customRequest === 'function'
                ? this.customRequest
                : this.customUploadReq;
        },
        uploadProps () {
            return {
                action: this.uploadUrl,
                data: this.otherParams || {},
                autoUpload: false,
                beforeUpload: this.onBeforeUpload,
                onSuccess: this.onUploadComplete,
                onError: this.onUploadError,
                onChange: this.onUploadChange,
                onRemove: this.onFileRemove,
                fileList: this.fileList,
                httpRequest: this.httpRequestFunc
            };
        }
    },
    methods: {
        customUploadReq (options) {
            // console.log('customUploadReq');
            const _this = this;
            const { file, filename } = options;
            const formData = new FormData();
            formData.append(filename, file, file.name);
            if (this.otherParams) {
                Object.entries(this.otherParams).forEach(([key, value]) => {
                    formData.append(key, value);
                });
            }
            const source = Axios.CancelToken.source();

            const req = new Promise(async (resolve, reject) => {
                try {
                    const uploadUrl = this.uploadUrl.replace(/^\/api/, '');
                    const response = await pureAxios({
                        url: uploadUrl,
                        data: formData,
                        responseType: 'blob',
                        cancelToken: source.token,
                        method: 'POST',
                        onUploadProgress: progressEvent => {
                            if (progressEvent.total > 0) {
                                progressEvent.percent =
                                    (progressEvent.loaded /
                                        progressEvent.total) *
                                    100;
                            }
                            typeof options.onProgress === 'function' &&
                                options.onProgress(progressEvent);
                        }
                    })
                        .then(res => res)
                        .catch(error => Promise.reject(error));

                    const data = response.data;
                    const fs = new FileReader();
                    fs.onload = function () {
                        try {
                            const res = JSON.parse(this.result);
                            resolve(res, file, this.fileList);
                        } catch {
                            if (data instanceof Blob && data.size > 0) {
                                _this
                                    .$confirm(
                                        '导入失败，是否下载查看错误数据',
                                        '温馨提示',
                                        {
                                            type: 'warning'
                                        }
                                    )
                                    .then(() => {
                                        _this.$utils.blobDownload(response);
                                    });
                                _this.$nextTick(() => {
                                    _this.$emit('update');
                                });
                            } else if (
                                data instanceof Blob &&
                                data.size === 0
                            ) {
                                resolve(
                                    {
                                        OpCode: 0
                                    },
                                    file,
                                    this.fileList
                                );
                            }
                        }
                    };
                    fs.onerror = err => {
                        reject(err);
                    };
                    fs.readAsText(new Blob([data]), 'utf-8');
                } catch (error) {
                    console.log('customUploadReq Caught Error', error);
                    reject(error);
                }
            });

            req.abort = function () {
                typeof source.cancel === 'function' &&
                    source.cancel('Upload canceled');
            };

            return req;
        },
        getTemplateName (path) {
            let lastIdx = path.lastIndexOf('/');
            lastIdx = lastIdx > -1 ? lastIdx : path.lastIndexOf('\\');
            if (lastIdx < 0) {
                return path;
            }
            return path.substring(lastIdx + 1);
        },
        downloadTemplate () {
            if (!this.templatePath) {
                this.$alert('文件不存在');
                return;
            }
            console.log(
                '%c 模板下载',
                'color:#4198f0;font-size:16px;',
                this.templatePath
            );
            // let a = document.createElement('a');
            // a.href = this.templatePath;
            // a.download =
            //     this.templateName || this.getTemplateName(this.templatePath);
            // a.click();
            // a.remove();
            // a = null;
            const fileNameGen = () =>
                this.templateName || this.getTemplateName(this.templatePath);
            this.$utils.blobDownload(this.templatePath, fileNameGen);
        },
        upload () {
            if (this.fileList.every(({ status }) => status === 'success')) {
                this.$message.info('导入完成');
                this.handleClose();
            } else {
                this.uploaderInstance.submit();
            }
        },
        handleClose () {
            this.$emit('closeDialog');
            if (this.fileList.some(({ status }) => status === 'success')) {
                this.$emit('update');
            }
            this.fileList = [];
            this.uploaderInstance.clearFiles();
        },
        onBeforeUpload (file) {
            if (typeof this.beforeUpload === 'function') {
                return this.beforeUpload(file);
            }
            return true;
        },
        onUploadComplete (response, file, fileList) {
            const { OpCode, OpDesc, Data } = response;
            const { SuccessNum, FailNum, FailList, Msg } = Data || {};
            const index = fileList.findIndex(item => item === file);
            const isSingleFile = fileList.length < 2;
            if (
                (OpCode === 0 || OpCode === '0') &&
                (SuccessNum === undefined ||
                    SuccessNum === null ||
                    SuccessNum !== 0) &&
                !FailNum
            ) {
                this.$message.success(
                    !isSingleFile ? `文件 ${index + 1} 导入成功` : '导入成功'
                );
                this.$nextTick(() => {
                    if (fileList.every(({ status }) => status === 'success')) {
                        this.handleClose();
                    }
                });
            } else {
                if (index !== -1) {
                    this.fileList[index].status = 'ready';
                }
                let message = Msg || OpDesc || '导入失败';
                if (Array.isArray(FailList) && FailList.length) {
                    message = FailList.reduce(
                        (prev, { RowNum, ErrorMsg }) =>
                            `${prev}第${RowNum + 1}行数据有误：${ErrorMsg}\n`,
                        index !== -1 && !isSingleFile
                            ? `文件 ${index + 1}上传失败\n\n`
                            : ''
                    ).trim();
                }
                this.$message.error({
                    message,
                    customClass: 'app-file-import-error-message',
                    duration: 5000
                });
            }
        },
        onUploadError (err, file, fileList) {
            console.log(
                '%c File Import Upload Caught Error',
                'font-size:18px;color:red;font-weight:700;',
                err
            );
            this.$message.error('导入失败');
        },
        onUploadChange (file, fileList) {
            this.fileList = fileList;
        },
        onFileRemove (file, fileList) {
            this.fileList = fileList;
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.app-file-import-upload) {
    .elp-upload {
        vertical-align: middle;
    }

    .elp-upload-dragger {
        border: none;
        border-radius: 0;
        width: auto;
        height: auto;
    }
}

.app-file-import-upload-btn {
    align-self: flex-start;
    flex: 0 0 auto;
    vertical-align: middle;
}

.app-file-import-step {
    display: flex;
    flex-direction: column;
    gap: var(--spacer-large);
    padding: 0 var(--spacer-large-3) var(--spacer-medium);

    .app-file-import-step-item {
        display: flex;
        flex-direction: row;
        align-items: baseline;
        gap: var(--spacer-large);

        .app-file-import-step-item-label,
        .app-file-import-step-item-content {
            line-height: var(--font-line-height-larger-1);
        }

        .app-file-import-step-item-label {
            font-size: var(--font-size-medium);
            color: var(--color-text-primary);
            flex: 0 0 auto;
        }

        .app-file-import-step-item-content {
            display: flex;
            flex-direction: column;
            flex: 1 1 auto;

            :deep(.app-file-import-step-item-content-text) {

                line-height: var(--font-line-height-primary);

                &:nth-child(1) {
                    line-height: var(--font-line-height-larger-1);
                }

                &.text-mark,
                .text-mark {
                    color: var(--color-text-placeholder);
                }

                &.text-regular,
                .text-regular {
                    color: var(--color-text-regular);
                }

                &.text-required,
                .text-required {
                    &::before {
                        content: '*';
                        display: inline-block;
                        padding-right: var(--spacer-small);
                        color: var(--color-danger);
                    }
                }

            }
        }
    }
}
</style>

<style lang="scss">
.app-file-import-error-message {
    .elp-message__content {
        white-space: pre-wrap;
        max-height: 50vh;
        overflow: auto;
    }
}

.app-file-import-custom {
    .elp-dialog__body {
        min-height: 150px;
    }
}
</style>
