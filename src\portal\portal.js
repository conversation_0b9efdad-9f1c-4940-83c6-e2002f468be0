import _, { cloneDeep } from 'lodash-es';

import App from './portal.vue';

import ElementPlus, { useGlobalConfig, ElMessage } from 'element-plus';

import EcpUIPlus, { ElementDefaultConfig, MicroUtils, useHistoryApp, useTheme, useConfig } from '@ecp/ecp-ui-plus';

import EcpChart from 'ecp-chart/v3';
import 'ecp-chart/lib/v3/es/style.css';

import CommonPart from '@common';

import router from '@router';

import store from '@store';
import useGlobalStore from '@store/global-config';
import usePermissionStore from '@store/permission';

import '@styles/index';

import * as InitialUtils from '@common/utils/initial-utils';

import { MENU_PROPS } from '@constants/menu-config';
import Menu from '@constants/mock/menu-config';

window._ = _;

// 必须在调用 ElementPlus 指令类组件（如 ElMessage）之前配置
const ElGlobalConfig = useGlobalConfig();
ElGlobalConfig.value = ElementDefaultConfig;

// 主题初始化渲染
useTheme();

useConfig({
    pagination: {
        layout: 'prev, pager, next, sizes, jumper'
    }
});

// 主应用菜单配置关键参数
const appConfig = {
    startsWith: '/s-',
    menuProps: cloneDeep(MENU_PROPS),
    menu: []
};

async function getMenu (config) {
    try {
        const permissionStore = usePermissionStore();
        const navMenu = permissionStore.navMenu || [];

        // config.menu = Menu.menu; // 使用 mock 数据
        config.menu = navMenu; // 使用接口数据

        // 初始化应用
        const locations = window.location;

        let href = locations.pathname + locations.hash;
        const rootPath = '/asset-manage-web/';

        // 获取第一个页面路径
        const isGetFirstMenu = href === `${rootPath}` || href === `${rootPath}#/` || href === '/' || href === '/#/';

        // 菜单格式化
        config.menu = InitialUtils.formatMenu({
            menu: config.menu,
            menuProps: config.menuProps,
            symbol: config.startsWith
        });

        // 没有路径时，使用配置的默认路径
        if (isGetFirstMenu) {
            href = MicroUtils.getFirstMenuRoute(config.menu, config.menuProps);
            if (href) {
                window.location.replace(href);
            }
        }

        // 将第一个路径/默认路径加到配置中
        appConfig.defaultPath = href;

        return {
            ...config
        };
    } catch (error) {
        ElMessage.error(error);
        console.log(
            '%c getMenu Caught error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    }
}

const initApp = async (container) => {
    try {
        // 如果还有其它渲染前置处理，请在 src、common、utils、initial-utils、index.js 的 systemInitial 里面添加
        await InitialUtils.systemInitial({
            // loadingTarget: (container || document).querySelector('#container')
            appConfig
        });

        await getMenu(appConfig);

        const globalStore = useGlobalStore();
        const globalConfigs = globalStore?.globalConfigs || {};

        appConfig.IMPORT_CONFIGS = globalConfigs?.IMPORT_CONFIGS;
        document.title = globalConfigs.IMPORT_CONFIGS.title;
    } catch (error) {
        console.log(
            '%c systemInitial Caught Error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    }
    return Promise.resolve();
};

let microApp = null;

const renderApp = async (props = {}) => {
    try {
        const VueApp = createApp(App, {
            ...props,
            menu: props?.config?.menu,
            menuProps: props?.config?.menuProps
        });
        VueApp.use(store);

        // 需要在 use(router) 之前调用，否则会先展示 404 页面
        await initApp(props.componentName && props.container);

        VueApp.use(router);
        VueApp.use(ElementPlus);
        VueApp.use(EcpUIPlus);
        VueApp.use(EcpChart);
        VueApp.use(CommonPart);

        // Vue3 在 createApp 之后、mount 之前，只能通过这样修改传入的属性值
        // VueApp._context.app._props.menuProps = appConfig.menuProps; // 一般不需要再修改菜单项关键字映射配置
        VueApp._context.app._props.menu = appConfig.menu;

        // 路由式加载初始化
        const historyApp = useHistoryApp({
            initOptions: {
                portalName: PACKAGE_NAME,
                portalContainer: props.container
                    ? props.container.querySelector('#container')
                    : '#container',
                subContainer: '#sub-wrapper',
                app: VueApp,
                symbol: props?.config?.startsWith,
                transitionName: 'fade-transform',
                plugins: [
                    {
                        cssBeforeLoaders: [
                            {
                                content: '#app .ecp-menu { display: none; }'
                            }
                        ]
                    }
                ],
                polyfill: {
                    // 未做适配改造的公服
                    'common-frontend': {
                        raw: true, // 未做生命周期改造的子应用
                        useUniPlugin: true // 使用了 UniPlugin 的 external，需要将 useUniPlugin 设为 true
                    }
                }
            },
            keepAlive: true // 是否缓存页面，默认不缓存
        });

        // 路由式加载实例
        microApp = historyApp.microApp;

        // 启动路由式加载
        const startInst = await microApp.start({
            firstRoute: props?.config?.defaultPath || ''
        });

        // 跳转第一个页面
        if (startInst?.replaceUri) {
            // 获取初始化需要跳转的链接
            const replaceUri = startInst.replaceUri;

            // 先替换地址栏链接
            requestAnimationFrame(() => {
                window.history.replaceState({}, '', replaceUri);
            });

            // 第一个页面是当前应用的页面时，用 router 跳转
            if (replaceUri.match(PACKAGE_NAME)) {
                const fullPath = MicroUtils.getAppFullPath(replaceUri);
                const matchedRoute = router.resolve(fullPath);
                const matchedRouteQuery = matchedRoute?.query || {};
                router.replace({
                    path: matchedRoute.path,
                    query: matchedRouteQuery,
                    force: true
                });
            }
        }

        return VueApp;
    } catch (error) {
        console.log(
            '%c renderApp Caught error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
        return null;
    }
};
(async function () {
    renderApp({
        config: appConfig
    });
})();
