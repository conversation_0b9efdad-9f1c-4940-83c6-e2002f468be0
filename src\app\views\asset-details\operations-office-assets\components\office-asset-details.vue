<template>
    <ecp-layout-pagination content-scroll :total="tableData.total" v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize" layout="prev, pager, next, sizes, jumper" :top-pagination="false"
        @query="getOfficeAssetDetailsTable()">
        <!-- <template #head>
        </template> -->
        <template #content>
            <!--<ecp-layout-pagination :total="total" v-model:current-page="pagination.pageNum"
                v-model:page-size="pagination.pageSize" layout="prev, pager, next, sizes, jumper"
                @current-change="SearchEvent">
                <template #head> -->
            <div class="header-toolbar">
                <el-form inline v-model="searchForm" ref="formRef">
                    <el-form-item label="资产大类" prop="assetBig">
                        <el-select v-model="searchForm.assetBig" @change="getDimAsset
                            (2)">
                            <el-option v-for="item in assetCategoriesList" :key="item.code" :label="item.name"
                                :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="资产中类" prop="assetMiddle">
                        <el-select v-model="searchForm.assetMiddle" @change="getDimAsset(3)">
                            <el-option v-for="item in assetClassList" :key="item.code" :label="item.name"
                                :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="资产小类" prop="assetSmall">
                        <el-select v-model="searchForm.assetSmall">
                            <el-option v-for="item in assetSubclassList" :key="item.code" :label="item.name"
                                :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="资产状态" prop="assetState">
                        <el-select v-model="searchForm.assetState">
                            <el-option v-for="item in assetStatusList" :key="item.code" :label="item.name"
                                :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item label="使用单位" prop="unitsUsed">
                                <el-select v-model="searchForm.unitsUsed"></el-select>
                            </el-form-item> -->
                    <el-form-item label="" prop="query">
                        <el-input v-model="searchForm.query" placeholder="请输入资产编码或名称、使用单位或使用部门" style="width: 320px;" />
                    </el-form-item>
                </el-form>
                <div class="button-group">
                    <ecp-button type="primary" text="查询" @click="SearchEvent" />
                    <ecp-button text="重置" @click="ResetEvent" />
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading.export" @click="ExportEvent" />
                </div>
            </div>
            <!-- </template>
<template #content> -->
            <app-dynamic-table :loading="loading.table" :table-data="tableData.list"
                :table-config="OFFICE_ASSET_DETIAL_TABLE" style="height: 100%; margin-bottom: 6rem;" />
        </template>
        <!-- </ecp-layout-pagination>
</template> -->
    </ecp-layout-pagination>
</template>

<script setup>
import { OFFICE_ASSET_DETIAL_TABLE } from './_constants/index';
import * as Api from '@api/index';
import { OperatingAsset } from '@api/asset-panorama/operating-asset';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
const total = ref(0);
const loading = reactive({
    table: false,
    export: false
});
const tableData = reactive({
    total: 0,
    list: []
});
const searchForm = reactive({
    assetBig: '', // 资产大类
    assetMiddle: '', // 资产中类
    assetSmall: '', // 资产小类
    udLineCode: '', // 所属路线
    // operateUnit: '', // 使用单位
    query: '', // 资产编码、名称、使用单位或使用部门
    assetState: '' // 资产状态

});
const pagination = reactive({
    pageSize: 10,
    pageNum: 1
});

const SearchEvent = () => {
    getOfficeAssetDetailsTable();
};
// 获取办公资产明细 S
const getOfficeAssetDetailsTable = async () => {
    try {
        loading.table = true;
        const response = await Api.AssetDetails.getOfficeAssetDetails({
            ...pagination,
            ...searchForm
        });
        const { Total, Data } = response;
        tableData.list = Data;
        tableData.total = Total;
    } catch (error) {
        console.log(error);
    } finally {
        loading.table = false;
    }
};
// 获取办公运营资产明细 E
// 获取所属线路 S
const lineDescOptions = ref([]);
const getLineDesc = async () => {
    try {
        const { Data } = await Api.AssetDetails.getLineDesc();
        lineDescOptions.value = Data;
    } catch (error) {
    }
};
// 获取所属线路 E
// 获取办公资产明细-资产大、中和小类 S
const assetCategoriesList = ref([]);// 大类
const assetClassList = ref([]);// 中类
const assetSubclassList = ref([]);// 小类
const getDimAsset = async (item) => {
    try {
        if (item === 1) { // 获取资产大类
            const { Data } = await Api.AssetDetails.getDimAsset();
            for (const [key, value] of Object.entries(Data)) {
                assetCategoriesList.value.push({
                    code: key,
                    name: value
                });
            }
            // assetCategoriesList.value = Data;
        } else if (item === 2) { // 获取资产中类
            const { Data } = await Api.AssetDetails.getDimAsset({ assetLevelOrCode: searchForm.assetBig });
            for (const [key, value] of Object.entries(Data)) {
                assetClassList.value.push({
                    code: key,
                    name: value
                });
            }
            // assetClassList.value = Data;
            searchForm.assetMiddle = null;
            searchForm.assetSmall = null;
        } else if (item === 3) { // 获取资产小类
            const { Data } = await Api.AssetDetails.getDimAsset({ assetLevelOrCode: searchForm.assetMiddle });
            for (const [key, value] of Object.entries(Data)) {
                assetSubclassList.value.push({
                    code: key,
                    name: value
                });
            }
            // assetSubclassList.value = Data;
            searchForm.assetSmall = null;
        }
    } catch (error) {
    }
};
// 获取办公资产明细-资产大、中和小类 E
// // 获取使用单位信息 S
// const getOperateUnitData = async () => {
//     try {
//         const { Data } = await Api.AssetDetails.getOperateUnit();
//         operateUnitOptions.value = Data.map(company => ({
//             data: company
//         }));
//     } catch (error) {
//     }
// };
// const operateUnitOptions = ref([]);
// // 获取使用单位信息 E

// 获取资产状态列表 S
const assetStatusList = ref([]);
const getAssetStatusList = async () => {
    try {
        const { Data } = await OperatingAsset.getAssetStatusList();
        if (Data && Data.length) {
            assetStatusList.value = Data.map(item => ({
                code: item.AssetStatusCode,
                name: item.AssetStatusName
            }));
        }
    } catch (error) {
        console.log(error);
    }
};
// 获取资产状态列表 E

// 导出办公资产明细表格数据（当前页当前条数）
const ExportEvent = async () => {
    try {
        // console.log(tableData.list, 'tableData.list');
        if (tableData.list.length === 0) {
            ElMessage.warning('暂无数据，不可导出');
            return;
        }
        const response = await Api.AssetDetails.exportOfficeAssetDetails({
            pageSize: 10000,
            pageNum: 1,
            ...searchForm
        });
        downloadBlobData(response);
        ElMessage.success('导出成功');
    } catch (error) {
        console.log(error);
    }
};
// 重置 S
const ResetEvent = () => {
    searchForm.assetBig = null; // 资产大类
    searchForm.assetMiddle = null; // 资产中类
    searchForm.assetSmall = null; // 资产小类
    searchForm.assetState = null; // 资产状态
    searchForm.query = null;
    assetClassList.value = [];
    assetSubclassList.value = [];
    getOfficeAssetDetailsTable();
};
// 重置 E
onMounted(() => {
    getOfficeAssetDetailsTable();
    getLineDesc();
    getDimAsset(1);
    getAssetStatusList();
});
</script>

<style scoped lang="scss">
.header-toolbar {
    display: flex;

    .button-group {
        display: flex;
        flex-grow: 1;
        justify-content: flex-end;
    }
}
</style>
