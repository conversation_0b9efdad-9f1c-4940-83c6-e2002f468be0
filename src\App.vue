<template>
    <el-config-provider v-bind="ElementDefaultConfig">
        <div class="app" ref="appRef">
            <template v-if="!componentName">
                <app-layout :name="title" :menu="menu" v-if="showNav" @select="goto">
                    <template #content>
                        <router-view v-slot="{ Component }">
                            <transition name="fade-transform" mode="out-in">
                                <keep-alive :include="cachedViews">
                                    <component :is="Component" :key="$route.fullPath" />
                                </keep-alive>
                            </transition>
                        </router-view>
                    </template>
                </app-layout>
                <template v-else>
                    <router-view v-slot="{ Component }">
                        <transition name="fade-transform" mode="out-in">
                            <keep-alive :include="cachedViews">
                                <component :is="Component" :key="$route.fullPath" />
                            </keep-alive>
                        </transition>
                    </router-view>
                </template>
            </template>
            <template v-else>
                <transition name="fade-transform" mode="out-in">
                    <ecp-micro-component is-route :url="componentName" :props="componentProps" v-if="isRoute" />
                    <template v-else>
                        <component :is="componentName" v-bind="componentProps" />
                    </template>
                </transition>
            </template>
        </div>
    </el-config-provider>
</template>

<script setup>
import { useTheme, ElementDefaultConfig, MicroUtils } from '@ecp/ecp-ui-plus';

import { MENU_PROPS } from '@constants/menu-config';

import usePermissionStore from '@store/permission';

import useGlobalStore from '@store/global-config';

const globalStore = useGlobalStore();
const title = computed(() => {
    return globalStore?.globalConfigs?.IMPORT_CONFIGS?.title || PACKAGE_NAME;
});

const { isDark, theme, setTheme } = useTheme();

const switchTheme = () => {
    setTheme(theme.value === 'default' ? 'dark' : 'default');
};

const props = defineProps({
    config: {
        type: Object
    },
    componentName: {
        type: String,
        default: ''
    },
    componentProps: {
        type: Object,
        default: () => ({})
    },
    menuProps: {
        type: Object,
        default: () => MENU_PROPS
    }
});

const permissionStore = usePermissionStore();
const router = useRouter();

const menu = computed(() => {
    return permissionStore.navMenu;
});
// 获取缓存页面组件 name
const cachedViews = computed(() => (permissionStore.cachedViews || []).map((view) => view.name));

const isRoute = computed(() => typeof props?.componentName === 'string' && !!props.componentName.match('/'));

const isSubMode = window.__POWERED_BY_QIANKUN__ || window.__POWERED_BY_WUJIE__;
const showNav = computed(() => {
    return !isSubMode;
});

let opener = null;

const goto = (data) => {
    let href = '';
    if (data.type === 'iframe') {
        // 拼接 iframe 承载页路径
        href = MicroUtils.getIframePath(
            data,
            props.menuProps,
            PACKAGE_NAME
        );
    } else if (data.type === 'open') {
        // 新窗口打开

        if (opener && !opener.closed) {
            opener.close();
        }
        opener = window.open(data[props.menuProps.url]);
        return;
    } else if (data.type === 'reload') {
        // 刷掉当前页面
        if (window.__POWERED_BY_WUJIE__) {
            // vite 子应用所有的 location 操作都必须采用 window.$wujie.location
            window.$wujie.location.href = data[props.menuProps.url];
        } else {
            window.location.href = data[props.menuProps.url];
        }
        return;
    } else {
        href = data[props.menuProps.route];
    }
    if (window.__POWERED_BY_QIANKUN__) {
        window.history.pushState({}, '', href);
    } else {
        const matchedRoute = router.resolve(href.replace(/.*#/, ''));
        const matchedRouteQuery = matchedRoute?.query || {};
        router.push({
            path: matchedRoute.path,
            query: matchedRouteQuery
        });
    }
};

const appRef = ref(null);
provide('$app', {
    getRootEl: () => appRef.value
});
</script>

<style lang="scss" scoped>
.app {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}
</style>
