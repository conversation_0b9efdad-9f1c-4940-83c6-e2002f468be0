<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder class="app-form-page">
        <template #head-right>
            <el-select class="mr-2" v-model="assetUnit" placeholder="请选择" @change="handleUnitQueryOptionChange"
                style="width: 150px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template>
        <template #content>
            <div class="asset-analysis-layout">
                <div class="row">
                    <!-- 资产使用情况 -->
                    <AssetUsagePieChart class="box usage-status" :asset-unit="assetUnit"
                        :is-initial-load="isInitialLoad" />
                    <!-- 资产数量vs员工数量分析 -->
                    <AssetEmployeBarLineChart class="box status-vs-traffic"></AssetEmployeBarLineChart>
                    <!-- 领用/借用到期分析 -->
                    <ExpireReturnedBarLineChart class="box category-status"></ExpireReturnedBarLineChart>
                </div>
                <div class="row">
                    <!-- 资产类型结构分布 -->
                    <app-business-panel mode="card" v-loading="loading1" class="box category-distribution"
                        title="资产类型结构分布">
                        <AssetTypeStructureDistribution v-if="chartData && chartData.length > 0" :key="chartData.length"
                            :chartData="chartData" />
                        <ecp-empty v-else description="暂无数据"></ecp-empty>
                    </app-business-panel>
                    <!-- 资产列表 -->
                    <app-business-panel mode="card" class="box asset-list" v-loading="loading2">
                        <AssetsList :tableData="tableData" :pagination="pagination"
                            @update-pagination="updatePagination" @export-offAssetInfo="exportOffAssetInfo">
                        </AssetsList>
                    </app-business-panel>
                </div>
            </div>
        </template>
    </app-form-page>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import dayjs from 'dayjs';
import AssetsList from './components/assets-list.vue';
import AssetTypeStructureDistribution from './components/asset-type-structure-distribution.vue';
import AssetUsagePieChart from './components/asset-usage-pie-chart.vue';
import AssetEmployeBarLineChart from './components/asset-employe-bar-line-chart.vue';
import ExpireReturnedBarLineChart from './components/expire-returned-bar-line-chart.vue';
import { OfficeAssetAnalysis } from '@api/asset-analysis/office-asset-analysis';
import { ElMessage } from 'element-plus';
import { downloadBlobData } from '@utils/download';
import {STACK_COLOR_LIAT} from '@constants/enum-config';

const name = 'asset-analysis-efficiency-analysis';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '办公资产分析'
        }
    ];
});

const isInitialLoad = ref(true);

const fetchAllChartData = () => {
    fetchAssetTypeStructureData();
    queryTableList();
};

onMounted(async () => {
    await fetchAssetUnitData();
    fetchAllChartData();
});

const assetUnit = ref('');
const options = ref([]);

const handleUnitQueryOptionChange = async (selectedValue) => {
    assetUnit.value = selectedValue;
    fetchAllChartData();
};

const fetchAssetUnitData = async () => {
    try {
        const data = await OfficeAssetAnalysis.getOfficeAssetUnitQueryData();
        options.value = data.Data.map(item => ({
            value: item.DataCode,
            label: item.DataName
        }));
        if (options.value.length > 0) {
            assetUnit.value = options.value[0].value;
        }
        isInitialLoad.value = false;
    } catch (error) {
        console.error('获取数据失败', error);
    }
};

const loading1 = ref(false);
const loading2 = ref(false);

const chartData = ref([]);

// 资产类型结构分布
const fetchAssetTypeStructureData = async () => {
    loading1.value = true;
    try {
        const params = { assetUnit: assetUnit.value };
        const response = await OfficeAssetAnalysis.getAssetTypeStructureData(params);
        if (Array.isArray(response.Data) && response.Data.length > 0) {
            chartData.value = response.Data.map((item, index) => ({
                name: item.DataName,
                value: item.Percentage,
                color: getColor(index)
            }));
        } else {
            chartData.value = [];
        }
        await nextTick();
    } catch (error) {
        console.error('获取资产类型结构分布数据失败', error);
        chartData.value = [];
    } finally {
        loading1.value = false;
    }
};

const getColor = (index) => {
    return STACK_COLOR_LIAT[index % STACK_COLOR_LIAT.length];
};

// 资产列表
const tableData = reactive({
    total: 0,
    list: []
});

const pagination = reactive({
    pageNo: 1,
    pageSize: 10
});

const queryTableList = async () => {
    loading2.value = true;
    const params = {
        assetUnits: [assetUnit.value],
        pageNum: pagination.pageNo.toString(),
        pageSize: pagination.pageSize.toString()
    };
    try {
        const response = await OfficeAssetAnalysis.getOffAssetInfo(params);
        tableData.list = response?.Data || [];
        tableData.total = response?.Total || 0;
    } catch (error) {
        console.error('获取资产信息失败', error);
    } finally {
        loading2.value = false;
    }
};

// 分页
const updatePagination = (newPageNo, newPageSize) => {
    pagination.pageNo = newPageNo;
    pagination.pageSize = newPageSize;
    queryTableList();
};

// 导出资产列表
const exportOffAssetInfo = async () => {
    const params = {
        assetUnits: [assetUnit.value],
        pageNum: pagination.pageNo.toString(),
        // pageSize: pagination.pageSize.toString()
        pageSize: '10000'
    };
    try {
        const response = await OfficeAssetAnalysis.exportOffAssetInfo(params);
        downloadBlobData(response);
        ElMessage.success('导出成功！');
    } catch (error) {
        ElMessage.error('导出失败，请重试！');
    }
};

</script>

<style lang="scss" scoped>
$page-name: asset-analysis-efficiency-analysis;

.#{$page-name} {
    .asset-analysis-layout {
        height: 100vh;
        width: 100%;

        display: grid;
        grid-template-columns: 1fr 1.25fr 1.25fr;
        grid-template-rows: 1fr 1.25fr;
        gap: 16px;

        .row {
            display: contents;

            .box {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                overflow: auto;
                /* 保证内容上下对齐 */

                // 第一行
                &.usage-status {
                    grid-column: 1;
                }

                &.status-vs-traffic {
                    grid-column: 2;
                }

                &.category-status {
                    grid-column: 3;
                }

                // 第二行
                &.category-distribution {
                    grid-column: 1;

                    :deep(.app-business-panel-content) {
                        overflow: hidden;
                    }
                }

                &.asset-list {
                    grid-column: 2 / span 2;
                }

                /* 确保模块高度相同 */
                min-height: 300px;
                min-width: 360px
            }
        }
    }
}
</style>
