import Cookies from 'js-cookie';
import SystemService from '@api/system-service';
import { LoginApi } from '@api/login';

const pureAxios = axios.create();

const useUserStore = defineStore('user-store', {
    state: () => ({
        userInfo: {},
        token: ''
    }),
    actions: {
        async getUserInfo () {
            try {
                if (Reflect.ownKeys(this.userInfo).length) return;
                const result = await SystemService.getUserInfo();
                this.userInfo = result;
            } catch (error) {
                console.log(
                    '%c [user-store] getUserInfo Caught Error',
                    'font-size:18px;color:red;font-weight:700;',
                    error
                );
                return Promise.reject(error);
            }
            return this.userInfo || {};
        },

        /**
         * @method loginWithUserCode 通过UserCode登录
         */
        async loginWithUserCode (userCode) {
            try {
                if (!userCode) {
                    throw new Error('userCode is Required!');
                }
                await LoginApi.logout(true);
                const token = await LoginApi.tokenLogin({
                    userCode
                });

                Cookies.set('x_auth_token', token);
                this.token = token;
                return Promise.resolve(token);
            } catch (error) {
                return Promise.reject(error);
            }
        }
    }
});

export default useUserStore;
