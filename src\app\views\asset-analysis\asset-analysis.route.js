import {
    InventoryAnalysis,
    OfficeAssetAnalysis,
    LineAssetAnalysis,
    StatisticalReport
} from './index';

export const AssetAnalysisRoutes = () => {
    const context = 'asset-analysis';
    return [
        {
            path: `/${context}/inventory-analysis`,
            component: InventoryAnalysis,
            label: '盘点分析',
            name: `${context}-inventory-analysis`
        },
        {
            path: `/${context}/line-asset-analysis`,
            component: LineAssetAnalysis,
            label: '线路资产分析',
            name: `${context}-line-asset-analysis`
        },
        {
            path: `/${context}/office-asset-analysis`,
            component: OfficeAssetAnalysis,
            label: '办公资产分析',
            name: `${context}-office-asset-analysis`
        },
        {
            path: `/${context}/statistical-report`,
            component: StatisticalReport,
            label: '统计报表',
            name: `${context}-statistical-report`
        }
    ];
};
