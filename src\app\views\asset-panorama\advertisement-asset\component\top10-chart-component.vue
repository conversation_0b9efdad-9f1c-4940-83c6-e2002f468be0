<template>
    <div class="chart-container">
        <ecp-chart-base-bar v-if="chartData?.source?.length" theme="whiteTheme" :data="chartData"
            :option="chartData.option" :legend="chartData.legend" :yName="unit" horizontal :unit="unit" />
        <ecp-empty v-else />
    </div>
</template>

<script setup>
import { watch, onMounted } from 'vue';
import { CHART_COLOR_LIST } from '@constants/enum-config';
import { formatXAxis } from '@utils/format';

onMounted(() => {
    console.log(props.data);
});

const props = defineProps({
    data: {
        type: Object,
        default: () => ({
            dimensions: [],
            source: []
        })
    },
    unit: {
        type: String,
        required: true
    }
});

const chartData = reactive({
    dimensions: props.data.dimensions,
    source: props.data.source,
    legend: {
        show: false
    },
    option: {
        color: CHART_COLOR_LIST,
        xAxis: {
            nameTextStyle: {
                color: 'rgba(0,0,0,0.45)'
            },
            axisLabel: {
                textStyle: {
                    color: 'rgba(0,0,0,0.45)',
                    fontFamily: 'D-DIN'
                }
            }
        },
        yAxis: {
            axisLabel: {
                rotate: 20,
                formatter: (value) => formatXAxis(value,8),
            }
        },
        grid: {
            top: 20,
            bottom: 0,
            left: 0,
            right: props.unit?.length > 2 ? 50 : 30
        },

        series: [
            {
                type: 'bar',
                itemStyle: {
                    borderRadius: [10, 10, 10, 10]
                },
                barWidth: '15'
            }
        ]

    }
});

watch(
    () => props.data,
    (newData) => {
        chartData.dimensions = newData.dimensions;
        chartData.source = newData.source;
        chartData.option.grid.right = props.unit?.length > 2 ? 50 : 30;
    },
    { deep: true }
);

</script>

<style scoped lang="scss">
.chart-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
</style>
