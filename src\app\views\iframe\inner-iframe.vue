<template>
    <div class="inner-iframe" :class="{ error: loadError }" v-loading="loading" ref="iframeInstance">
        <app-status class="inner-iframe-error" title="暂未开放，敬请期待~" :subtitle="title" v-if="loadError" />
    </div>
</template>

<script>
import useGlobalStore from '@store/global-config';
export default {
    name: 'inner-iframe',
    components: {},
    setup () {
        const { globalConfigs } = useGlobalStore();
        return {
            title: globalConfigs?.IMPORT_CONFIGS?.title
        };
    },
    data () {
        return {
            loading: false,
            loadError: false
        };
    },
    computed: {
        iframeInstance () {
            return this.$refs.iframeInstance;
        }
    },
    mounted () {
        this.setUrl();
    },
    methods: {
        setUrl () {
            let url = this.$route.params.url;
            this.$nextTick(() => {
                url = decodeURIComponent(url);
                if (url.match('%2F')) {
                    url = decodeURIComponent(url);
                }
                console.log('%c url', 'font-size:18px;color:purple;font-weight:700;', url);
                this.initIframe(url);
            });
        },
        async initIframe (src) {
            try {
                const iframeEle = this.$el.querySelector('iframe');
                if (iframeEle) {
                    this.$el.removeChild(iframeEle);
                }
                this.loading = true;
                const iframe = document.createElement('iframe');
                iframe.src = src;

                if (iframe.attachEvent) {
                    iframe.attachEvent('onload', () => {
                        // let iframeTitle = iframe?.contentDocument?.title;
                        // if (iframeTitle && iframeTitle.match('404|Error')) {
                        //     this.loadError = true;
                        //     this.$el.removeChild(iframe);
                        // }
                        this.loading = false;
                    });
                    iframe.attachEvent('onerror', error => {
                        // let iframeTitle = iframe?.contentDocument?.title;
                        // if (iframeTitle && iframeTitle.match('404|Error')) {
                        //     this.loadError = true;
                        //     this.$el.removeChild(iframe);
                        // }
                        throw error;
                    });
                } else {
                    iframe.onload = () => {
                        // let iframeTitle = iframe?.contentDocument?.title;
                        // if (iframeTitle && iframeTitle.match('404|Error')) {
                        //     this.loadError = true;
                        //     this.$el.removeChild(iframe);
                        // }
                        this.loading = false;
                    };
                    iframe.onerror = (error) => {
                        throw error;
                    };
                }
                this.$el.appendChild(iframe);
            } catch (err) {
                this.loading = false;
                this.loadError = true;
                console.log('访问失败', err);
            }
        }
    }
};
</script>

<style scoped lang="scss">
.inner-iframe {
    width: 100%;
    height: 100%;
    overflow: hidden;

    :deep(iframe) {
        width: 100%;
        height: 100%;
    }

    &.error {
        display: flex;
        padding: var(--spacer-large);

        .inner-iframe-error {
            width: 100%;
            height: 100%;
            border-radius: 4px;
            flex: 1 1 auto;
            align-self: center;
            padding-bottom: 200px;
            background: var(--color-white);
        }
    }
}
</style>
