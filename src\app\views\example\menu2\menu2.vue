<template>
    <app-form-page class="Menu2" title="Menu 2">
        <app-business-panel title="Menu 2 Content" mode="card">
            <ecp-icon icon="#ecp-icon-user"></ecp-icon><br />
            <router-link to="/Menu1">TO Menu1</router-link>
            <el-form>
                <el-form-item label="名称：">
                    <el-input v-model="form.name" placeholder="请输入名称" />
                </el-form-item>
            </el-form>
            <ecp-button text="手动加载本应用页面" @click="loadLocalPage()" />
            <ecp-button text="手动加载微应用页面" @click="showTargetMicroApp()" />

            <el-dialog class="menu2-dialog" :title="dialog.title" v-model="dialogVisible" width="80%" append-to-body
                modal-append-to-body :close-on-click-modal="false" :before-close="handleClose"
                @closed="dialogAvailable = false" v-if="dialogAvailable">
                <ecp-micro-component show-loading :is-route="isRoute" :url="dialog.path" :props="dialog.props"
                    v-bind="dialog.attrs" @micro-component-emitter="onMicroComponentEmitter" @confirm="handleConfirm"
                    ref="microAppComponent" :key="dialog.path" v-if="dialogVisible">
                    <template #header>
                        <div class="text-slot pa-1 pl-4 pr-4 mt-4 amber white--text">Slot Header: {{ version }}</div>
                    </template>
                    <div class="text-slot pa-1 pl-4 pr-4 mt-2 mb-4 amber darken-3 white--text">Slot Default: {{ version
                        }}
                    </div>
                </ecp-micro-component>
            </el-dialog>
        </app-business-panel>
    </app-form-page>
</template>

<script>
import dayjs from 'dayjs';

import { EcpMicroComponent } from '@ecp/ecp-ui-plus';

export default {
    name: 'Menu2',
    components: {
        EcpMicroComponent
    },
    data: function () {
        return {
            dialog: {},
            dialogVisible: false,
            microAppLoading: false,

            // 全局 zIndex 更新测试
            dialogAvailable: false,

            form: {
                name: '',
                status: '',
                time: null
            }
        };
    },
    computed: {
        isRoute() {
            return !!(this?.dialog?.path || '').match(PACKAGE_NAME);
        }
    },
    watch: {
        dialogVisible(val) {
            if (val) {
                this.dialogAvailable = true;
            }
        }
    },
    async beforeRouteLeave(to, from, next) {
        if (this.dialogVisible && this.isRoute) {
            try {
                await this.handleClose();
            } catch (error) {
                console.log(
                    '%c handleClose Caught Error',
                    'font-size:18px;color:red;font-weight:700;',
                    error
                );
            }
        }
        next();
    },
    methods: {
        onMicroComponentEmitter(...args) {
            console.log('%c onMicroComponentEmitter', 'font-size:18px;color:gold;font-weight:700;', ...args);
        },
        async unmountMicroApp() {
            try {
                if (typeof this.microApp?.unmount === 'function') {
                    await this.microApp.unmount();
                }
                return Promise.resolve();
            } catch (error) {
                return Promise.reject(error);
            }
        },
        loadLocalPage() {
            this.dialog = {
                title: 'Load Menu3 As Route-Component',
                path: `/asset-manage-web#/menu3?loadAs=Route-Component&detailInfo=${JSON.stringify({
                    name: this.name
                })}`,
                props: {
                    detailInfo: this.form
                }
            };
            this.version = dayjs().format('YYYY-MM-DD HH:mm:ss');
            this.$nextTick(() => {
                this.dialogVisible = true;
            });
        },
        showTargetMicroApp() {
            const _this = this;
            const item = {
                Text: '菜单管理',
                Target: '/common-frontend/sub.html#/SystemConfig/MenuManage'
            };
            this.dialog = {
                title: item.Text,
                path: item.Target,
                props: {},
                attrs: {
                    plugins: [{
                        // 在加载其它所有css之前注入，优先级固定 !important
                        cssBeforeLoaders: [{ content: _this.getInjectCss() }]
                    }],
                    polyfill: {
                        raw: true,
                        useUniPlugin: true
                    }
                }
            };
            this.$nextTick(() => {
                this.dialogVisible = true;
            });
        },
        getInjectCss() {
            return `
                html,
                body,
                #app {
                    min-width: 100% !important;
                }
                
                html {
                    height: 500px !important;
                }
            `;
        },
        async handleClose() {
            this.dialogVisible = false;
        },
        handleConfirm(type, data) {
            console.log('%c handleConfirm', 'font-size:18px;color:green;font-weight:700;', type, data);
            if (type === 'confirm') {
                this.form = data;
            }
            this.handleClose();
        }
    }
};
</script>

<style lang="scss">
.Menu2 {
    // border: 3px solid $--color-primary;
}

#sub-load-wrapper {
    width: 100%;
    height: 500px;
}

.menu2-dialog {
    .elp-dialog__body {
        min-height: 200px;
        padding: 0 !important;
        display: flex;
        flex-direction: column;
    }

    .text-slot {}
}
</style>
