<template>
    <el-dialog title="账号密码认证" v-model="dialogVisible" :close-on-click-modal="false" top="30vh" width="320px"
        append-to-body class="ecpp-login-component dialog-second-login-password"
        custom-class="ecpp-login-component dialog-second-login-password">
        <el-form ref="loginFormRef" :model="loginForm" :rules="rules">
            <el-form-item prop="username">
                <el-input type="text" v-model.trim="loginForm.username"
                    :placeholder="placeholderConfig?.username || '请输入您的账号'" autocomplete="new-password"
                    @keydown.enter="login">
                    <template #prefix>
                        <slot name="usernameIcon" v-if="$slots?.usernameIcon"></slot>
                        <el-icon v-else>
                            <User></User>
                        </el-icon>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item prop="password">
                <el-input type="password" autocomplete="new-password" v-model.trim="loginForm.password"
                    :placeholder="placeholderConfig?.password || '请输入您的密码'" @keydown.enter="login">
                    <template #prefix>
                        <slot name="passwordIcon" v-if="$slots?.passwordIcon"></slot>
                        <el-icon v-else>
                            <Lock></Lock>
                        </el-icon>
                    </template>
                </el-input>
            </el-form-item>
            <div class="btn-wrap">
            </div>
        </el-form>
        <template #footer>
            <el-button @click="handleClose">
                取 消
            </el-button>
            <el-button type="primary" @click="login" :disabled="(!loginForm.username || !loginForm.password)">
                登 录
            </el-button>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, computed, defineComponent, defineProps, provide, onMounted, getCurrentInstance, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { User, Lock } from '@element-plus/icons-vue';

import { LoginApi } from '../api';
import * as Utils from '../utils';

defineComponent({
    name: 'ecp-second-login-password'
});

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    isComplexNetwork: Boolean,

    placeholderConfig: {
        type: Object,
        default: () => ({})
    }
});

const emits = defineEmits(['update:visible', 'modify-password']);

const loginForm = reactive({
    username: '',
    password: ''
});
const rules = {
    username: { required: true, message: '用户名不能为空' },
    password: { required: true, message: '密码不能为空' }
};

const loginFormRef = ref();

const dialogVisible = computed({
    get () {
        return props.visible;
    },
    set (val) {
        emits('update:visible', val);
    }
});
const originUrl = computed(() => {
    return Utils.getOriginUrl(props.isComplexNetwork);
});
const getPasswordFromCookie = () => {
    const { username, password } = Utils.getPasswordFromCookie();
    loginForm.username = username;
    loginForm.password = password;
};
const login = async () => {
    try {
        const valid = await loginFormRef.value.validate();
        if (!valid) {
            ElMessage.warning('校验不通过，请看错误提示');
            throw new Error(valid);
        }
        // alg : 加密方式； 1：明文； 2：AES+hex； 3:待拓展；
        // loginType: null||'': 默认标准登录  2: PKI登录；[p：传ST参数，st根据身份证号获得，5s有效期]
        loginForm.alg = '2';
        const time = Date.parse(new Date());
        const username = Utils.Encrypt(
            loginForm.username,
            `PCI${time}`
        );
        const password = Utils.Encrypt(
            loginForm.password,
            `PCI${time}`
        );
        const serviceParam = Utils.getUrlParam('service');
        const service =
            serviceParam || Utils.stringsToHex(originUrl.value);
        const param = {
            u: Utils.stringsToHex(username),
            p: Utils.stringsToHex(password),
            x: Utils.stringsToHex(loginForm.username),
            service,
            time,
            alg: '2'
        };
        const isNativeClient = window?._env?.isNativeClient; // 是否托盘客户端

        const res = await LoginApi.ssoDoLogin(param, {
            headers: {
                x_op_client: isNativeClient ? 2 : 1
            }
        });
        const result = res;
        if (result.OpCode === 0) {
            const redirectUrl = Utils.getRedirectUrl(
                result.Data.redirectUrl || originUrl.value,
                props.isComplexNetwork
            );
            window.location.href = redirectUrl;
            window.localStorage &&
                window.localStorage.setItem('loginStatus', 'logined');
        } else if (result.OpCode === 1001 || result.OpCode === 1002) {
            emits('modify-password', result.OpCode);
        } else {
            ElMessage.warning(result.OpDesc || '登录失败');
        }
    } catch (error) {
        console.log(
            '%c [ECP-LOGIN] login Caught error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    }
};
const handleClose = () => {
    dialogVisible.value = false;
};

onMounted(() => {
    getPasswordFromCookie();
});
</script>
