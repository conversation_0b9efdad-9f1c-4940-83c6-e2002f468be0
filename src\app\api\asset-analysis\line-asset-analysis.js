import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

export const LineAssetAnalysis = {
    /**
     * 线路资产分析
     * @method statusAndAnalyse 第一个柱线图
     */
    getStatusAndAnalyse (params) {
        return request.post(`${AssetsAnalysisService}/lineAssetAnalyse/statusAndAnalyse`, params).then(
            ({ data }) => data
        );
    },
    /**
     * 线路资产分析
     * @method assetAnalyse 第二个柱线图
     */
    getAssetAnalyse (params) {
        return request.post(`${AssetsAnalysisService}/lineAssetAnalyse/assetAnalyse`, params).then(
            ({ data }) => data
        );
    },
    /**
     * 线路资产分析
     * @method powerRate 仪表盘
     */
    getpowerRate (params) {
        return request.post(`${AssetsAnalysisService}/lineAssetAnalyse/powerRate`, params).then(
            ({ data }) => data
        );
    },
    /**
     * 线路资产分析
     * @method assetRate 饼图
     */
    getassetRate (params) {
        return request.post(`${AssetsAnalysisService}/lineAssetAnalyse/assetRate`, params).then(
            ({ data }) => data
        );
    },
    /**
     * 巡逻签到业务-我的签到记录
     * @method showAssertList
    */
    getShowAssertList (params) {
        return request.post(`${AssetsAnalysisService}/lineAssetAnalyse/showAssertList`, params).then(
            ({ data }) => data
        );
    },
    /**
     * 巡逻签到业务-导出我的签到记录
     * @method export
    */
    exportList (params) {
        return request.post(`${AssetsAnalysisService}/lineAssetAnalyse/exportAssetDataTable`, params, { responseType: 'blob' });
    }
};
