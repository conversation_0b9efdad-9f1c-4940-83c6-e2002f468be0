<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <template #head-right>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template>
        <template #content>
            <div class="app-form-page-content-grid">
                <property-trends></property-trends>
                <residential-sales-trends></residential-sales-trends>
                <remaining-housing-delivery-trend></remaining-housing-delivery-trend>
                <property-type></property-type>
                <affordable-rental-housing-lease-trend></affordable-rental-housing-lease-trend>
                <pending-demolition-trend-of-acquired-properties></pending-demolition-trend-of-acquired-properties>
            </div>
        </template>
    </app-form-page>
</template>

<script setup>

import dayjs from 'dayjs';
import PropertyTrends from './_component/property-trends.vue';
import ResidentialSalesTrends from './_component/residential-sales-trends.vue';
import RemainingHousingDeliveryTrend from './_component/remaining-housing-delivery-trend.vue';
import PropertyType from './_component/property-type.vue';
import AffordableRentalHousingLeaseTrend from './_component/affordable-rental-housing-lease-trend.vue';
import PendingDemolitionTrendOfAcquiredProperties from './_component/pending-demolition-trend-of-acquired-properties.vue';

const name = 'asset-panorama-house-asset';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '房产'
        }
    ];
});

onMounted(() => {

});

</script>

<style lang="scss" scoped>
$page-name: asset-panorama-house-asset;

.#{$page-name} {
    :deep(.app-form-page-content-grid) {
        display: grid;
        height: 100%;
        width: 100%;
        gap: 16px;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: 1fr 1fr;

        .property-trends,
        .residential-sales-trends,
        .affordable-rental-housing-lease-trend {
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 100%;
        }

        .app-business-panel.card-panel {
            height: calc(50vh - 48px);
            min-height: 270px;
        }
    }
}
</style>
