<template>
    <div class="layout-logo" :class="[{ collapse }, direction]">
        <img class="layout-logo-icon" :src="LAYOUT_LOGO_SIDE" />
        <div class="layout-logo-text" :data-text="name" v-if="name && !collapse">{{ name }}</div>
    </div>
</template>

<script setup>
defineComponent({
    name: 'layout-logo'
});
const LAYOUT_LOGO_SIDE = `/${PACKAGE_NAME}/layout/logo-side.png`;

const props = defineProps({
    name: {
        type: String
    },
    direction: {
        type: String,
        default: 'vertical'
    },
    collapse: <PERSON>olean
});

</script>

<style lang="scss" scoped>
$layout-logo: layout-logo;

.#{$layout-logo} {
    width: auto;
    flex: 0 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacer-large);
    user-select: none;

    &-icon {
        width: auto;
        height: var(--font-line-height-larger-1);
        flex: 0 0 auto;
        object-fit: contain;
        object-position: center;
        pointer-events: none;
    }

    &-text {
        width: auto;
        flex: 1 0 auto;
        font-size: var(--font-size-medium);
        font-weight: normal;
        line-height: var(--font-line-height-larger-1);
        color: var(--color-text-light-darken);
        position: relative;
    }

    &.vertical {
        max-width: 100%;
        padding: var(--spacer-large) var(--spacer-large) var(--spacer-large) var(--spacer-large-3);
        gap: var(--spacer);
        overflow-x: hidden;

        &.collapse {
            padding: var(--spacer-large) var(--spacer-large);
            height: 64px;
        }

        .#{$layout-logo}-icon {
            max-width: 40px;
            height: 24px;
            flex: 0 0 auto;
        }

        .#{$layout-logo}-text {
            flex-shrink: 1;
            letter-spacing: 0;
            font-size: var(--font-size-medium);
            line-height: var(--font-line-height-larger-1);
        }
    }
}
</style>
