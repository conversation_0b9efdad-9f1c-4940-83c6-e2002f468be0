import { ElMessage } from 'element-plus';
import { ElLoadingService } from '@ecp/ecp-ui-plus';

import { getLoginUrl, goToLoginPage } from '@utils/initial-utils/login';

const loginUrl = getLoginUrl();

const AxiosService = axios.create();
AxiosService.defaults.withCredentials = true; // 让ajax携带cookie

export const LoginApi = {
    logout (requestOnly) {
        return new Promise((resolve, reject) => {
            const loadingInstance = ElLoadingService({
                target: document.querySelector('#app'),
                body: false,
                fullscreen: true,
                lock: true
            });
            AxiosService.get('/sso/logout')
                .then(res => {
                    if (requestOnly) {
                        resolve();
                    } else {
                        if (res.data.OpCode === 0) {
                            if (USE_PORTAL && !window.__POWERED_BY_QIANKUN__ && !window.__POWERED_BY_WUJIE__) {
                                window?.PortalTabsActions?.clear?.();
                            }
                            window.location.href = loginUrl;
                        } else if (res.data.OpCode === 403) {
                            goToLoginPage(res.data);
                        } else {
                            const messageText =
                              res.data.OpDesc || '服务访问异常';
                            ElMessage({
                                type: 'error',
                                message: messageText
                            });
                            reject(new Error(messageText));
                        }
                    }
                })
                .catch(error => {
                    reject(error);
                })
                .finally(() => {
                    loadingInstance &&
                      loadingInstance.close &&
                      loadingInstance.close();
                });
        });
    },
    tokenLogin (params) {
        return AxiosService.get('/sso/generateToken', { params }).then(
            res => res.data.Data
        );
    }
};
