<script setup>
import * as Api from '@api/index';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
import { TABLE_CONFIG } from '../constants';

const loading = ref(false);
const tableData = reactive({
    total: 0,
    list: []
});
const queryData = reactive({
    majorCategory: '',
    midCategory: '',
    minorCategory: '',
    udLine: '',
    locationRename: '',
    udUseDept: '',
    checkResult: ''
});
const dropDownOptions = reactive({
    majorCategory: [],
    midCategory: [],
    minorCategory: [],
    udLine: [],
    locationRename: [],
    udUseDept: [],
    checkResult: []
});
const pagination = reactive({
    pageNum: 1,
    pageSize: 10
});

const getCheckDetailData = async () => {
    try {
        loading.value = true;
        const response = await Api.AssetAnalysis.getCheckDetailData({ ...queryData, ...pagination });
        const { Total, Data } = response;
        tableData.list = Data;
        tableData.total = Total;
    } catch (error) {
        console.log('%c getCheckDetailData error', 'color: red', error);
        tableData.list = [];
        tableData.total = 0;
    } finally {
        loading.value = false;
    }
};

const exportEvent = async () => {
    try {
        const response = await Api.AssetAnalysis.exportCheckDetailData(queryData);
        downloadBlobData(response);
        ElMessage.success('导出成功');
    } catch (error) {
        console.log('%c exportEvent error', 'color: red', error);
        ElMessage.success('导出失败');
    }
};

const getCheckDetailType = async (dataType) => {
    return await Api.AssetAnalysis.getCheckDetailType({ dataType });
};
const typeList = ['majorCategory', 'midCategory', 'minorCategory', 'udLine', 'locationRename', 'udUseDept', 'checkResult'];
const getCheckDetailTypeList = async () => {
    const result = await Promise.allSettled(Array.from(typeList, (_, index) => getCheckDetailType(index + 1)));
    result.forEach((item, index) => {
        if (item.status === 'rejected') {
            console.log('%c getCheckDetailTypeList error', 'color: red', item.reason, index);
        } else {
            const { Data } = item.value;
            const key = typeList[index];
            dropDownOptions[key] = Data;
        }
    });
};

watch(() => queryData, getCheckDetailData, { deep: true, immediate: true });
onMounted(() => {
    getCheckDetailTypeList();
});
</script>

<template>
    <div class="inventory-details-container">
        <ecp-layout-pagination content-scroll :total="tableData.total" v-model:current-page="pagination.pageNum"
            v-model:page-size="pagination.pageSize" :loading="loading" :top-pagination="false"
            layout="prev, pager, next, sizes, jumper" @query="getCheckDetailData">
            <template #head>
                <div class="filter-container">
                    <div class="filter-container__body">
                        <el-select placeholder="资产大类" v-model="queryData.majorCategory" clearable>
                            <el-option v-for="(item, index) in dropDownOptions.majorCategory" :key="index"
                                :label="item.Name" :value="item.Code" />
                        </el-select>
                        <el-select placeholder="资产中类" v-model="queryData.midCategory" clearable>
                            <el-option v-for="(item, index) in dropDownOptions.midCategory" :key="index"
                                :label="item.Name" :value="item.Code" />
                        </el-select>
                        <el-select placeholder="资产小类" v-model="queryData.minorCategory" clearable>
                            <el-option v-for="(item, index) in dropDownOptions.minorCategory" :key="index"
                                :label="item.Name" :value="item.Code" />
                        </el-select>
                        <el-select placeholder="所属线路" v-model="queryData.udLine" clearable>
                            <el-option v-for="(item, index) in dropDownOptions.udLine" :key="index" :label="item.Name"
                                :value="item.Code" />
                        </el-select>
                        <el-select placeholder="资产位置" v-model="queryData.locationRename" clearable>
                            <el-option v-for="(item, index) in dropDownOptions.locationRename" :key="index"
                                :label="item.Name" :value="item.Code" />
                        </el-select>
                        <el-select placeholder="使用部门" v-model="queryData.udUseDept" clearable>
                            <el-option v-for="(item, index) in dropDownOptions.udUseDept" :key="index"
                                :label="item.Name" :value="item.Code" />
                        </el-select>
                        <el-select placeholder="盘点结果" v-model="queryData.checkResult" clearable>
                            <el-option v-for="(item, index) in dropDownOptions.checkResult" :key="index"
                                :label="item.Name" :value="item.Code" />
                        </el-select>
                    </div>
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading" @click="exportEvent" />
                </div>
            </template>
            <template #content>
                <app-dynamic-table class="app-panel-content-wrapper" :loading="loading" :tableData="tableData.list"
                    :tableConfig="TABLE_CONFIG" />
            </template>
        </ecp-layout-pagination>
    </div>
</template>

<style scoped lang="scss">
.inventory-details-container {
    display: flex;
    flex: 1 1 auto;
    overflow-x: hidden;

    .filter-container {
        padding-top: 33px;
        display: flex;
        justify-content: space-between;
        gap: 8px;
        width: 100%;

        &__body {
            display: flex;
            gap: 8px;
            flex-wrap: wrap
        }

        :deep(.elp-select) {
            width: 144px;
        }

        .export-button {
            margin-left: auto;

            i {
                margin-right: 8px;
            }
        }
    }

    :deep(.app-panel-content-wrapper) {
        display: flex;
        flex-direction: column;

        .ecpp-layout-pagination-content {
            display: flex;
            flex-direction: column;
            flex: 1 1 auto;
            overflow: hidden;

            .content-main {
                flex: 1 1 auto;
                overflow: auto;
                display: flex;
            }
        }
    }
}
</style>
