import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

const prefix = `${AssetsAnalysisService}/assetDetails`;
export const OperationsOfficeAssets = {
/**
     * @method getRunningAssetDetailTable 获取运营资产明细列表
     * */
    getRunningAssetDetailTable (params) {
        return request.post(`${prefix}/getRunningAssetDetailTable`, params).then(
            ({ data }) => data
        ); ;
    },
    /**
     * @method getOperateUnit 获取运营资产中的使用单位信息
     * */
    getOperateUnit (params) {
        return request.get(`${prefix}/getOperateUnit`, params).then(
            ({ data }) => data
        );
    },
    /**
     * @method exportRunningAssetDetailTable 导出线路运营明细
     * */
    exportRunningAssetDetailTable (params) {
        return request.post(`${prefix}/exportRunningAssetDetailTable`, params, { responseType: 'blob' });
    },
    /**
     * @method getMultiAssetCodeDesc 获取资产大、中和小类
     * */
    getMultiAssetCodeDesc (params) {
        return request({
            url: `${prefix}/getMultiAssetCodeDesc`,
            method: 'get',
            params
        }).then(
            ({ data }) => data
        );
    },

    // 办公资产明细 S
    /**
     * @method getOfficeAssetDetails 资产明细-办公资产明细
     * */
    getOfficeAssetDetails (params) {
        return request.post(`${prefix}/getOfficeAssetDetails`, params).then(
            ({ data }) => data
        ); ;
    },
    /**
     * @method getDimAsset 获取资产大、中和小类
     * */
    getDimAsset (params) {
        return request({
            url: `${prefix}/getDimAsset`,
            method: 'get',
            params
        }).then(
            ({ data }) => data
        );
    },
    /**
     * @method exportOfficeAssetDetails 导出办公明细
     * */
    exportOfficeAssetDetails (params) {
        return request.post(`${prefix}/exportOfficeAssetDetails`, params, { responseType: 'blob' });
    }
    // 办公资产明细 E
};
