export const LINE_OPERATING_ASSET_DETIAL_TABLE = {
    props: {},
    cols: [
        {
            label: '序号',
            prop: 'Order'
        },
        {
            label: '资产编码',
            prop: 'AssetCode'
        },
        {
            label: '资产名称',
            prop: 'AssetName'
        },
        {
            label: '资产大类',
            prop: 'AssetMajorName'
        },
        {
            label: '资产中类',
            prop: 'AssetMiddleName'
        },
        {
            label: '资产小类',
            prop: 'AssetMinorName'
            // align: 'right'
        },
        {
            label: '管理单位',
            prop: 'ManagerUnit'
        },
        {
            label: '使用单位',
            prop: 'OperateUnit'
        },
        {
            label: '使用部门',
            prop: 'OperateUsedDept'
        },
        {
            label: '资产状态',
            prop: 'AssetStatus'
        },
        {
            label: '地点位置名称',
            prop: 'LocationName'
        },
        {
            label: '所属路线',
            prop: 'LineDesc'
        },
        {
            label: '线路期段（合同项目）',
            prop: 'ContractProject'
        },
        {
            label: '规格型号',
            prop: 'Specifications'
        },
        {
            label: '数量',
            prop: 'Amount'
        },
        {
            label: '计量单位',
            prop: 'Measurement'
        },
        {
            label: '建帐日期',
            prop: 'BuildBillDate'
        }
    ]
};

export const OFFICE_ASSET_DETIAL_TABLE = {
    props: {},
    cols: [
        {
            label: '序号',
            prop: 'Number'
        },
        {
            label: '资产编码',
            prop: 'AssetNumber'
        },
        {
            label: '资产名称',
            prop: 'AssetName'
        },
        {
            label: '资产大类',
            prop: 'AssetBig'
        },
        {
            label: '资产中类',
            prop: 'AssetMiddle'
            // align: 'right'
        },
        {
            label: '资产小类',
            prop: 'AssetSmall'
        },
        {
            label: '管理单位',
            prop: 'ManagementUnit'
        },
        {
            label: '使用单位',
            prop: 'UserUnit'
        },
        {
            label: '资产状态',
            prop: 'AssetState'
        },
        {
            label: '地点位置名称',
            prop: 'PlaceName'
        },
        {
            label: '数量',
            prop: 'Quantity'
        },
        {
            label: '计量单位',
            prop: 'Unit'
        },
        {
            label: '建帐日期',
            prop: 'AccountDate'
        }
    ]
};
