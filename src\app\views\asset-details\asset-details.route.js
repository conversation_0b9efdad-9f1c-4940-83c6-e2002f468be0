import {
    HouseResources,
    LandResources,
    OtherAssets,
    OperationsOfficeAssets,
    CertificateProcessing
} from './index';

export const AssetDetailsRoutes = () => {
    const context = 'asset-details';
    return [
        {
            path: `/${context}/house-resources`,
            component: HouseResources,
            label: '房产资源',
            name: `${context}-house-resources`
        },
        {
            path: `/${context}/land-resources`,
            component: LandResources,
            label: '土地资源',
            name: `${context}-land-resources`
        },
        {
            path: `/${context}/other-assets`,
            component: OtherAssets,
            label: '其他经营资产',
            name: `${context}-other-assets`
        },
        {
            path: `/${context}/operations-office-assets`,
            component: OperationsOfficeAssets,
            label: '运营与办公资产',
            name: `${context}-operations-office-assets`
        },
        {
            path: `/${context}/certificate-processing`,
            component: CertificateProcessing,
            label: '权证办理',
            name: `${context}-certificate-processing`
        }
    ];
};
