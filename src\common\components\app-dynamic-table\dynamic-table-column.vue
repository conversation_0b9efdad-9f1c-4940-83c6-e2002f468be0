<template>
    <el-table-column v-bind="{ ...defaultColumnProps, ...tableColumnProps }" class="dynamic-table-column" :class-name="getColumnClassName(
        'dynamic-table-column',
        col.type,
        colHidden ? 'is-hidden' : '',
        `level-${level}`,
        tableColumnProps.className
    )
        ">
        <template #header="headerData" v-if="col['slotHeader']">
            <slot v-bind="{ ...headerData }" :name="getCustomSlotName(col['slotHeader'], true)"></slot>
        </template>
        <template v-if="col['cols'] && col['cols'].length > 0">
            <dynamic-table-column v-for="(childCol, childIndex) in col['cols']" :page-config="pageConfig"
                :col="childCol" :level="level + 1" :key="`${childCol.prop}_${childIndex}`">
                <template #[getCustomSlotName(childCol.slotHeader)]="headerData" v-if="childCol.slotHeader">
                    <slot :name="getCustomSlotName(childCol.slotHeader, true)" v-bind="headerData"></slot>
                </template>
                <template #[getCustomSlotName(childCol.slot)]="defaultData" v-if="childCol.slot">
                    <slot :name="getCustomSlotName(childCol.slot)" v-bind="defaultData"></slot>
                </template>
                <template #default="{ row, column, $index }" v-else-if="!col.type">
                    <template v-if="
                        notEmpty(
                            formatData(
                                row,
                                column,
                                row && row[column && column.property],
                                $index,
                                true
                            ),
                            col
                        )
                    ">
                        <span>{{
                            formatData(
                                row,
                                column,
                                row && row[column && column.property],
                                $index
                            )
                        }}</span>
                    </template>
                    <template v-else>
                        <span class="empty-cell"></span>
                    </template>
                </template>
            </dynamic-table-column>
        </template>
        <template #default="defaultData" v-if="!!col['slot']">
            <slot v-bind="setSlotParams(defaultData)" :name="getCustomSlotName(col['slot'])"></slot>
        </template>
    </el-table-column>
</template>

<script setup>
import { cloneDeep } from 'lodash-es';

defineComponent({
    name: 'dynamic-table-column'
});

const DEFAULT_TABLE_COLUMN_PROP = {};

const props = defineProps({
    col: {
        type: Object,
        default: () => ({})
    },
    pageConfig: {
        type: Object,
        default: () => ({})
    },
    level: {
        type: [Number, String],
        default: 1
    }
});

const defaultColumnProps = {
    showOverflowTooltip: true
};

const tableColumnProps = ref({});
const colHidden = ref(false);

watch(
    () => props.col,
    (newVal) => {
        const col = cloneDeep(newVal);

        const target = cloneDeep({
            ...DEFAULT_TABLE_COLUMN_PROP,
            ...col,
            prop: col.prop,
            label: col.label || (col.type === 'index' ? '序号' : '')
        });

        if (target.type === 'index') {
            const pageSize = props.pageConfig.pageSize || 10;
            let pageNum =
                props.pageConfig.pageNum || props.pageConfig.pageNum === 0
                    ? props.pageConfig.pageNum
                    : 1;
            pageNum = pageNum - 1 < 0 ? 0 : pageNum - 1;

            target.index =
                typeof col.index === 'function'
                    ? (index) => col.index(index, pageNum, pageSize)
                    : (index) => {
                        let colIndex = pageNum * pageSize + index + 1;
                        if (colIndex < 10) {
                            colIndex = `0${colIndex}`;
                        }
                        return colIndex;
                    };
            target.width = target.width || 70;
        }

        colHidden.value = !!target.colHidden;
        delete target.colHidden;

        tableColumnProps.value = cloneDeep(target);
    },
    {
        immediate: true,
        deep: true
    }
);

watch(
    () => props.pageConfig,
    (newVal) => {
        const pageConfig = newVal;

        nextTick(() => {
            const col = props.col;
            if (!pageConfig || col.type !== 'index') return;
            const target = {};

            const pageSize = pageConfig.pageSize || 10;
            let pageNum =
                pageConfig.pageNum || pageConfig.pageNum === 0 ? pageConfig.pageNum : 1;
            pageNum = pageNum - 1 < 0 ? 0 : pageNum - 1;

            target.index =
                typeof col.index === 'function'
                    ? (index) => col.index(index, pageNum, pageSize)
                    : (index) => {
                        let colIndex = pageNum * pageSize + index + 1;
                        if (colIndex < 10) {
                            colIndex = `0${colIndex}`;
                        }
                        return colIndex;
                    };
            target.width = col.width || 70;

            tableColumnProps.value = cloneDeep({
                ...tableColumnProps.value,
                ...target
            });
        });
    }
);

const notEmpty = (value, col) => {
    return (
        col?.keepNull || (value !== '' && value !== null && value !== undefined)
    );
};

const formatData = (row, column, cellValue, index, ignoreDefaultFill) => {
    if (column && typeof column.formatter === 'function') {
        cellValue = column.formatter(row, column, cellValue, index);
    }
    if (notEmpty(cellValue, column)) {
        return cellValue;
    }
    return ignoreDefaultFill ? cellValue : '--';
};

const setSlotParams = (defaultData) => {
    const property = defaultData?.column?.property;
    const emptyCell = !notEmpty(
        formatData(
            defaultData.row,
            defaultData.column,
            defaultData.row && defaultData.row[property],
            defaultData.$index,
            true
        ),
        props.col
    );
    return {
        emptyCell,
        col: props.col,
        ...defaultData
    };
};

const getColumnClassName = (...classList) => {
    return (classList || [])
        .filter((item) => item !== '' && item !== null && item !== undefined)
        .join(' ');
};

const getCustomSlotName = (templateName, isHeader) => {
    if (!templateName) return;

    return (isHeader ? `${templateName}-header` : templateName).toLowerCase();
};
</script>

<style lang="scss" scoped>
.dynamic-table-column {
    & > .cell {
        min-width: 36px;

        .empty-cell {
            color: var(--color-text-placeholder);
        }

        .cell-content + .cell-content {
            margin-left: 8px;
        }

        .cell-tag {
            line-height: 18px;
        }
    }

    &.el-table__cell:not(.is-group .el-table__cell) {
        & > .cell {
            line-height: 32px;
        }
    }

    &.expand.is-hidden {
        width: 0;
        min-width: 0;
        max-width: 0;

        & > .cell {
            width: 0;
            min-width: 0;
            overflow: hidden;
        }
    }
}
</style>
