<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <template #head-right>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template>
        <template #content>
            <div class="chart-grid">
                <app-business-panel class="merged-panel" mode="card" title="车站广告资源租赁情况" v-loading="loading1">
                    <template #header-append>
                        <div class="button-group">
                            <el-select v-model="line" placeholder="请选择" class="type-select"
                                @change="handleLineOptionChange">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                            <ecp-tag v-model="type1" :options="tagTypeOptions" @change="handleStationData(type1)" />
                        </div>
                    </template>
                    <LineAdvertising :chartData="stationChartData" :unit="unit1" />
                </app-business-panel>
                <app-business-panel mode="card" title="出租率top10车站" v-loading="loading2">
                    <template #header-append>
                        <ecp-tag v-model="type2" :options="tagTypeOptions" @change="handleAlreadyData(type2)" />
                    </template>
                    <chart-component :data="chartData2" :unit="unit2" />
                </app-business-panel>
                <app-business-panel mode="card" title="线路广告资源租赁情况" v-loading="loading3">
                    <template #header-append>
                        <ecp-tag v-model="type3" :options="tagTypeOptions" @change="handleLineData(type3)" />
                    </template>
                    <LineAdvertising :chartData="lineChartData" :unit="unit3" />
                </app-business-panel>
                <app-business-panel mode="card" title="其他资源" v-loading="loading4">
                    <template #header-append>
                        单位:亩
                    </template>
                    <template v-if="otherResourcesData.length === 0">
                        <ecp-empty></ecp-empty>
                    </template>
                    <template v-else>
                        <OtherResources :otherResourcesData="otherResourcesData" />
                    </template>
                </app-business-panel>
                <app-business-panel mode="card" title="待出租率top10车站" v-loading="loading5">
                    <template #header-append>
                        <ecp-tag v-model="type5" :options="tagTypeOptions" @change="handleTreatData(type5)" />
                    </template>
                    <chart-component :data="chartData5" :unit="unit5" />
                </app-business-panel>
            </div>
        </template>
    </app-form-page>
</template>

<script setup>
import { ref, computed, onMounted, onUpdated, onActivated } from 'vue';
import { AdvertisementAsset } from '@api/asset-panorama/advertisement-asset';
import ChartComponent from './component/top10-chart-component.vue';
import OtherResources from './component/other-resources.vue';
import LineAdvertising from './component/line-advertising.vue';

import dayjs from 'dayjs';

const name = 'asset-panorama-advertisement-asset';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '文体广告'
        }
    ];
});

const tagTypeOptions = [
    { label: '数 量', value: 'amount' },
    { label: '面 积', value: 'area' }
];

// Loading 状态变量
const loading1 = ref(false);
const loading2 = ref(false);
const loading3 = ref(false);
const loading4 = ref(false);
const loading5 = ref(false);

const line = ref('');
const options = ref([]);

// 获取线路数据
const fetchLineData = async () => {
    try {
        const data = await AdvertisementAsset.getLineCode();
        // const filteredData = data.Data.filter(item => {
        //     const lineCode = parseInt(item.LineCode, 10);
        //     return (
        //         (lineCode >= 1 && lineCode <= 8) ||
        //         lineCode === 21
        //     );
        // });
        //
        // options.value = filteredData.map(item => ({
        //     value: item.LineCode,
        //     label: parseInt(item.LineCode, 10) === 21 ? '阳逻线' : item.LineName,
        // }));
        options.value = data.Data.map(item => ({
            value: item.LineCode,
            label: parseInt(item.LineCode, 10) === 21 ? '阳逻线' : item.LineName,
        }));

        if (options.value.length > 0) {
            line.value = options.value[0].value;
        }
    } catch (error) {
        console.error('获取数据失败', error);
    }
};

// 格式化图表数据
const formatChartData = (data) => {
    return {
        dimensions: ['类目名称', '数量'],
        source: data.map(item => ({
            类目名称: item.DataName,
            数量: item.Percentage || 0
        }))
    };
};

// 获取图表数据
const fetchChartData = async (loading, apiMethod, params, formatFunction, chartData) => {
    loading.value = true;
    try {
        const response = await apiMethod(params);
        if (!response.Data || response.Data.length === 0) {
            chartData.value = { dimensions: [], source: [] }; // 空数据结构
        } else {
            chartData.value = formatFunction(response.Data || {});
        }
    } catch (error) {
        console.error('获取图表数据失败', error);
    } finally {
        loading.value = false;
    }
};

// 车站广告资源租赁情况
const stationChartData = reactive({
    dimensions: ['类目名称', '可租赁面积', '已租赁面积', '出租率'],
    source: []
});
const unit1 = ref('个');
const type1 = ref('amount'); // 当前选中字段类型

const fetchStationChartData = async (type, lineCode) => {
    
    if (!lineCode) { // 检查 lineCode 是否为空
        return;
    }
    loading1.value = true;

    if (type === 'amount') {
        stationChartData.dimensions = ['类目名称', '可租赁数量', '已租赁数量', '出租率'];
    } else {
        stationChartData.dimensions = ['类目名称', '可租赁面积', '已租赁面积', '出租率'];
    }
    try {
        const response = await AdvertisementAsset.getStationAdSituation({ type, lineCode });
        if (response?.Data) {
            const sourceData = response.Data.map((item) => ({
                类目名称: item.DataName || '',
                [stationChartData.dimensions[1]]: item.Rentable || 0, // 可租赁数量/面积
                [stationChartData.dimensions[2]]: item.Leased || 0, // 已租赁数量/面积
                [stationChartData.dimensions[3]]: item.RentalRate || 0 // 出租率
            }));
            stationChartData.source = sourceData;
        } else {
            stationChartData.source = [];
        }
    } catch (error) {
        console.error('获取线路广告资源租赁情况数据失败:', error);
        stationChartData.source = [];
    } finally {
        loading1.value = false;
    }
};

const handleLineOptionChange = async (selectedValue) => {
    line.value = selectedValue;
    fetchStationChartData(type1.value, line.value);
};

const handleStationData = (selectedType) => {
    type1.value = selectedType;
    fetchStationChartData(selectedType, line.value);
    unit1.value = selectedType === 'amount' ? '个' : '平方米';
};

// 已出租率top10
const chartData2 = ref({
    dimensions: [],
    source: []
});
const type2 = ref('amount');
const unit2 = ref('个'); // 默认单位为“个”
const alreadyParams = computed(() => ({
    type: type2.value,
    state: 'already'
}));

const getAlreadyTop10Data = () => fetchChartData(loading2, AdvertisementAsset.getAdvertisingTop10, alreadyParams.value, formatChartData, chartData2);
const handleAlreadyData = (selectedType) => {
    type2.value = selectedType;
    getAlreadyTop10Data();
    unit2.value = selectedType === 'amount' ? '个' : '平方米';
};

// 线路广告资源租赁情况

const lineChartData = reactive({
    dimensions: ['类目名称', '可租赁面积', '已租赁面积', '出租率'],
    source: []
});
const unit3 = ref('个');
const type3 = ref('amount'); // 当前选中字段类型

const fetchLineChartData = async (type) => {
    loading3.value = true;
    if (type === 'amount') {
        lineChartData.dimensions = ['类目名称', '可租赁数量', '已租赁数量', '出租率'];
    } else {
        lineChartData.dimensions = ['类目名称', '可租赁面积', '已租赁面积', '出租率'];
    }
    try {
        const response = await AdvertisementAsset.getLineAdSituation({ type });
        if (response?.Data) {
            const sourceData = response.Data.map((item) => ({
                类目名称: item.DataName || '',
                [lineChartData.dimensions[1]]: item.Rentable || 0, // 可租赁数量/面积
                [lineChartData.dimensions[2]]: item.Leased || 0, // 已租赁数量/面积
                [lineChartData.dimensions[3]]: item.RentalRate || 0 // 出租率
            }));

            lineChartData.source = sourceData;
        } else {
            lineChartData.source = [];
        }
    } catch (error) {
        console.error('获取线路广告资源租赁情况数据失败:', error);
        lineChartData.source = [];
    } finally {
        loading3.value = false;
    }
};

const handleLineData = (selectedType) => {
    type3.value = selectedType;
    fetchLineChartData(selectedType);
    unit3.value = selectedType === 'amount' ? '个' : '平方米';
};

// 其他资源数据
const otherResourcesData = ref([]);

const fetchOtherResourcesData = async () => {
    loading4.value = true;
    try {
        const response = await AdvertisementAsset.getAdvertisingOther();
        if (response?.Data) {
            otherResourcesData.value = response.Data;
        } else {
            otherResourcesData.value = [];
        }
    } catch (error) {
        console.error('获取其他资源数据失败:', error);
        otherResourcesData.value = [];
    } finally {
        loading4.value = false;
    }
};

// 待出租率top10
const chartData5 = ref({
    dimensions: [],
    source: []
});
const unit5 = ref('个');
const type5 = ref('amount');
const treatParamsTreat = computed(() => ({
    type: type5.value,
    state: 'treat'
}));
const handleTreatData = (selectedType) => {
    type5.value = selectedType;
    getTreatTop10Data();
    unit5.value = selectedType === 'amount' ? '个' : '平方米';
};

const getTreatTop10Data = () => fetchChartData(loading5, AdvertisementAsset.getAdvertisingTop10, treatParamsTreat.value, formatChartData, chartData5);


onActivated(async () => {
    getTreatTop10Data(),
        getAlreadyTop10Data();
    fetchOtherResourcesData();
    fetchLineChartData(type3.value);
    fetchStationChartData(type1.value, line.value);
});

onMounted(async () => {
    await fetchLineData();
    fetchStationChartData(type1.value, line.value);
});

</script>

<style lang="scss" scoped>
$page-name: asset-panorama-advertisement-asset;

.#{$page-name} {
    // height: calc(100vh - 60px);
    display: flex;
    flex-direction: column;

    .chart-grid {
        flex: 1;
        height: 100%;
        width: 100%;
        display: grid;
        gap: var(--spacer-large);
        grid-template-rows: 1fr 1fr;
        grid-template-columns: repeat(3, minmax(320px, 1fr));
        // grid-auto-rows: 1fr;
        grid-auto-rows: minmax(200px, auto); // 设置最小高度，避免高度过高

        .merged-panel {
            grid-column: span 2;
        }

        .app-business-panel {
            height: 100%;
            min-height: 320px;
            min-width: 320px;
            overflow: hidden;
        }

        :deep(.app-business-panel.card-panel) {
            height: calc(50vh - 48px);
            min-height: 270px;
        }

        .button-group {

            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: var(--spacer-large);

            .type-select {
                flex-shrink: 0;
                width: 120px;
            }
        }



    }

}
</style>
