<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <template #head-right>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template>
        <template #content>
            <div class="chart-grid">
                <app-business-panel mode="card" title="办公资产大类" v-loading="loading1">
                    <chart-component :data="chartData1" chartType="vertical" />
                </app-business-panel>
                <app-business-panel mode="card" v-loading="loading2">
                    <template #header>
                        <span class="app-business-panel-title">办公资产中类</span>
                    </template>
                    <template #header-append>
                        <el-select class="type-select" v-model="bigQuerySelected" placeholder="资产大类"
                            @change="handleBigQueryOptionChange">
                            <el-option v-for="option in bigQueryOptions" :key="option.value" :label="option.label"
                                :value="option.value"></el-option>
                        </el-select>
                    </template>
                    <chart-component :data="chartData2" chartType="vertical" />
                </app-business-panel>
                <app-business-panel mode="card" title="办公资产中类累计top10" v-loading="loading3">
                    <chart-component :data="chartData3" chartType="horizontal" yName="项" />
                </app-business-panel>
                <app-business-panel mode="card" title="办公资产归属单位" v-loading="loading4">
                    <chart-component :data="chartData4" chartType="vertical" />
                </app-business-panel>
                <app-business-panel mode="card" v-loading="loading5">
                    <template #header>
                        <span class="app-business-panel-title">办公资产归属部门</span>
                    </template>
                    <template #header-append>
                        <el-select class="type-select" v-model="unitCodeSelected" placeholder="归属单位"
                            @change="handleUnitQueryOptionChange">
                            <el-option v-for="option in unitQueryOptions" :key="option.value" :label="option.label"
                                :value="option.value"></el-option>
                        </el-select>
                    </template>
                    <chart-component :data="chartData5" chartType="vertical" />
                </app-business-panel>
                <app-business-panel mode="card" title="办公资产归属部门增长top10" v-loading="loading6">
                    <chart-component :data="chartData6" chartType="horizontal" yName="项" />
                </app-business-panel>

            </div>
        </template>
    </app-form-page>
</template>

<script setup>
import ChartComponent from './components/chart-component.vue';
import { OfficeAsset } from '@api/asset-panorama/office-asset';
import { ref, computed, onMounted, onUpdated, onActivated } from 'vue';
import dayjs from 'dayjs';
const name = 'asset-panorama-office-asset';

defineOptions({
    name
});

const title = computed(() => [
    { label: '办公资产' }
]);

const disabledFutureDates = (date) => {
    return date.getTime() > new Date().getTime();
};

// Loading 状态变量
const loading1 = ref(false);
const loading2 = ref(false);
const loading3 = ref(false);
const loading4 = ref(false);
const loading5 = ref(false);
const loading6 = ref(false);

const bigQueryOptions = ref([]); // 办公资产中类变化情况的下拉框选项
const unitQueryOptions = ref([]); // 办公资产归属部门变化情况的下拉框选项
const bigQuerySelected = ref(null); // 存储办公资产中类变化情况的下拉框选中项
const unitCodeSelected = ref(null);

const handleBigQueryOptionChange = async (selectedValue) => {
    bigQuerySelected.value = selectedValue;
    getOfficeAssetMediumData();
};

const handleUnitQueryOptionChange = async (selectedValue) => {
    unitCodeSelected.value = selectedValue;
    getOfficeAssetUnitDeptData();
};

const fetchDropdownOptions = async () => {
    try {
        const bigQueryData = await OfficeAsset.getOfficeAssetBigQueryData();
        bigQueryOptions.value = bigQueryData.Data.map(item => ({
            label: item.DataName,
            value: item.DataCode
        }));
        // 默认选中第一个
        if (bigQueryOptions.value && bigQueryOptions.value.length > 0) {
            bigQuerySelected.value = bigQueryOptions.value[0].value;
        }

        const unitQueryData = await OfficeAsset.getOfficeAssetUnitQueryData();
        unitQueryOptions.value = unitQueryData.Data.map(item => ({
            label: item.DataName,
            value: item.DataCode
        }));
        // 默认选中第一个
        if (unitQueryOptions.value && unitQueryOptions.value.length > 0) {
            unitCodeSelected.value = unitQueryOptions.value[0].value;
        }
    } catch (error) {
        console.error('获取下拉框数据失败', error);
    }
};

// 各个图表数据定义
const chartData1 = ref({
    dimensions: [],
    source: []
});

const chartData2 = ref({
    dimensions: [],
    source: []
});
const chartData3 = ref({
    dimensions: [],
    source: []
});
const chartData4 = ref({
    dimensions: [],
    source: []
});
const chartData5 = ref({
    dimensions: [],
    source: []
});

const chartData6 = ref({
    dimensions: [],
    source: []
});

// 响应为空展示空状态
const fetchChartData = async (loading, apiMethod, params, formatFunction, chartData) => {
    loading.value = true;
    try {
        const response = await apiMethod(params);
        // 如果后端返回的数据为空，确保 chartData 也是空
        if (!response.Data || response.Data.length === 0) {
            chartData.value = { dimensions: [], source: [] }; // 空数据结构
        } else {
            chartData.value = formatFunction(response.Data || {});
        }
    } catch (error) {
        console.error('获取图表数据失败', error);
    } finally {
        loading.value = false;
    }
};

// 通用的 formatChartData 函数
const formatChartData = (data, fields) => {
    return {
        dimensions: ['类目名称', '数量'],
        source: fields.map(field => ({
            类目名称: field.label,
            数量: data && data[field.key] ? data[field.key] : 0
        }))
    };
};

const formatChartData2 = (data) => {
    return {
        dimensions: ['类目名称', '数量'],
        source: data.map(item => ({
            类目名称: item.DataName,
            数量: item.DataCount || 0
        }))
    };
};

const formatChartData5 = (data) => {
    return {
        dimensions: ['部门名称', '数量'], // 保持图表字段一致
        source: data.map(item => ({
            部门名称: [item.ParentName, item.DataName].filter(Boolean).join(' / '), // 拼接父部门和子部门名称
            数量: item.DataCount || 0
        }))
    };
};

const commonParams = reactive({});

// 获取办公资产大类变化情况1
const getOfficeAssetBigData = () => fetchChartData(loading1, OfficeAsset.getOfficeAssetBig, commonParams, formatChartData2, chartData1);

// 获取办公资产中类变化情况2
const getOfficeAssetMediumData = () => {
    if (!bigQuerySelected.value) {
        return;
    }
    fetchChartData(loading2, OfficeAsset.getOfficeAssetMedium, { ...commonParams, officeDeviceType: bigQuerySelected.value }, formatChartData2, chartData2);
};

// 获取办公资产中类累计Top10 3
const getOfficeAssetMiddleTop10Data = () => fetchChartData(loading3, OfficeAsset.getOfficeAssetMiddleTop10, commonParams, formatChartData2, chartData3);

// 获取办公资产归属单位变化情况4
const getOfficeAssetUnitData = () => fetchChartData(loading4, OfficeAsset.getOfficeAssetUnit, commonParams, formatChartData2, chartData4);

// 获取办公资产归属部门项变化情况
const getOfficeAssetUnitDeptData = () => {
    if (!unitCodeSelected.value) {
        return;
    }
    fetchChartData(loading5, OfficeAsset.getOfficeAssetUnitDept, {
        ...commonParams,
        unitCode: unitCodeSelected.value
    }, formatChartData2, chartData5);
};

// 获取办公资产同居部门项Top10
const getOfficeAssetUnitDeptTop10Data = () => fetchChartData(loading6, OfficeAsset.getOfficeAssetUnitDeptTop10, commonParams, formatChartData5, chartData6);

// 获取所有图表数据
const fetchAllChartData = async () => {
    await Promise.all([
        getOfficeAssetBigData(),
        getOfficeAssetMediumData(),
        getOfficeAssetMiddleTop10Data(),
        getOfficeAssetUnitData(),
        getOfficeAssetUnitDeptData(),
        getOfficeAssetUnitDeptTop10Data()

    ]);
};

onActivated(async () => {
    await fetchDropdownOptions();
    fetchAllChartData();
});

</script>

<style scoped lang="scss">
$page-name: asset-panorama-office-asset;

.#{$page-name} {
    height: calc(100vh - 60px);
    display: flex;
    flex-direction: column;

    .chart-grid {
        flex: 1;
        height: 100%;
        width: 100%;
        display: grid;
        gap: 16px;
        grid-template-areas:
            "header header"
            "main main";
        grid-template-rows: 1fr 1fr;
        grid-template-columns: repeat(3, minmax(320px, 1fr)); // 强制三列，最小宽度320px

        :deep(.app-business-panel.card-panel) {
            height: calc(50vh - 48px);
            min-height: 270px;
        }

        .type-select {
            width: 120px;
        }
    }
}
</style>
