<template>
    <div class="asset-change">
        <ecp-layout-pagination>
            <template #head>
                <div class="header-toolbar">
                    <el-date-picker v-model="_createDate" type="month" placeholder="选择年月"
                        :disabled-date="disabledFutureDates" style="width: 150px" :clearable="false" />
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading" @click="ExportAssetChangeTable" />
                </div>
            </template>
            <template #content>
                <app-dynamic-table :loading="loading" :table-data="tableData" :table-config="tableConfig" />
            </template>
        </ecp-layout-pagination>
    </div>
</template>

<script setup>
import * as Api from '@api/index';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import { CHANGES_IN_ASSETS_TABLE_CONFIG } from '../constants';

const props = defineProps({
    disabledFutureDates: {
        type: Function,
        default: () => true
    }
});
const _createDate = ref(new Date());
const loading = ref(false);
const tableData = ref([]);
const firstCol = ref([]);
const timeSpan = computed(() => {
    const arr = [];
    const list = tableData.value.map(item => item.Source);
    const copy = new Set(list);
    copy.forEach((item) => {
        const count = list.filter(i => i === item).length;
        arr.push(...[count, ...Array.from({ length: count - 1 }, () => 0)]);
    });
    return arr;
});
const tableConfig = computed(() => ({
    ...CHANGES_IN_ASSETS_TABLE_CONFIG,
    props: { spanMethod }
}));

const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
    const span = timeSpan.value;
    if (row.Source === row.AssetType && columnIndex <= 1) {
        return {
            rowspan: columnIndex,
            colspan: 2
        };
    }
    if (columnIndex === 0) {
        return {
            rowspan: span[rowIndex],
            colspan: 1
        };
    }
    return {
        rowspan: 1,
        colspan: 1
    };
};

const ConstructData = (current, last) => {
    const tempSource = [];
    const tempAssetType = [];
    const newData = [];
    const setData = (data, type) => {
        const { Source, AssetType, Count, Value } = data;
        const tempSourceIndex = tempSource.indexOf(Source);
        const tempAssetTypeIndex = tempAssetType.indexOf(AssetType);
        if (tempSourceIndex === -1) {
            tempSource.push(Source);
            tempAssetType.push(AssetType);
            newData.push({ Source, AssetType, [`${type}_count`]: Count, [`${type}_value`]: Value });
        } else if (tempSourceIndex !== -1 && tempAssetTypeIndex === -1) {
            tempAssetType.push(AssetType);
            newData.push({ Source, AssetType, [`${type}_count`]: Count, [`${type}_value`]: Value });
        } else if (tempSourceIndex !== -1 && tempAssetTypeIndex !== -1) {
            const object = newData.find(item => item.Source === Source && item.AssetType === AssetType);
            if (object) {
                object[`${type}_count`] = Count;
                object[`${type}_value`] = Value;
            } else {
                newData.push({ Source, AssetType, [`${type}_count`]: Count, [`${type}_value`]: Value });
            }
        }
    };
    let currentSummary;
    let lastSummary;
    for (const datum of current) {
        if (datum.Source === '总计') {
            currentSummary = datum;
            continue;
        }
        setData(datum, 'thisMonth');
    }
    for (const datum of last) {
        if (datum.Source === '总计') {
            lastSummary = datum;
            continue;
        }
        setData(datum, 'lastMonth');
    }
    const summary = { Source: '总计', AssetType: '总计', thisMonth_count: currentSummary.Count, lastMonth_count: lastSummary.Count, thisMonth_value: currentSummary.Value, lastMonth_value: lastSummary.Value };
    const filtered = newData.filter(obj => obj.Source !== '其他');
    const moved = newData.filter(obj => obj.Source === '其他');
    tableData.value = filtered.concat(moved.map(item => ({ ...item, Source: item.AssetType }))).concat([summary]);
    firstCol.value = tableData.value.map(item => item.Source);
};

const GetAssetChangeTable = async () => {
    try {
        loading.value = true;
        const currentMonth = dayjs(_createDate.value).format('YYYY-MM');
        const lastMonth = dayjs(_createDate.value).subtract(1, 'month').format('YYYY-MM');
        const currentMonthResponse = await Api.AssetAnalysis.GetAssetChangeTable({ createDate: currentMonth });
        const lastMonthResponse = await Api.AssetAnalysis.GetAssetChangeTable({ createDate: lastMonth });
        ConstructData(currentMonthResponse.Data, lastMonthResponse.Data);
    } catch (e) {
        console.log('%c GetAssetChangeTable', 'font-size:18px;color:green;font-weight:700;', e);
    } finally {
        loading.value = false;
    }
};

const ExportAssetChangeTable = async () => {
    try {
        if (tableData.value.length > 0) {
            const response = await Api.AssetAnalysis.ExportAssetChangeTable({ createDate: dayjs(_createDate.value).format('YYYY-MM') });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (e) {
        console.log('%c ExportAssetChangeTable', 'font-size:18px;color:green;font-weight:700;', e);
        ElMessage.error('导出失败');
    }
};

watch(_createDate, GetAssetChangeTable, { immediate: true });
</script>

<style scoped lang="scss">
.asset-change {
    height: 100%;
    width: 100%;

    :deep(.ecpp-layout-pagination) {
        display: flex;
        flex-direction: column;

        .ecpp-layout-pagination-content {
            flex: 1;
            display: flex;

            .content-main {
                flex: 1;
                display: flex;

                .elp-table {
                    flex: 1;
                }
            }
        }
    }
}
</style>
