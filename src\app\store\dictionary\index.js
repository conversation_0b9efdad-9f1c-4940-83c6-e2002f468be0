import { cloneDeep } from 'lodash-es';

import { CUSTOM_DICT_MAP, DICTIONARY_MAP } from '@constants/dictionary-map';
import SystemService from '@api/system-service';

const useDictionaryStore = defineStore('dictionary-store', {
    state: () => {
        return {
            // 字典缓存
            dictionaryCache: {},

            // 字典树缓存
            dictionaryTreeCache: {}
        };
    },
    actions: {
        /**
         * @method getDictionaryCache 获取并缓存字典 (平铺)
         * @param {String,Number} KindKey
         * @param { Boolean } force 是否强制刷新字典缓存
         * @returns {Promise.resolve(Array)}
         */
        async getDictionaryCache (KindKey, force) {
            const existDictionaryMap = DICTIONARY_MAP.hasOwnProperty.call(KindKey);
            const Kind = existDictionaryMap ? DICTIONARY_MAP[KindKey] : KindKey;
            if (
                !force &&
                this.dictionaryCache[KindKey] &&

                this.dictionaryCache[KindKey] instanceof Array &&
                this.dictionaryCache[KindKey].length > 0
            ) {
                return this.dictionaryCache[KindKey];
            } else {
                if (window.dictionaryCallbacks?.[KindKey]) {
                    return new Promise((resolve, reject) => {
                        window.dictionaryCallbacks[KindKey].push(res =>
                            resolve(res)
                        );
                    });
                } else {
                    if (!window.dictionaryCallbacks) {
                        window.dictionaryCallbacks = {};
                    }
                    window.dictionaryCallbacks[KindKey] = [];

                    let dict = [];

                    try {
                        if (KindKey && String(KindKey).match(/^CUSTOM_/)) {
                            const matchedAction = CUSTOM_DICT_MAP[Kind];
                            if (typeof matchedAction === 'function') {
                                dict = await matchedAction();
                            } else {
                                throw new Error('未找到匹配的自定义字典方法');
                            }
                        } else {
                            dict = await SystemService.getDictionary(Kind);
                        }
                    } catch (error) {
                        console.log(
                            '%c getDictionary Caught Error',
                            'font-size:18px;color:red;font-weight:700;',
                            error
                        );
                    }

                    window.dictionaryCallbacks[KindKey].forEach(callback =>
                        callback(dict)
                    );
                    window.dictionaryCallbacks[KindKey] = null;
                    delete window.dictionaryCallbacks[KindKey];

                    // commit('SET_DICTIONARYCACHE', {
                    //     key: KindKey,
                    //     value: dict
                    // });
                    this.dictionaryCache[KindKey] = dict;
                    return dict;
                }
            }
        },
        /**
         * @method getDictionaryTreeCache 获取并缓存字典树
         * @param {String,Number} KindKey
         * @param { Boolean } force 是否强制刷新字典缓存
         * @returns {Promise.resolve(Array)}
         */
        async getDictionaryTreeCache (KindKey, force) {
            const existDictionaryTreeMap = Object.hasOwnProperty.call(DICTIONARY_MAP, KindKey);
            const Kind = existDictionaryTreeMap
                ? DICTIONARY_MAP[KindKey]
                : KindKey;
            if (
                !force &&
                this.dictionaryTreeCache[KindKey] &&
                this.dictionaryTreeCache[KindKey] instanceof Array &&
                this.dictionaryTreeCache[KindKey].length > 0
            ) {
                return this.dictionaryTreeCache[KindKey];
            } else {
                if (window.dictionaryTreeCallbacks?.[KindKey]) {
                    return new Promise(async (resolve, reject) => {
                        window.dictionaryTreeCallbacks[KindKey].push((res) =>
                            resolve(res)
                        );
                    });
                } else {
                    if (!window.dictionaryTreeCallbacks) {
                        window.dictionaryTreeCallbacks = {};
                    }
                    window.dictionaryTreeCallbacks[KindKey] = [];

                    let dictTree = [];

                    try {
                        if (KindKey && String(KindKey).match(/^CUSTOM_/)) {
                            const matchedAction = CUSTOM_DICT_MAP[Kind];
                            if (typeof matchedAction === 'function') {
                                dictTree = await matchedAction();
                            } else {
                                throw new Error('未找到匹配的自定义字典方法');
                            }
                        } else {
                            dictTree =
                                await SystemService.getDictionaryTree(Kind);
                            if (KindKey === 'DISTRICT') {
                                const getFilteredTree = (list) => {
                                    return !list
                                        ? []
                                        : cloneDeep(list).map((item) => {
                                            let ChildNodes = item.ChildNodes || [];

                                            // // 过滤区县级以下的行政区划
                                            // if (String(item.Value).length >= 6) {
                                            //     ChildNodes = null;
                                            // }

                                            ChildNodes = getFilteredTree(ChildNodes);
                                            if (!ChildNodes?.length) {
                                                delete item.ChildNodes;
                                            } else {
                                                item.ChildNodes = ChildNodes;
                                            }
                                            return item;
                                        });
                                };
                                dictTree = getFilteredTree(dictTree);
                            }
                        }
                    } catch (error) {
                        console.log(
                            '%c getDictionaryTree Caught Error',
                            'font-size:18px;color:red;font-weight:700;',
                            error
                        );
                    }

                    window.dictionaryTreeCallbacks[KindKey].forEach((callback) =>
                        callback(dictTree)
                    );
                    window.dictionaryTreeCallbacks[KindKey] = null;
                    delete window.dictionaryTreeCallbacks[KindKey];

                    // commit('SET_DICTIONARYTREECACHE', {
                    //     key: KindKey,
                    //     value: dictTree
                    // });
                    this.dictionaryTreeCache[KindKey] = dictTree;
                    return dictTree;
                }
            }
        }
    }
});

export default useDictionaryStore;
