import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

const prefix = `${AssetsAnalysisService}/assetDetails`;

export const LandResources = {
    /**
     * @method getLineDesc 所属线路
     * */
    getLineDesc () {
        return request.get(`${prefix}/getLineDesc`).then(
            ({ data }) => data
        );
    },
    /**
     * @method getLandDetailsList 土地资源明细
     * */
    getLandDetailsList (params) {
        return request.post(`${prefix}/getLandDetails`, params).then(
            ({ data }) => data
        );
    },
    /**
     * @method getResourceTypeAndLandCategory 资源类型和土地类别
     * */
    getResourceTypeAndLandCategory () {
        return request.get(`${prefix}/getResourceTypeAndLandCategory`).then(
            ({ data }) => data
        );
    },
    /**
     * @method exportLandDetails 资源类型和土地类别
     * */
    exportLandDetails (params) {
        return request.post(`${prefix}/exportLandDetails`, params, { responseType: 'blob' });
    }
};
