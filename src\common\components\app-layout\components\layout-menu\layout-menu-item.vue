<template>
    <el-sub-menu class="layout-menu-item sub-menu" :class="[{ nested, collapse }, direction]"
        v-bind="{ index: uniqKey }" v-if="existChildren">
        <template #title><ecp-icon class="layout-menu-item-icon" v-bind="icon" v-if="icon"></ecp-icon><span
                class="layout-menu-item-text" v-if="!collapse || nested && collapse">{{ label
                }}</span></template>
        <template #default>
            <template v-for="child in children" :key="getUniqKey(menuProps, child)">
                <LayoutMenuItem nested v-bind="{ menuItem: child, menuProps }" @select="emitSelectItem" />
            </template>
        </template>
    </el-sub-menu>
    <el-menu-item class="layout-menu-item trigger" v-bind="{ index: uniqKey }" @click="onSelectItem" v-else><ecp-icon
            class="layout-menu-item-icon" v-bind="icon" v-if="icon"></ecp-icon><span class="layout-menu-item-text"
            v-if="!collapse || nested && collapse">{{ label
            }}</span></el-menu-item>
</template>

<script setup>
import { MENU_ICON, MENU_PROPS } from '@constants/menu-config';

import LayoutMenuItem from './layout-menu-item.vue';

import { useMenu } from './use-menu';

defineComponent({
    name: 'layout-menu-item'
});

const props = defineProps({
    direction: {
        type: String,
        default: 'vertical'
    },
    menuItem: {
        type: Object,
        default: () => ({})
    },
    menuProps: {
        type: Object,
        default: () => MENU_PROPS
    },
    nested: Boolean
});

const emits = defineEmits(['select']);

const { getMenuValueByKey, getUniqKey, collapse } = useMenu();

const uniqKey = computed(() => {
    if (props?.menuItem?.FunTag === 'SYSTEM_MANAGEMENT') {
        // 特殊处理需要新窗口打开的页面不高亮菜单
        return null;
    } else {
        return getUniqKey(props.menuProps, props.menuItem);
    }
});

const label = computed(() => getMenuValueByKey(props.menuProps.label, props.menuItem));
const children = computed(() => getMenuValueByKey(props.menuProps.children, props.menuItem));
const existChildren = computed(() => !!children?.value?.length);

const icon = computed(() => !props.nested ? MENU_ICON[getMenuValueByKey(props.menuProps.funTag, props.menuItem)] : '');

const onSelectItem = (menuItemInstance) => {
    emitSelectItem({
        uniqKey,
        menuItem: props.menuItem,
        menuItemInstance
    });
};
const emitSelectItem = (...args) => {
    emits('select', ...args);
};

</script>

<style lang="scss" scoped>
$layout-menu-item: layout-menu-item;

:deep(.#{$layout-menu-item}:not(.nested)) {
    & > .elp-sub-menu__title {

        &,
        & > .#{$layout-menu-item}-text {
            color: var(--color-white);
        }
    }

}

:deep(.#{$layout-menu-item}.nested) {
    &:not(.collapse):not(.horizontal) {
        & > .elp-sub-menu__title {

            &,
            & > .#{$layout-menu-item}-text {
                color: var(--elp-menu-hover-text-color);
            }
        }
    }

    &.collapse,
    &.horizontal {
        --elp-menu-item-height: 36px;
        --elp-menu-sub-item-height: 36px;

        .elp-sub-menu__title:hover {
            background-color: var(--elp-menu-hover-bg-color);

            &,
            & > .#{$layout-menu-item}-text {
                color: var(--elp-menu-hover-text-color);
            }
        }

        &.is-active {

            > .elp-sub-menu__title,
            > .elp-sub-menu__title .#{$layout-menu-item}-text {
                color: var(--elp-menu-active-color);
            }
        }
    }
}

.#{$layout-menu-item} {

    .#{$layout-menu-item}-text {
        color: var(--elp-menu-text-color);
        position: relative;
    }

    .#{$layout-menu-item}-icon {
        color: var(--elp-menu-text-color);
        font-size: var(--font-size-large);
        margin-right: var(--spacer);
    }

    &.trigger {
        color: var(--color-white-opacity-2-5);

        &:hover {
            .#{$layout-menu-item}-text {
                color: var(--elp-menu-hover-text-color);
            }
        }

        &.is-active {
            .#{$layout-menu-item}-text {
                color: var(--elp-menu-active-color);
            }
        }
    }

}
</style>
