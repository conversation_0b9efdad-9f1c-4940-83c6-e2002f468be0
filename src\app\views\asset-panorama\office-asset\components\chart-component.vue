<template>
    <div class="chart-container">
        <ecp-chart-base-bar v-if="chartData?.source?.length" theme="whiteTheme" :data="chartData"
            :option="chartData.option" :legend="chartData.legend" :horizontal="horizontal" :unit="unit"
            :yName="yName" />
        <ecp-empty v-else />
    </div>
</template>

<script setup>
import { watch, onMounted } from 'vue';
import { CHART_COLOR_LIST } from '@constants/enum-config';
import { formatXAxis } from '@utils/format';

onMounted(() => {
    console.log(props.data);
});

const props = defineProps({
    data: {
        type: Object,
        default: () => ({
            dimensions: [],
            source: []
        })
    },
    chartType: {
        type: String,
        default: 'vertical'
    },
    unit: {
        type: String,
        default: '项'
    },
    yName: {
        type: String,
        default: '单位：项'
    }
});

const horizontal = computed(() => props.chartType === 'horizontal');

const chartData = reactive({
    dimensions: props.data.dimensions,
    source: props.data.source,
    legend: {
        show: false
    },
    option: {
        color: CHART_COLOR_LIST,
        xAxis: {
            nameTextStyle: {
                color: 'rgba(0,0,0,0.45)'
            },
            axisLabel: horizontal.value
                ? {
                    textStyle: {
                        color: 'rgba(0,0,0,0.45)',
                        fontFamily: 'D-DIN'
                    }
                }
                : {
                    rotate: 60,
                    formatter: (value) => formatXAxis(value,6),
                    interval: 0, // 强制显示所有坐标轴标签
                    color: 'rgba(0,0,0,0.45)'
                }
        },
        yAxis: {
            nameTextStyle: {
                padding: [0, -12, 0, 0]
            },
            axisLabel: horizontal.value
                ? {
                    rotate: 20,
                    formatter: (value) => formatXAxis(value,8),
                    color: 'rgba(0,0,0,0.45)'
                }
                : {
                    textStyle: {
                        color: 'rgba(0,0,0,0.45)',
                        fontFamily: 'D-DIN'
                    }
                }
        },
        grid: {
            top: horizontal.value ? 20 : 40,
            bottom: 0,
            left: 0,
            right: horizontal.value ? 30 : 0
        },
        series: [
            {
                type: 'bar',
                itemStyle: {
                    borderRadius: [10, 10, 10, 10]
                },
                barWidth: '15',
            }
        ]

    }
});

watch(
    () => props.data,
    (newData) => {
        chartData.dimensions = newData.dimensions;
        chartData.source = newData.source;
    },
    { deep: true }
);

</script>

<style scoped lang="scss">
.chart-container {
    width: 100%;
    height: 100%;
}
</style>
