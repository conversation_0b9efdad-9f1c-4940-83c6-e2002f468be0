<template>
    <div class="other-resources">
        <div class="left-legend">
            <div v-for="(item, index) in chartData" :key="index" class="chart-legend-item">
                <i class="icon" :style="{ backgroundColor: item.color }"></i>
                <div class="chart-legend-item-label">
                    <span class="chart-legend-item-text">{{ item.name }}</span>
                    <span class="chart-legend-item-rate font-number-bold" :title="item.value">{{ item.displayValue
                        }}</span>
                </div>
            </div>
        </div>
        <div class="right-chart" ref="pieChartRef"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import * as echarts from 'echarts';
import { formatBankNumber } from '@utils/format.js';

const props = defineProps({
    otherResourcesData: {
        type: Array,
        required: true
    }
});

const pieChartRef = ref(null);
let pieChartInstance = null;

const chartData = ref([]); // 自定义图例数据
const total = ref(0); // 总计字段

const generateColorPalette = (length) => {
    const COLORS = ['#0371ff', '#11be69', '#ffd022', '#e74c3c', '#8e44ad', '#3498db', '#2ecc71'];
    const colorPalette = [...COLORS];

    while (colorPalette.length < length) {
        const randomColor = `#${Math.floor(Math.random() * ********).toString(16)}`;
        if (!colorPalette.includes(randomColor)) {
            colorPalette.push(randomColor);
        }
    }

    return colorPalette.slice(0, length);
};

const updateChartData = (data) => {
    const filteredData = data.filter((item) => item.DataName !== '总计'); // 过滤掉 "总计"
    const colors = generateColorPalette(filteredData.length);
    const newChartData = filteredData.map((item, index) => {
        const valueStr = formatBankNumber(item.Percentage.toString());
        return {
            name: item.DataName,
            value: item.Percentage, // 原始值
            // color: ['#0371ff', '#11be69'][index % 2],
            color: colors[index],
            // displayValue: valueStr.length > 6
            //     ? valueStr.slice(0, 6) + '...'
            //     : valueStr,
            displayValue: valueStr,
        };
    });

    // 更新总计
    const totalField = data.find((item) => item.DataName === '总计');
    total.value = formatBankNumber(totalField ? totalField.Percentage : 0);
    chartData.value = newChartData;

    return newChartData;
};



const formatTotalValue = (value) => {
    const valueStr = value.toString();
    return valueStr.length > 6 ? valueStr.slice(0, 6) + '...' : valueStr;
    //return valueStr;
};



const calculateFontSize = () => {
    const width = window.innerWidth;
    if (width <= 768) {
        return { large: 16, small: 10 };
    }
    if (width <= 1380) {
        return { large: 24, small: 12 };
    }
    if (width <= 1600) {
        return { large: 30, small: 14 };
    }
    return { large: 36, small: 16 };
};


const initChart = (chartData) => {
    if (pieChartRef.value) {
        if (!pieChartInstance) {
            pieChartInstance = echarts.init(pieChartRef.value);
        }

        const fontSize = calculateFontSize();
        const dynamicColors = generateColorPalette(chartData.length);
        const pieOption = {
            tooltip: {
                textStyle: {
                    fontSize: 10
                },
                extraCssText:
                    'padding: 0; border-radius: 0.4em; border: 0; overflow: hidden;',
                backgroundColor: '#fff',
                formatter: (params) => {
                    const color = params.color;
                    return `
                    <div class="ecp-chart-tooltip-wrapper is-white">
                        <div class="ecp-chart-tooltip-head">${params.name}</div>
                        <div class="ecp-chart-tooltip-item">
                            <span class="ecp-chart-tooltip-label" style="--color: ${color}">数量</span>
                            <span class="ecp-chart-tooltip-value">
                                <i class="ecp-chart-tooltip-value-num">${params.value}</i>
                                <i class="ecp-chart-tooltip-value-unit">亩</i>
                            </span>
                        </div>
                    </div>`;
                }
            },
            series: [
                {
                    type: 'pie',
                    radius: ['50%', '70%'],
                    avoidLabelOverlap: false,
                    data: chartData,
                    itemStyle: {
                        borderWidth: 3,
                        borderColor: '#fff'
                    },
                    label: {
                        show: false // 隐藏环形边上的文字
                    },
                    labelLine: {
                        show: false // 隐藏指向标线
                    },
                    color: dynamicColors
                }
            ],
            graphic: [
                {
                    type: 'text',
                    left: 'center',
                    top: '40%',
                    style: {
                        text: `${formatTotalValue(total.value)}`,
                        textAlign: 'center',
                        fill: '#333',
                        fontSize: fontSize.large,
                        fontWeight: 700,
                        fontFamily: 'D-DIN-Bold',
                    },
                    tooltip: {
                        show: true,
                        formatter: () => ` <span class="ecp-chart-tooltip-value">
                                <i class="ecp-chart-tooltip-value-num" style="padding:10px">${total.value}</i>
                            </span>`,
                    },
                },
                {
                    type: 'text',
                    left: 'center',
                    top: '55%',
                    style: {
                        text: '总计',
                        textAlign: 'center',
                        fill: '#666',
                        fontSize: fontSize.small,
                        fontWeight: 'normal',
                        fontFamily: 'D-DIN',
                    },
                },
            ],
        };

        pieChartInstance.setOption(pieOption);
    }
};

watch(
    () => props.otherResourcesData,
    (newData) => {
        if (newData && newData.length > 0) {
            const updatedChartData = updateChartData(newData);
            initChart(updatedChartData);
        }
    },
    { immediate: true, deep: true }
);

const disposeChart = () => {
    if (pieChartInstance) {
        pieChartInstance.dispose();
        pieChartInstance = null;
    }
};

onMounted(() => {
    nextTick(() => {
        if (props.otherResourcesData.length > 0) {
            const chartData = updateChartData(props.otherResourcesData);
            initChart(chartData);
        }

        const resizeHandler = () => {
            if (pieChartInstance) {
                const chartData = updateChartData(props.otherResourcesData);
                initChart(chartData); // 重新渲染图表，调整字体大小
                pieChartInstance.resize();
            }
        };

        window.addEventListener('resize', resizeHandler);

        onBeforeUnmount(() => {
            disposeChart();
            window.removeEventListener('resize', resizeHandler);
        });
    });
});

</script>

<style scoped lang="scss">
.other-resources {
    display: flex;
    width: 100%;
    height: 100%;

    .left-legend {
        display: flex;
        flex-direction: column;
        flex: 1;
        justify-content: flex-start;
        padding-left: 20px;
        margin-top: var(--spacer-large-4);

        max-height: 80%;
        overflow-y: auto;

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
        }

        &::-webkit-scrollbar-track {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .chart-legend-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 16px;

            .icon {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                margin-right: 8px;
                flex-shrink: 0;
            }

            .chart-legend-item-label {
                display: flex;
                flex-direction: column;
                align-items: flex-start;

                .chart-legend-item-text {
                    line-height: 1.2;
                    font-size: 12px;
                    color: var(--color-text-secondary);
                    margin-bottom: 4px;
                }

                .chart-legend-item-rate {
                    font-size: var(--font-size-large);
                    color: var(--color-text-primary);
                }
            }
        }
    }

    .right-chart {
        flex: 2;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
