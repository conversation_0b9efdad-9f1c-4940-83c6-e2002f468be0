<template>
    <el-tooltip v-bind="tooltipAttrs" class="app-tips">
        <ecp-icon class="app-tips-icon" :icon="icon" />
        <template #content v-if="$slots.content">
            <slot name="content"></slot>
        </template>
    </el-tooltip>
</template>

<script setup>
defineComponent({
    name: 'app-tips'
});

const props = defineProps({
    content: String,
    placement: {
        type: String,
        default: 'bottom'
    },
    showDelay: {
        type: Number,
        default: 0
    },
    hideDelay: {
        type: Number,
        default: 200
    },
    effect: {
        type: String,
        default: 'dark'
    },
    icon: {
        type: String,
        default: '#ecp-icon-circle-info'
    }
});

const tooltipAttrs = computed(() => {
    const {
        content,
        placement,
        effect,
        showDelay: showAfter,
        hideDelay: hideAfter
    } = props;
    return {
        content,
        placement,
        effect,
        showAfter,
        hideAfter,
        popperClass: 'app-tips-popper'
    };
});
</script>

<style lang="scss" scoped>
.app-tips {
    &-icon {
        width: var(--font-size-base);
        height: var(--font-size-base);
        color: var(--color-text-secondary);
    }
}
</style>

<style lang="scss">
.app-tips-popper {
    max-width: 720px
}
</style>
