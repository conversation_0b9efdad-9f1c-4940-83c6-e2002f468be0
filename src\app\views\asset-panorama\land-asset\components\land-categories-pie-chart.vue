<template>
    <app-business-panel class="land-categories-pie-chart" title="土地大类比例" mode="card" v-loading="loading">
        <template #default>
            <app-chart-pie :graphData="graphData" :borderWidth="2" />
        </template>
    </app-business-panel>
</template>

<script setup>
import { LandAssetApi } from '@api/asset-panorama/land-asset';
import { CHART_COLOR_LIST } from '@constants/enum-config';

const loading = ref(false);

const graphData = ref([]);

const getChartData = async () => {
    console.log('%c getChartData', 'font-size:18px;color:gold;font-weight:700;');

    loading.value = true;
    graphData.value = [];

    try {
        const res = await LandAssetApi.getLandProportion();
        const list = (res || []).map((item, index) => ({
            name: item.DataName,
            value: item.Percentage ? Math.round(item.Percentage * 1000) / 1000 : 0, // 保留三位小数
            color: CHART_COLOR_LIST[index]
        }));

        graphData.value = list;
    } catch (error) {
        console.log('%c getTransferStatDataTrend Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    }

    loading.value = false;
};

onActivated(() => {
    getChartData();
});

</script>

<style scoped lang="scss">
.land-categories-pie-chart {}
</style>
