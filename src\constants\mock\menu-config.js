const context = 'asset-manage-web';
export default {
    about: [],
    menu: [
        {
            Text: '资产全景',
            Target: `/${context}/#/asset-panorama`,
            FunTag: 'ASSET_FULL_VIEW',
            ChildNodes: [
                {
                    Text: '概览',
                    Target: `/${context}/#/asset-panorama/overview`,
                    FunTag: 'ASSET_FULL_VIEW_OVERVIEW'
                },
                {
                    Text: '房产',
                    Target: `/${context}/#/asset-panorama/house-asset`,
                    FunTag: 'ASSET_FULL_VIEW_HOUSE'
                },
                {
                    Text: '土地',
                    Target: `/${context}/#/asset-panorama/land-asset`,
                    FunTag: 'ASSET_FULL_VIEW_LAND'
                },
                {
                    Text: '物业',
                    Target: `/${context}/#/asset-panorama/property-asset`,
                    FunTag: 'ASSET_FULL_VIEW_PROPERTY'
                },
                {
                    Text: '文体广告',
                    Target: `/${context}/#/asset-panorama/advertisement-asset`,
                    FunTag: 'ASSET_FULL_VIEW_ADVERTISEMENT'
                },
                {
                    Text: '运营资产',
                    Target: `/${context}/#/asset-panorama/operating-asset`,
                    FunTag: 'ASSET_FULL_VIEW_OPERATING'
                },
                {
                    Text: '办公资产',
                    Target: `/${context}/#/asset-panorama/office-asset`,
                    FunTag: 'ASSET_FULL_VIEW_OFFICE'
                }
            ]
        },
        {
            Text: '资产分析',
            Target: `/${context}/#/asset-analysis`,
            FunTag: 'ASSET_ANALYSIS',
            ChildNodes: [
                {
                    Text: '盘点分析',
                    Target: `/${context}/#/asset-analysis/inventory-analysis`,
                    FunTag: 'ASSET_ANALYSIS_INVENTORY'
                },
                {
                    Text: '线路资产分析',
                    Target: `/${context}/#/asset-analysis/line-asset-analysis`,
                    FunTag: 'ASSET_ANALYSIS_LINE'
                },
                {
                    Text: '办公资产分析',
                    Target: `/${context}/#/asset-analysis/office-asset-analysis`,
                    FunTag: 'ASSET_ANALYSIS_OFFICE'
                },
                {
                    Text: '统计报表',
                    Target: `/${context}/#/asset-analysis/statistical-report`,
                    FunTag: 'ASSET_ANALYSIS_REPORT'
                }
            ]
        },
        {
            Text: '资产明细',
            Target: `/${context}/#/asset-details`,
            FunTag: 'ASSET_DETAILS',
            ChildNodes: [
                {
                    Text: '房产资源',
                    Target: `/${context}/#/asset-details/house-resources`,
                    FunTag: 'ASSET_DETAILS_HOUSE'
                },
                {
                    Text: '土地资源',
                    Target: `/${context}/#/asset-details/land-resources`,
                    FunTag: 'ASSET_DETAILS_LAND'
                },
                {
                    Text: '其他经营资产',
                    Target: `/${context}/#/asset-details/other-assets`,
                    FunTag: 'ASSET_DETAILS_OTHER'
                },
                {
                    Text: '运营与办公资产',
                    Target: `/${context}/#/asset-details/operations-office-assets`,
                    FunTag: 'ASSET_DETAILS_OPERATIONS_OFFICE'
                },
                {
                    Text: '权证办理',
                    Target: `/${context}/#/asset-details/certificate-processing`,
                    FunTag: 'ASSET_DETAILS_CERTIFICATE'
                }
            ]
        },
        {
            Text: '数据采集',
            Target: `/${context}/#/data-acquisition`,
            FunTag: 'DATA_COLLECTION',
            ChildNodes: []
        },
        {
            Text: '系统管理',
            Target: '/common-frontend',
            FunTag: 'SYSTEM_MANAGEMENT',
            ChildNodes: []
        }
    ]
};
