<script setup>
import * as Api from '@api/index';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
import { REAL_ESTATE_RESOURCES_TABLE_CONFIG } from '../constants';
import dayjs from 'dayjs';

const props = defineProps({
    disabledFutureDates: {
        type: Function,
        default: () => true
    }
});
const _createDate = ref(new Date());
const loading = ref(false);
const tableData = ref([]);
const ExportEvent = async () => {
    try {
        if (tableData.value.length > 0) {
            const createDate = dayjs(_createDate.value).format('YYYY-MM');
            const response = await Api.AssetAnalysis.ExportRealEstateTable({ createDate });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (e) {
        console.log('%c ExportEvent', 'font-size:18px;color:green;font-weight:700;', e);
        ElMessage.error('导出失败');
    }
};

const GetRealEstateResourcesTable = async () => {
    try {
        loading.value = true;
        const createDate = dayjs(_createDate.value).format('YYYY-MM');
        const response = await Api.AssetAnalysis.ShowRealEstateTable({ createDate });
        tableData.value = response.Data;
    } catch (e) {
        console.log('%c GetRealEstateResourcesTable', 'font-size:18px;color:green;font-weight:700;', e);
    } finally {
        loading.value = false;
    }
};

onMounted(GetRealEstateResourcesTable);
</script>

<template>
    <div class="real-estate-resources" style="height: 100%;width: 100%;">
        <ecp-layout-pagination>
            <template #head>
                <div class="header-toolbar">
                    <div class="header-toolbar__selector">
                        <el-date-picker v-model="_createDate" type="month" placeholder="选择年月"
                            :disabled-date="disabledFutureDates" style="width: 150px" :clearable="false"
                            @change="GetRealEstateResourcesTable" />
                    </div>
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading" @click="ExportEvent" />
                </div>
            </template>
            <template #content>
                <app-dynamic-table :loading="loading" :table-data="tableData"
                    :table-config="REAL_ESTATE_RESOURCES_TABLE_CONFIG" />
            </template>
        </ecp-layout-pagination>
    </div>
</template>

<style scoped lang="scss"></style>
