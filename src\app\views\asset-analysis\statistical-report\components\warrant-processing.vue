<!-- 权证办理 -->
<template>
    <div class="warrant-processing">
        <ecp-layout-pagination :total="total" v-model:current-page="localCurrentPage" v-model:page-size="localPageSize"
            layout="prev, pager, next, sizes, jumper" @current-change="handlePageChange">
            <template #head>
                <div class="header-toolbar">
                    <div class="header-toolbar__selector">
                        <el-date-picker v-model="_createDate" type="month" placeholder="选择年月"
                            :disabled-date="disabledFutureDates" style="width: 150px" :clearable="false"
                            @change="GetCertificateHandlingTable" />
                    </div>
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading" @click="ExportEvent" />
                </div>
            </template>
            <template #content>
                <app-dynamic-table :tableData="tableData" :tableConfig="WARRANT_PROCESSING_TABLE_CONFIG"
                    :pageConfig="pageConfig" :loading="loading" style="height: calc(100vh - 285x);">
                </app-dynamic-table>
            </template>
        </ecp-layout-pagination>
    </div>
</template>

<script setup>
import * as Api from '@api/index';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
import { WARRANT_PROCESSING_TABLE_CONFIG } from '../constants';
import { StatisticalReport } from '@api/asset-analysis/statistical-report';

import dayjs from 'dayjs';

const props = defineProps({
    disabledFutureDates: {
        type: Function,
        default: () => true
    }
});

const total = ref('');
const localCurrentPage = ref('1');
const localPageSize = ref('10');

const _createDate = ref(new Date());
const loading = ref(false);
const tableData = ref([]);
// const queryTime = dayjs(_createDate.value).format('YYYY-MM');
const queryTime = computed(() => {
    return dayjs(_createDate.value).format('YYYY-MM');
});
const ExportEvent = async () => {
    try {
        if (total.value > 0) {
            const response = await StatisticalReport.ExportCertificateHandlingTable({ queryTime: queryTime.value });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (e) {
        console.log('%c ExportEvent', 'font-size:18px;color:green;font-weight:700;', e);
        ElMessage.error('导出失败');
    }
};

const GetCertificateHandlingTable = async () => {
    try {
        loading.value = true;
        const pageNum = parseInt(localCurrentPage.value, 10);
        const pageSize = parseInt(localPageSize.value, 10);
        const response = await StatisticalReport.ShowCertificateHandlingTable({
            queryTime: queryTime.value,
            pageNum,
            pageSize
        });
        tableData.value = response.Data;
        total.value = response.Total;
        localCurrentPage.value = response.PageNum;
        localPageSize.value = response.PageSize;
    } catch (e) {
        console.log('%c GetCertificateHandlingTable', 'font-size:18px;color:green;font-weight:700;', e);
    } finally {
        loading.value = false;
    }
};

const handlePageChange = (page) => {
    localCurrentPage.value = page;
    GetCertificateHandlingTable();
};

watch(localPageSize, () => {
    GetCertificateHandlingTable();
});

onMounted(GetCertificateHandlingTable);
</script>

<style scoped lang="scss">
.warrant-processing {
    width: 100%;
    height: 100%;
}
</style>
