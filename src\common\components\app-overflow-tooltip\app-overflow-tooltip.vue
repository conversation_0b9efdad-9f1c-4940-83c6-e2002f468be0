<template>
    <div class="app-overflow-tooltip" :class="{ 'multi-line': multipleLine }">
        <div class="content-container" ref="wrapperRef" v-resize="onResize">
            <span class="raw-content" ref="contentRef" v-if="!tooltipVisible">
                <slot>{{ content }}</slot>
            </span>

            <el-popover class="overflow-popover" :placement="placement" :visible-arrow="false" trigger="hover"
                :popper-class="poperFormatClass" v-if="tooltipVisible">
                <template #default>
                    <slot>{{ content }}</slot>
                </template>
                <template #reference>
                    <div class="overflow-popover-reference"
                        :class="[multipleLine ? 'multiple-ellipsis' : 'single-ellipsis']"
                        :style="{ '-webkit-line-clamp': lineClamp }">
                        <span class="raw-content" ref="contentRef">
                            <slot>{{ content }}</slot>
                        </span>
                    </div>
                </template>
            </el-popover>
        </div>
    </div>
</template>

<script setup>
import { nextTick, onMounted } from 'vue';

defineComponent({
    name: 'app-overflow-tooltip'
});

const props = defineProps({
    content: String,
    placement: {
        type: String,
        default: 'right'
    },
    multipleLine: {
        type: Boolean,
        default: false
    },
    lineClamp: {
        type: Number,
        default: 2
    },
    poperClass: {
        type: Array,
        default: () => []
    }
});

const tooltipVisible = ref(false);
const poperFormatClass = computed(() =>
    ['app-cell-tooltip-popover shadow-tooltip', ...props.poperClass].join(' ')
);

watch(
    () => props.content,
    () => {
        judgeTooltipVisible();
    }
);

const wrapperRef = ref();
const contentRef = ref();
const slots = useSlots();

const judgeTooltipVisible = () => {
    nextTick(() => {
        requestAnimationFrame(() => {
            const wrapperHeight = wrapperRef.value?.offsetHeight || 0;
            const contentHeight = contentRef.value?.offsetHeight || 0;
            const heightOverflow = wrapperHeight < contentHeight;

            const wrapperWidth = wrapperRef.value?.offsetWidth || 0;
            const contentWidth = contentRef.value?.offsetWidth || 0;
            const widthOverflow = wrapperWidth < contentWidth;

            tooltipVisible.value = slots.default
                ? widthOverflow || contentWidth
                : props.multipleLine
                    ? heightOverflow
                    : widthOverflow;
        });
    });
};

let triggerTimeout = null;
const onResize = () => {
    triggerTimeout && clearTimeout(triggerTimeout);
    triggerTimeout = setTimeout(() => {
        judgeTooltipVisible();
    }, 300);
};

onMounted(() => {
    judgeTooltipVisible();
});
</script>

<style lang="scss" scoped>
.app-overflow-tooltip {
    flex-grow: 1;
    overflow: hidden;

    &:not(.multi-line) {
        white-space: nowrap;
    }

    .content-container {
        .overflow-popover-reference {
            &.single-ellipsis {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            &.multiple-ellipsis {
                display: -webkit-box;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
        }
    }

    &.multi-line {
        .overflow-popover-reference {
            white-space: pre-wrap;
        }
    }
}
</style>
