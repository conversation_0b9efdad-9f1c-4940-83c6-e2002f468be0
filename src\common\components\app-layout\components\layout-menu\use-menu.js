import { useStorage } from '@vueuse/core';
import { isObject } from 'lodash-es';

const activeMenu = ref();

const collapse = useStorage('menu-collapse', false);

export const useMenu = () => {
    const getMenuValueByKey = (key, data) => {
        if (!isObject(data)) return null;
        if ((typeof key !== 'string' &&
            typeof key !== 'number') ||
            (!key && key !== 0)) {
            return null;
        }
        const keys = `${key}`.split('.');
        let result = null;
        for (let index = 0; index < keys.length; index++) {
            const keyItem = keys[index];
            if (index === 0) {
                result = data;
            }
            if (result) {
                result = result[keyItem];
            } else {
                result = null;
            }
        }
        return result;
    };

    const getUniqKey = (menuProps, data) => {
        return data[menuProps.id] || data[menuProps.route] || data[menuProps.url];
    };

    return {
        activeMenu,
        getMenuValueBy<PERSON>ey,
        getUniq<PERSON>ey,
        collapse
    };
};
