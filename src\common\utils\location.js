/**
 * @constant { Object } locationTransformUtils 坐标转换关联方法集合
 */
export const locationTransformUtils = {
    PI: Math.PI,
    BD_FACTOR: (Math.PI * 3000.0) / 180.0,
    /* eslint-disable-next-line no-loss-of-precision */
    EE: 0.00669342162296594323, // 椭球的偏心率平
    delta (lat, lon) {
        /**
         * <PERSON> 1940
         *
         * a = 6378245.0, 1/f = 298.3
         * b = a * (1 - f)
         * ee = (a^2 - b^2) / a^2;
         */
        const a = 6378245.0; //  a: 卫星椭球坐标投影到平面地图坐标系的投影因子。

        /* eslint-disable-next-line no-loss-of-precision */
        const ee = 0.00669342162296594323; //  ee: 椭球的偏心率。

        let dLat = this.transformLat(lon - 105.0, lat - 35.0);
        let dLon = this.transformLon(lon - 105.0, lat - 35.0);
        const radLat = (lat / 180.0) * this.PI;
        let magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        const sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / (((a * (1 - ee)) / (magic * sqrtMagic)) * this.PI);
        dLon = (dLon * 180.0) / ((a / sqrtMagic) * Math.cos(radLat) * this.PI);
        return {
            lat: dLat,
            lon: dLon
        };
    },
    outOfChina (lat, lon) {
        if (lon < 72.004 || lon > 137.8347) return true;
        if (lat < 0.8293 || lat > 55.8271) return true;
        return false;
    },
    transformLat (x, y) {
        let ret =
            -100.0 +
            2.0 * x +
            3.0 * y +
            0.2 * y * y +
            0.1 * x * y +
            0.2 * Math.sqrt(Math.abs(x));
        ret +=
            ((20.0 * Math.sin(6.0 * x * this.PI) +
                20.0 * Math.sin(2.0 * x * this.PI)) *
                2.0) /
            3.0;
        ret +=
            ((20.0 * Math.sin(y * this.PI) + 40.0 * Math.sin((y / 3.0) * this.PI)) *
                2.0) /
            3.0;
        ret +=
            ((160.0 * Math.sin((y / 12.0) * this.PI) +
                320 * Math.sin((y * this.PI) / 30.0)) *
                2.0) /
            3.0;
        return ret;
    },
    transformLon (x, y) {
        let ret =
            300.0 +
            x +
            2.0 * y +
            0.1 * x * x +
            0.1 * x * y +
            0.1 * Math.sqrt(Math.abs(x));
        ret +=
            ((20.0 * Math.sin(6.0 * x * this.PI) +
                20.0 * Math.sin(2.0 * x * this.PI)) *
                2.0) /
            3.0;
        ret +=
            ((20.0 * Math.sin(x * this.PI) + 40.0 * Math.sin((x / 3.0) * this.PI)) *
                2.0) /
            3.0;
        ret +=
            ((150.0 * Math.sin((x / 12.0) * this.PI) +
                300.0 * Math.sin((x / 30.0) * this.PI)) *
                2.0) /
            3.0;
        return ret;
    }
};

/**
 * @method wgs84ToGcj02 GPS地图坐标(WGS-84)转换为国测局坐标(GCJ-02)
 *
 * @param { Coordinate } wgsCoordinate GPS地图坐标(WGS-84)
 *
 * @return { Coordinate } 国测局坐标(GCJ-02)
 */
export const wgs84ToGcj02 = (wgsCoordinate) => {
    const longitude = Array.isArray(wgsCoordinate) ? wgsCoordinate[0] : wgsCoordinate.longitude;
    const latitude = Array.isArray(wgsCoordinate) ? wgsCoordinate[1] : wgsCoordinate.latitude;

    const d = locationTransformUtils.delta(
        latitude,
        longitude
    );

    const gcjCoo = {
        longitude: longitude + d.lon,
        latitude: latitude + d.lat
    };
    return gcjCoo;
};

/**
 * @method gcj02ToWgs84 国测局坐标(GCJ-02)转换为GPS地图坐标(WGS-84)
 *
 * @param { Coordinate } gcjCoordinate 国测局坐标
 *
 * @return { Coordinate } GPS地图坐标(WGS-84)
 */
export const gcj02ToWgs84 = (gcjCoordinate) => {
    const longitude = Array.isArray(gcjCoordinate) ? gcjCoordinate[0] : gcjCoordinate.longitude;
    const latitude = Array.isArray(gcjCoordinate) ? gcjCoordinate[1] : gcjCoordinate.latitude;

    const d = locationTransformUtils.delta(
        latitude,
        longitude
    );

    const wgsCoo = {
        longitude: longitude - d.lon,
        latitude: latitude - d.lat
    };
    return wgsCoo;
};

/**
 * @method gcj02ToWgs84Exact 国测局坐标(GCJ-02)精确转换为GPS地图坐标(WGS-84)
 *
 * @param { Coordinate } gcjCoordinate 国测局坐标
 *
 * @return { Coordinate } GPS地图坐标(WGS-84)
 */
export const gcj02ToWgs84Exact = (gcjCoordinate) => {
    const longitude = Array.isArray(gcjCoordinate) ? gcjCoordinate[0] : gcjCoordinate.longitude;
    const latitude = Array.isArray(gcjCoordinate) ? gcjCoordinate[1] : gcjCoordinate.latitude;

    const initDelta = 0.01;
    const threshold = 0.000000001;
    let dLat = initDelta;
    let dLon = initDelta;
    let mLat = latitude - dLat;
    let mLon = longitude - dLon;
    let pLat = latitude + dLat;
    let pLon = longitude + dLon;
    let wgsLat;
    let wgsLon;
    let i = 0;
    while (1) {
        wgsLat = (mLat + pLat) / 2;
        wgsLon = (mLon + pLon) / 2;
        const tmp = wgs84ToGcj02({
            longitude: wgsLon,
            latitude: wgsLat
        });
        dLat = tmp.latitude - latitude;
        dLon = tmp.longitude - longitude;
        if (Math.abs(dLat) < threshold && Math.abs(dLon) < threshold) break;

        if (dLat > 0) pLat = wgsLat;
        else mLat = wgsLat;
        if (dLon > 0) pLon = wgsLon;
        else mLon = wgsLon;

        if (++i > 10000) break;
    }
    const wgsCoo = {
        longitude: wgsLon,
        latitude: wgsLat
    };
    return wgsCoo;
};

/**
 * @method bd09ToGcj02 百度地图坐标(BD-09)转换为国测局坐标(GCJ-02)
 *
 * @param { Coordinate } bdCoordinate 百度地图坐标
 *
 * @return { Coordinate } 国测局坐标(GCJ-02)
 */
export const bd09ToGcj02 = (bdCoordinate) => {
    const longitude = Array.isArray(bdCoordinate) ? bdCoordinate[0] : bdCoordinate.longitude;
    const latitude = Array.isArray(bdCoordinate) ? bdCoordinate[1] : bdCoordinate.latitude;

    const { BD_FACTOR } = locationTransformUtils;

    const x = Number(longitude) - 0.0065;
    const y = Number(latitude) - 0.006;
    const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * BD_FACTOR);
    const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * BD_FACTOR);
    const lngs = z * Math.cos(theta);
    const lats = z * Math.sin(theta);

    const gcjCoo = {
        longitude: lngs,
        latitude: lats
    };
    return gcjCoo;
};

/**
 * @method gcj02ToBd09 国测局坐标(GCJ-02)转换为百度地图坐标(BD-09)
 *
 * @param { Coordinate } gcjCoordinate 国测局坐标
 *
 * @return { Coordinate } 百度地图坐标(BD-09)
 */
export const gcj02ToBd09 = (gcjCoordinate) => {
    const longitude = Array.isArray(gcjCoordinate) ? gcjCoordinate[0] : gcjCoordinate.longitude;
    const latitude = Array.isArray(gcjCoordinate) ? gcjCoordinate[1] : gcjCoordinate.latitude;

    const { BD_FACTOR } = locationTransformUtils;

    const x = Number(longitude);
    const y = Number(latitude);
    const z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * BD_FACTOR);
    const theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * BD_FACTOR);

    const bdLng = z * Math.cos(theta) + 0.0065;
    const bdLat = z * Math.sin(theta) + 0.006;

    const bdCoo = {
        longitude: bdLng,
        latitude: bdLat
    };
    return bdCoo;
};

/**
 * @method getDistance 计算坐标距离
 *
 * @param { Coordinate } destOrgCoo 目标位置坐标
 * @param { Coordinate } currCoo 当前位置坐标
 * @param { String } coorTransferType 坐标转换类型
 *
 * @return { Number } 坐标距离，单位：米
 */
export const getDistance = (
    destOrgCoo,
    currCoo,
    coorTransferType = ''
) => {
    function Rad (d) {
        return (d * Math.PI) / 180.0;
    }

    const destLongitude = +(Array.isArray(destOrgCoo) ? destOrgCoo[0] : destOrgCoo.longitude);
    const destLatitude = +(Array.isArray(destOrgCoo) ? destOrgCoo[1] : destOrgCoo.latitude);
    const currLongitude = +(Array.isArray(currCoo) ? currCoo[0] : currCoo.longitude);
    const currLatitude = +(Array.isArray(currCoo) ? currCoo[1] : currCoo.latitude);

    const coorTransferTypeMap = {
        wgs84ToGcj02,
        gcj02ToWgs84,
        gcj02ToWgs84Exact,
        bd09ToGcj02,
        gcj02ToBd09
    };
    const destCoo =
        coorTransferType && coorTransferTypeMap[coorTransferType]
            ? coorTransferTypeMap[coorTransferType]({
                longitude: destLongitude,
                latitude: destLatitude
            })
            : {
                longitude: destLongitude,
                latitude: destLatitude
            };
    console.log(
        '%c [@ecp/ecp-ui-plus] getDistance - Transfered Coordinate',
        'font-size:18px;color:blue;font-weight:700;',
        destCoo
    );

    const radLat1 = Rad(destCoo.latitude);
    const radLat2 = Rad(currLatitude);
    const a = radLat1 - radLat2;
    const b = Rad(destCoo.longitude) - Rad(currLongitude);
    let s =
        2 *
        Math.asin(
            Math.sqrt(
                Math.sin(a / 2) ** 2 +
                Math.cos(radLat1) * Math.cos(radLat2) * Math.sin(b / 2) ** 2
            )
        );
    s *= 6378.137; // EARTH_RADIUS
    s = Math.round(s * 10000) / 10;
    console.log(
        '%c [@ecp/ecp-ui-plus] getDistance - Distance(meter)',
        'font-size:18px;color:blue;font-weight:700;',
        s
    );
    // 返回位置间距离，单位：米
    return s;
};
