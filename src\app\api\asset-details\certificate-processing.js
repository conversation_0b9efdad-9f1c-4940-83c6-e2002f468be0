import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

const prefix = `${AssetsAnalysisService}/assetDetails`;

export const Certificate = {
    // 查询权证办理统计表
    getCertificateOfRights (data) {
        return request.post(`${prefix}/getCertificateOfRightsTable`, data).then(
            ({ data }) => data
        );
    },

    // 获取所属路线信息
    getLineDesc1 (params) {
        return request.get(`${prefix}/getCertificateOfRightsLine`, params).then(
            ({ data }) => data
        );
    },

    // 导出权证明细列表
    exportCertificateOfRightsTable (data) {
        return request.post(`${prefix}/exportCertificateOfRightsTable`, data, { responseType: 'blob' });
    }
};
