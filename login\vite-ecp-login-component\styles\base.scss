@use "./config.scss" as *;

[class*="ecpp-login-component"] {
    --ecpp-login-color-white: var(--color-white, #fff);
    --ecpp-login-color-black: var(--color-black, #000);
    --ecpp-login-color-primary: var(--color-primary, #1890ff);
    --ecpp-login-color-danger: var(--color-danger, #f5222d);

    --ecpp-login-border-color-base: var(--border-color-base, rgba(0, 0, 0, 15%));
    --ecpp-login-border-color-light: var(--border-color-light,
            rgba(0, 0, 0, 9%));
    --ecpp-login-border-base: var(--border-base,
            1px solid var(--ecpp-login-border-color-base));
    --ecpp-login-border-light: var(--border-base,
            1px solid var(--ecpp-login-border-color-light));

    --ecpp-login-background-base: var(--background-color-dialog, #fff);

    --ecpp-login-text-primary: var(--color-text-primary, rgba(0, 0, 0, 85%));
    --ecpp-login-text-regular: var(--color-text-regular, rgba(0, 0, 0, 65%));
    --ecpp-login-text-secondary: var(--color-text-secondary, rgba(0, 0, 0, 45%));
    --ecpp-login-text-placeholder: var(--color-text-placeholder, rgba(0, 0, 0, 25%));
}

[class*="ecpp-login-component"] {
    .#{$namespace}-dialog__header {
        // padding: 16px 24px;
        // border-bottom: var(--ecpp-login-border-light);

        .#{$namespace}-dialog__title {
            color: var(--ecpp-login-text-primary);
            font-size: 16px;
            line-height: 22px;
        }
    }

    .#{$namespace}-dialog__footer {
        border-top: 1px solid var(--ecpp-login-border-color-light);
        padding: 8px 24px !important;
    }

    /* 表单样式 */
    .app-form {
        .#{$namespace}-form-item {
            display: flex;
            align-items: center;
            margin: 0 0 32px 0;

            &__label {
                min-width: 90px;
                padding: 0;
                flex: 0 0 90px;
            }

            &__content {
                flex: auto;

                .#{$namespace}-select,
                .#{$namespace}-cascader,
                .#{$namespace}-date-editor {
                    width: 100%;
                    display: block;
                }

                .#{$namespace}-checkbox-group {
                    line-height: 1;
                }
            }
        }
    }

    .#{$namespace}-form {
        box-sizing: border-box;
        width: 100%;

        .#{$namespace}-input {
            &__inner {
                width: 100%;
                display: flex;
                align-items: center;
            }

            &--small {
                --#{$namespace}-input-height: 32px;
                font-size: 12px;
            }

            &__prefix-inner {
                line-height: 1em;
            }
        }

        .#{$namespace}-form-item--medium {
            margin-bottom: 24px;

            .#{$namespace}-input__prefix > .#{$namespace}-input__icon,
            .icon {
                font-size: 18px;
            }

            .icon {
                top: 30%;
            }
        }

        .#{$namespace}-form-item--small {
            margin-bottom: 24px;

            .#{$namespace}-input__prefix > .#{$namespace}-input__icon,
            .icon {
                font-size: 16px;
            }

            .icon {
                top: 27%;
            }
        }

        .#{$namespace}-form-item--mini {
            margin-bottom: 16px;

            .#{$namespace}-input__prefix > .#{$namespace}-input__icon,
            .icon {
                font-size: 14px;
            }

            .icon {
                top: 25%;
            }
        }
    }
}