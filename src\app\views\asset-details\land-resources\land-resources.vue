<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <!-- <template #head-right>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template> -->
        <!-- <template #content> -->
            <el-card class="card">
                <ecp-layout-pagination content-scroll :total="tableData.total" v-model:current-page="pagination.pageNum"
                    v-model:page-size="pagination.pageSize" :top-pagination="false"
                    layout="prev, pager, next, sizes, jumper" @query="getLandDetailsList()">
                    <template #head>

                    </template>
                    <template #content>
                        <div class="header-toolbar">
                            <el-form inline v-model="searchForm" ref="formRef" style="display: flex;flex-wrap: nowrap;">
                                <el-form-item label="资源类型" prop="resourceType">
                                    <el-select v-model="searchForm.resourceType">
                                        <el-option v-for="item in resourceTypeOptions" :key="item" :label="item"
                                            :value="item" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="土地类别" prop="landCategory">
                                    <el-select v-model="searchForm.landCategory">
                                        <el-option v-for="item in landCategoryOptions" :key="item" :label="item"
                                            :value="item" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="所属线路" prop="line">
                                    <el-select v-model="searchForm.line">
                                        <el-option v-for="item in lineDescOptions" :key="item.LineCode"
                                            :label="item.LineName" :value="item.LineName" />
                                    </el-select>
                                </el-form-item>
                            </el-form>
                            <div class="header-toolbar-right">
                                    <ecp-button type="primary" text="查询" @click="SearchEvent" />
                                    <ecp-button text="重置" @click="ResetEvent" />
                                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading.export"
                                        @click="ExportEvent" />
                            </div>
                        </div>
                        <app-dynamic-table :loading="loading.table" :table-data="tableData.list"
                            :table-config="LAND_RESOURCES_TABLE" style="height: calc(100vh - 220px);"/>
                    </template>
                </ecp-layout-pagination>
            </el-card>
        <!-- </template> -->
    </app-form-page>
</template>

<script setup>
import dayjs from 'dayjs';
import { LAND_RESOURCES_TABLE } from '../_constants/index';
import * as Api from '@api/index';
import { ElMessage } from 'element-plus';
import { downloadBlobData } from '@utils/download';
const name = 'asset-details-land-resources';
defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '土地资源'
        }
    ];
});
// 获取表格数据
const getLandDetailsList = async () => {
    try {
        loading.table = true;
        const response = await Api.AssetDetails.getLandDetailsList({ ...searchForm, ...pagination });
        const { Total, Data } = response;
        tableData.list = Data;
        tableData.total = Total;
    } catch (error) {
        console.log(error);
        tableData.list = [];
        tableData.total = 0;
    } finally {
        loading.table = false;
    }
};
const resourceTypeOptions = ref([]);
const landCategoryOptions = ref([]);
// 获取土地类别和资源类型
const getResourceTypeAndLandCategory = async () => {
    try {
        const { Data } = await Api.AssetDetails.getResourceTypeAndLandCategory();
        resourceTypeOptions.value = Data[0]['资源类型'] || [];
        landCategoryOptions.value = Data[0]['土地类别'] || [];
    } catch (error) {
        console.log(error);
    }
};
const lineDescOptions = ref([]);
// 获取所属线路
const getLineDesc = async () => {
    try {
        const { Data } = await Api.AssetDetails.getLineDesc();
        lineDescOptions.value = Data;
        // 后端要求把这个接口数据中的21号线，转为 阳逻线
        lineDescOptions.value.forEach((item) => {
            if (item.LineCode === '21') {
                item.LineName = '阳逻线';
            }
        });
    } catch (error) {
    }
};
onMounted(() => {
    getLandDetailsList();
    getResourceTypeAndLandCategory();
    getLineDesc();
});

const total = ref(0);
const formRef = ref(null);
const loading = reactive({
    table: false,
    export: false
});
const searchForm = reactive({
    resourceType: '', // 资源类型
    line: '', // 所属路线
    landCategory: ''// 土地类别
});
const pagination = reactive({
    pageSize: 10,
    pageNum: 1
});
const tableData = reactive({
    total: 0,
    list: []
});

const SearchEvent = async () => {
    pagination.pageNum = 1;
    pagination.pageSize = 10;
    getLandDetailsList();
};

const ResetEvent = async () => {
    searchForm.landCategory = '';
    searchForm.line = '';
    searchForm.resourceType = '';
    pagination.pageNum = 1;
    pagination.pageSize = 10;
    getLandDetailsList();
};
// 导出
const ExportEvent = async () => {
    try {
        loading.export = true;
        if (tableData.total > 0) {
            const response = await Api.AssetDetails.exportLandDetails({ ...searchForm, pageNum: 1, pageSize: 10000 });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (error) {
        console.log(error);
    } finally {
        loading.export = false;
    }
};
</script>

<style lang="scss" scoped>
$page-name: asset-details-land-resources;

.#{$page-name} {
    :deep(.elp-select) {
        width: 120px;
    }
    .card {
        flex: 1 1 auto;
        display: flex;
        overflow: auto;
        height: 1vh;
        :deep(.elp-card__body) {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            padding-top: 0;
            width: 100%;
        }
        .header-toolbar{
            display: flex;
            .header-toolbar-right{
                display: flex;
                flex-grow: 1;
                justify-content: flex-end;
            }
        }
    }
}
</style>
