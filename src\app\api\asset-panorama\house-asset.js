import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

const prefix = `${AssetsAnalysisService}/estate`;

export const HouseAsset = {
    /**
     * 房产资产
     * @method DeliveryTrends 还建房交楼趋势
     */
    DeliveryTrends () {
        return request.get(`${prefix}/deliveryTrends`).then(
            ({ data }) => data
        );
    },
    /**
     * 房产资产
     * @method DemolitionTrends 已征未拆拆迁趋势
     */
    DemolitionTrends () {
        return request.get(`${prefix}/demolitionTrends`).then(
            ({ data }) => data
        );
    },
    /**
     * 房产资产
     * @method RealEstateTrends 房产趋势
     */
    RealEstateTrends () {
        return request.get(`${prefix}/realEstateTrends`).then(
            ({ data }) => data
        );
    },
    /**
     * 房产资产
     * @method RealEstateType 房产类型
     */
    RealEstateType () {
        return request.get(`${prefix}/realEstateType`).then(
            ({ data }) => data
        );
    },
    /**
     * 房产资产
     * @method RentalTrends 保障性租赁用房租赁趋势
     */
    RentalTrends () {
        return request.get(`${prefix}/rentalTrends`).then(
            ({ data }) => data
        );
    },
    /**
     * 房产资产
     * @method RoomSalesTrends 住宅销售趋势
     */
    RoomSalesTrends () {
        return request.get(`${prefix}/roomSalesTrends`).then(
            ({ data }) => data
        );
    }
};
