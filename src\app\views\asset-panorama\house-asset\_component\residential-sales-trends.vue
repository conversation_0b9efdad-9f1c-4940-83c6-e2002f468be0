<script setup>
import * as Api from '@api/index';

const loading = ref(false);
const option = {
    color: ['#0367FC', '#11B062', '#487690'],
    legend: {
        show: false
    },
    grid: {
        top: '25px'
    },
    xAxis: {
        axisLabel: {
            textStyle: {
                color: 'rgba(0, 0, 0, 0.45)',
                fontFamily: 'D-DIN'
            }
        }
    },
    yAxis: {
        nameTextStyle: {
            padding: [0, -40, 0, 0]
        }
    },
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    series: [
        {
            type: 'line',
            itemStyle: {
                borderColor: '#fff',
                borderWidth: 2
            },
            lineStyle: {
                width: 4
            },
            symbolSize: 8,
            showSymbol: true
        }
    ]
};
const data = reactive({
    dimensions: ['日期', '出售总数'],
    source: []
});
const total = ref(0);

const RoomSalesTrends = async () => {
    try {
        loading.value = true;
        const response = await Api.AssetPanorama.RoomSalesTrends();
        response.Data.forEach(item => {
            data.source.push({
                日期: item.Date,
                出售总数: item.UDBuArea
            });
            total.value += item.UDBuArea;
        });
        data.source.reverse();
    } catch (error) {
        console.log('%c RoomSalesTrends Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    } finally {
        loading.value = false;
    }
};

onMounted(RoomSalesTrends);
</script>

<template>
    <app-business-panel mode="card" title="住宅销售趋势">
        <template #content>
            <div class="residential-sales-trends" v-loading="loading">
                <div class="total-value font-number-bold">{{ total.toFixed(2) }}</div>
                <ecp-chart-base-line theme="whiteTheme" :data="data" :option="option" yName="单位：万平方" :lineWidth="4" />
            </div>
        </template>
    </app-business-panel>
</template>

<style scoped lang="scss">
.residential-sales-trends {
    .total-value {
        margin-top: 12px;
        font-size: 30px;
        line-height: 30px;
        letter-spacing: 0.8px;
        color: var(--elp-text-color-primary);
    }
}
</style>
