<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <template #head-right>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template>
        <template #content>
            <el-card class="container">
                <div class="tab-container">
                    <el-tabs class="app-detail-tabs tabs-only" v-model="activeTab" style="margin-bottom: 20px;">
                        <el-tab-pane label="资产变化情况" name="tab1"></el-tab-pane>
                        <el-tab-pane label="资产使用情况" name="tab2"></el-tab-pane>
                        <el-tab-pane label="线路设备使用情况" name="tab3"></el-tab-pane>
                        <el-tab-pane label="房产统计表" name="tab4"></el-tab-pane>
                        <el-tab-pane label="土地统计表" name="tab5"></el-tab-pane>
                        <el-tab-pane label="文体广告统计表" name="tab6"></el-tab-pane>
                        <el-tab-pane label="运营资产统计表" name="tab7"></el-tab-pane>
                        <el-tab-pane label="办公资产统计表" name="tab8"></el-tab-pane>
                        <el-tab-pane label="权证办理统计表" name="tab9"></el-tab-pane>
                    </el-tabs>
                </div>
                <div class="content-area">
                    <transition name="fade-transform" mode="out-in">
                        <keep-alive>
                            <component :is="Component" :disabledFutureDates="disabledFutureDates" />
                        </keep-alive>
                    </transition>
                </div>
            </el-card>

        </template>
    </app-form-page>
</template>

<script setup>
import AssetChange from './components/asset-change.vue';
import AssetUse from './components/asset-use.vue';
import LineEquipmentUsage from './components/line-equipment-usage.vue';
import PropertyResource from './components/real-estate-resources.vue';
import LandResources from './components/land-resources.vue';
import StylisticAdvertising from './components/stylistic-advertising.vue';
import OperatingAssets from './components/operating-assetsTable.vue';
import OfficeAsset from './components/office-assets.vue';
import WarrantProcessing from './components/warrant-processing.vue';
import dayjs from 'dayjs';

const name = 'asset-analysis-statistical-report';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '统计报表'
        }
    ];
});

onMounted(() => {

});

const Component = computed(() => {
    const componentMap = {
        tab1: AssetChange,
        tab2: AssetUse,
        tab3: LineEquipmentUsage,
        tab4: PropertyResource,
        tab5: LandResources,
        tab6: StylisticAdvertising,
        tab7: OperatingAssets,
        tab8: OfficeAsset,
        tab9: WarrantProcessing
    };
    return componentMap[activeTab.value] || AssetChange;
});

const createDate = ref(dayjs().format('YYYY-MM'));
const disabledFutureDates = (date) => {
    return date.getTime() > new Date().getTime();
};

const activeTab = ref('tab1'); // 当前选中的 Tab

// onActivated(() => {
//     createDate.value = dayjs().format('YYYY-MM');
// });
</script>

<style lang="scss" scoped>
$page-name: asset-analysis-statistical-report;

.#{$page-name} {
    .container {
        flex: 1 1 auto;
        display: flex;
        overflow: auto;

        :deep {

            .elp-tabs__nav-prev,
            .elp-tabs__nav-next {
                height: 100%;
                display: flex;

                i {
                    margin: auto;
                }
            }
        }

        :deep(.elp-card__body) {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            padding-top: 0;
            width: 100%;
        }

        .content-area {
            flex: 1 1 auto;
            overflow: hidden;

            :deep {
                .header-toolbar {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;

                    &__selector {
                        display: flex;
                        gap: 8px;
                    }
                }
            }
        }
    }
}
</style>
