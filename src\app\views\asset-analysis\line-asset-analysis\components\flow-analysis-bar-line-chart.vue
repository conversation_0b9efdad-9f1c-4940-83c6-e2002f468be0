<template>
    <app-business-panel mode="card" v-loading="loading.chart1">
        <template #header>
            <span class="app-business-panel-title">资产状态vs客流分析</span>
        </template>
        <template #header-append>
            <div class="dropdown-container">
                <el-select v-model="operationalAssets.asset">
                    <el-option v-for="(
                                item, index
                            ) in operationalAssets.assetArray" :key="index" :label="item.Name" :value="item.Code" />
                </el-select>
                <el-select v-model="operationalAssets.line">
                    <el-option v-for="(item, index) in operationalAssets.lineArray" :key="index" :label="item.Name"
                        :value="item.Code" />
                </el-select>
            </div>
        </template>
        <template v-if="chartData1?.source?.length">
            <ecp-chart-bar-line theme="whiteTheme" :unit="['台', '万人']" :data="chartData1" :option="chartData1.option"
                class="demo-container" />
        </template>
        <template v-else>
            <ecp-empty />
        </template>
    </app-business-panel>
</template>

<script setup>
import { defineProps, reactive, watch, nextTick, ref, shallowRef, onActivated } from 'vue';
import { OperatingAsset } from '@api/asset-panorama/operating-asset';
import { LineAssetAnalysis } from '@api/asset-analysis/line-asset-analysis';

const props = defineProps({
    selectedLine: String, // 选择的线路
    isInitialLoad: Boolean
});

const loading = reactive({
    chart1: false
});

const operationalAssets = reactive({
    asset: '全部类型',
    line: '1',
    assetArray: [],
    lineArray: [
        { Name: '月累计客运量', Code: '1' },
        { Name: '每月日均客运量', Code: '2' }
    ],
    options: {
        series: []
    }
});

const isInitialLoad1 = ref(true);

onMounted(() => {
    // 获取资产类型数据
    OperatingAsset.getAssetList().then((response) => {
        if (response.Data && response.Data.length) {
            const assets = response.Data.map((item) => ({
                Name: item.AssetName,
                Code: item.AssetCode
            }));
            assets.unshift({ Name: '全部类型', Code: 'all' });
            operationalAssets.assetArray = assets;
            operationalAssets.asset = 'all';
            isInitialLoad1.value = false;
        } else {
            console.log('没有资产类型数据返回');
        }
    })
        .catch((error) => {
            console.error('获取资产类型数据失败:', error);
        });
});

onActivated(() => {
    fetchChartData();
});

const _color = ['#0367FC', '#487690'];

const chartData1 = reactive({
    dimensions: ['类目名称', '资产在用数', '月累计客运量'],
    source: [],
    option: {
        color: _color,
        xAxis: {
            axisLabel: {
                fontFamily: 'D-DIN',
                rotate: 60,
                interval: 'auto', // 自动调整标签间隔
                color: 'rgba(0, 0, 0, 0.45)' // 设置标签颜色
            }
        },
        yAxis: [
            {
                type: 'value',
                name: '单位:台',
                nameTextStyle: {
                    padding: [0, -12, 0, 0]
                },
                axisLabel: {
                    color: 'rgba(0, 0, 0, 0.45)'
                }
            },
            {
                type: 'value',
                name: '单位:万人',
                nameTextStyle: {
                    padding: [0, -12, 0, 0]
                },
                axisLabel: {
                    color: 'rgba(0, 0, 0, 0.45)'
                }
            }
        ],
        series: [
            {
                type: 'bar',
                yAxisIndex: 0,
                itemStyle: {
                    borderRadius: 20
                },
                barWidth: 10
            },
            {
                type: 'line',
                yAxisIndex: 1,
                showSymbol: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    type: 'dashed'
                },
                smooth: 0.5

            }
        ],
        dataZoom: [
            {
                type: 'inside',
                xAxisIndex: [0],
                start: 0,
                end: 100,
                zoomOnMouseWheel: true,
                moveOnMouseMove: true
            }
        ],
        grid: {
            bottom: 0,
            top:40,
            left: 0,
            right: 0
        },
        legend: {
            top:10,
        }
    }
});

const fetchChartData = async () => {
    if (props.isInitialLoad || isInitialLoad1.value) {
        return;
    }
    loading.chart1 = true;
    const params = {
        daysOrMonths: operationalAssets.line || '1'
    };
    if (operationalAssets.asset !== 'all') {
        params.assetNum = operationalAssets.asset;
    } else if (props.selectedLine.line !== 'all') {
        params.udLine = props.selectedLine.line;
    }
    try {
        const response = await LineAssetAnalysis.getStatusAndAnalyse(params);
        if (response && response.Data) {
            const { Asset, Passenger } = response.Data;
            const assetMap = new Map(
                Asset.map((a) => [a.DateInAsset, a.AssetAmount])
            );
            const passengerMap = new Map(
                Passenger.map((p) => [
                    p.DateInPassenger,
                    p.PassengerAmount / 10000
                ])
            );
            const source = Passenger.map((p) => ({
                类目名称: p.DateInPassenger,
                资产在用数: assetMap.has(p.DateInPassenger)
                    ? assetMap.get(p.DateInPassenger)
                    : 0,
                [operationalAssets.line === '1' ? '月累计客运量' : '每月日均客运量']: passengerMap.get(p.DateInPassenger)
            }));
            chartData1.source = source;
        }
    } catch (error) {
        console.error('获取柱线图数据失败:', error);
    } finally {
        loading.chart1 = false;
    }
};

// 监听属性变化
watch(() => props.selectedLine.line, (newLine) => {
    fetchChartData();
});

watch(() => operationalAssets.asset, (newAsset) => {
    fetchChartData();
});

// 监听选择框的变化
watch(() => operationalAssets.line, (newLineType) => {
    if (newLineType === '1') {
        chartData1.dimensions = ['类目名称', '资产在用数', '月累计客运量'];
    } else if (newLineType === '2') {
        chartData1.dimensions = ['类目名称', '资产在用数', '每月日均客运量'];
    }
    fetchChartData();
});

</script>

<style scoped lang="scss">
.dropdown-container {
    display: flex;
    justify-content: space-between;
    gap: 8px;

    .elp-select {
        width: 100px;
        height: 32px;
    }
}
</style>
