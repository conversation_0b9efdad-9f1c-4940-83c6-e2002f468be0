<!-- <docs>
# 登录表单

## props传参
- FormSize[String]：登录表单尺寸，medium | small | mini （默认small）
- EncryptionType[String]：加密方式，none | AES （none为不加密，默认为AES，以后可拓展其他方式）
- RegExpPasswordForMod：请看<form-password-mod>子组件说明
- ErrorNotePasswordForMod：请看<form-password-mod>子组件说明

## slot插槽
- name=loginFormBefore - 登录表单前置内容
- name=configError - 登录配置获取失败展示内容
- name=secondLogin - 自定义二次登录方式展示

</docs> -->

<template>
    <div class="ecpp-login-component ecpp-login-form">
        <!-- 登录表单 -->
        <div class="ecpp-login-form-wrapper" v-if="!secondLoginView">
            <el-form :size="FormSize" ref="loginFormRef" :model="loginForm" :rules="rules">
                <!-- 插槽 - 登录表单前置内容 -->
                <slot name="loginFormBefore" v-if="$slots?.loginFormBefore"></slot>

                <template v-if="configError">
                    <!-- 插槽 - 登录配置获取失败展示内容 -->
                    <slot name="configError" v-if="$slots.configError"></slot>
                    <el-result icon="warning" title="登录配置获取失败" v-else>
                        <template #extra>
                            <el-button type="primary" size="medium" @click="configRetry">重 试</el-button>
                        </template>
                    </el-result>
                </template>

                <template v-if="hasPasswordBtn">
                    <el-form-item prop="username">
                        <el-input class="ecpp-login-form-input" type="text" autocomplete="new-password"
                            v-model.trim="loginForm.username" :placeholder="placeholderConfig?.username || '请输入您的账号'"
                            @keydown.enter="login">
                            <template #prefix>
                                <slot name="usernameIcon" v-if="$slots?.usernameIcon"></slot>
                                <el-icon v-else>
                                    <User></User>
                                </el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="password">
                        <el-input class="ecpp-login-form-input" type="password" autocomplete="new-password"
                            v-model.trim="loginForm.password" :placeholder="placeholderConfig?.password || '请输入您的密码'"
                            @keydown.enter="login" show-password>
                            <template #prefix>
                                <slot name="passwordIcon" v-if="$slots?.passwordIcon"></slot>
                                <el-icon v-else>
                                    <Lock></Lock>
                                </el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                    <div class="btn-wrap">
                        <el-button class="ecpp-login-form-button" :size="FormSize" type="primary" @click="login"
                            :disabled="(!loginForm.username || !loginForm.password)">登 录</el-button>
                    </div>
                    <!-- <el-form-item class="remember-password">
                        <el-checkbox v-model="remainPwd">记住密码</el-checkbox>
                    </el-form-item> -->
                </template>
                <!-- 仅有pki登录时，pki登录以按钮形式放在中间 -->
                <template v-if="!hasPasswordBtn && hasPKIBtn">
                    <el-button class="ecpp-login-form-button only-pki-button" :size="FormSize" v-if="hasPKIBtn"
                        type="primary" @click="doPkiLogin()">PKI 登 录</el-button>
                </template>
            </el-form>

            <!-- 插槽 - 登录表单后置内容 -->
            <slot name="loginFormAfter" v-if="$slots?.loginFormAfter"></slot>

            <!-- 有密码和pki登录两种方式时，pki登录放在底部 -->
            <el-button type="text" class="footer-pki-button" @click="doPkiLogin()" v-if="hasPasswordBtn && hasPKIBtn">
                PKI登录<el-icon>
                    <DArrowRight />
                </el-icon>
            </el-button>
        </div>
        <template v-else>
            <!-- 自定义二次登录方式展示 -->
            <template v-if="customSecondLogin && $slots?.secondLogin">
                <slot name="secondLogin" v-bind="{ handleBack, secondLoginWay, doSecondLogin }"></slot>
            </template>

            <!-- 默认二次登录方式展示 -->
            <div class="second-login-view" v-else>
                <el-icon class="back-btn" @click="handleBack">
                    <Back />
                </el-icon>
                <div class="tips">已启用双重身份认证机制，请选择第二种认证方式：</div>
                <div class="login-way-box">
                    <template v-for="(item, index) in secondLoginWay">
                        <div class="login-way-card" @click="doSecondLogin(item.key)"
                            :key="`${item && item.key || ''}_${index}`" v-if="item && item.key">
                            <div class="login-way-card-main">
                                <div class="second-title">方式{{ toUpper(index + 1) }}：</div>
                                <div class="title">{{ item.desc }}</div>
                                <div class="button">立即认证<el-icon>
                                        <ArrowRight />
                                    </el-icon></div>
                            </div>
                            <div class="login-way-card-decoration">
                                <div class="background"></div>
                                <i class="ecpp-login-iconfont"
                                    :class="[item.key === 'FACE' ? 'ecpp-login-icon-xian186' : 'ecpp-login-icon-xian304']"></i>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </template>

        <!-- 二次登录-账号密码认证 -->
        <second-login-password v-model:visible="secondLoginPwdVisible" :is-complex-network="isComplexNetwork"
            :placeholder-config="placeholderConfig" @modify-password="handleOpenPwdMod">
            <!-- 插槽传递 - 图标 - 账号 -->
            <template #usernameIcon v-if="$slots.usernameIcon">
                <slot name="usernameIcon"></slot>
            </template>
            <!-- 插槽传递 - 图标 - 密码 -->
            <template #passwordIcon v-if="$slots.passwordIcon">
                <slot name="passwordIcon"></slot>
            </template>
        </second-login-password>

        <!-- 二次登录-人脸认证 -->
        <second-login-face :is-complex-network="isComplexNetwork" v-model:visible="secondLoginFaceVisible"
            :userCode="userCode" :service="service" />

        <!--修改密码组件-->
        <form-password-mod
            v-bind="{ pwModType, regExpPasswordForMod, errorNotePasswordForMod, passwordVerify, pwdMinLength, safeRequest }"
            :userCode="loginForm.username" :visible="pwModVisible" @close="pwModVisible = false" v-if="pwModVisible" />
    </div>
</template>

<script setup>
import { ref, computed, defineComponent, defineProps, provide, onMounted, getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus';
import { User, Lock, DArrowRight, Back } from '@element-plus/icons-vue';

import { LoginApi } from '../api';
import * as Utils from '../utils';
import * as PKI from './pkiLogin.js';
import {
    SecondLoginWayDict,
    UserNameSaltKey,
    PwdSaltKey
} from '../constants.dict';

import FormPasswordMod from './form-password-mod.vue';
import SecondLoginPassword from './second-login-password.vue';
import SecondLoginFace from './second-login-face.vue';

defineComponent({
    name: 'ecp-login-form'
});

const { proxy } = getCurrentInstance();

const props = defineProps({
    config: {
        type: Object,
        default: () => {
            return {};
        }
    },
    redirectUrl: {
        type: String,
        default: ''
    },
    isComplexNetwork: Boolean,
    safeRequest: Boolean,

    customSecondLogin: Boolean,
    customLoginSuccess: Boolean,
    customLoginError: Boolean,
    customModifyPwd: Boolean,

    placeholderConfig: {
        type: Object,
        default: () => ({})
    },

    configError: Boolean,
    loginWay: {
        type: Array,
        default: () => {
            return [
                {
                    key: 'PASSWORD',
                    desc: '账号密码'
                }
            ];
        }
    },
    passwordVerify: {
        type: Array
    },
    pwdMinLength: Number,
    secondLoginWayStr: {
        type: String,
        default: ''
    }
});

const emits = defineEmits([
    'configRetry',
    'onSecondLogin',
    'onLoginSuccess', 'onLoginError',
    'onModifyPwd'
]);

const loginForm = reactive({
    username: '',
    password: ''
});
const rules = {
    username: { required: true, message: '用户名不能为空' },
    password: { required: true, message: '密码不能为空' }
};

// 修改密码弹窗
const pwModVisible = ref(false);
// 1001为首次登录需重置密码； 1002为账户密码已过有效期需修改密码
const pwModType = ref(null);
// 是否记住密码
const remainPwd = ref(false);
// 二次登录视图
const secondLoginView = ref(false);
// 二次登录方式
const secondLoginWay = ref([]);
const secondLoginPwdVisible = ref(false);
const secondLoginFaceVisible = ref(false);

const service = ref('');
const userCode = ref('');
let pkiBtnDisabled = false;

const loginFormRef = ref();

const FormSize = computed(() => {
    return (
        proxy.$attrs.FormSize ||
        proxy.$attrs.formSize ||
        props.config.FormSize ||
        props.config.formSize ||
        'small'
    );
});
const regExpPasswordForMod = computed(() => {
    return (
        proxy.$attrs.RegExpPasswordForMod ||
        proxy.$attrs.regExpPasswordForMod ||
        props.config.RegExpPasswordForMod ||
        props.config.regExpPasswordForMod
    );
});
const errorNotePasswordForMod = computed(() => {
    return (
        proxy.$attrs.ErrorNotePasswordForMod ||
        proxy.$attrs.errorNotePasswordForMod ||
        props.config.ErrorNotePasswordForMod ||
        props.config.errorNotePasswordForMod
    );
});
const EncryptionType = computed(() => {
    return (
        proxy.$attrs.EncryptionType ||
        proxy.$attrs.encryptionType ||
        props.config.EncryptionType ||
        props.config.encryptionType ||
        'AES'
    );
});
const hasPKIBtn = computed(() => {
    return props.loginWay.findIndex(item => item?.key === 'PKI') !== -1;
});
const hasPasswordBtn = computed(() => {
    return props.loginWay.findIndex(item => item?.key === 'PASSWORD') !== -1;
});
const originUrl = computed(() => {
    return Utils.getOriginUrl(props.isComplexNetwork);
});

const toUpper = (val) => {
    const map = {
        1: '一',
        2: '二',
        3: '三'
    };
    return map[val];
};
const handleBack = () => {
    secondLoginView.value = false;
};
const getPasswordFromCookie = () => {
    const { username, password } = Utils.getPasswordFromCookie();
    loginForm.username = username;
    loginForm.password = password;
    if (loginForm.username && loginForm.password) {
        remainPwd.value = true;
    }
};
const doRemainPwd = () => {
    const username = Utils.Encrypt(
        loginForm.username,
        UserNameSaltKey
    );
    const password = Utils.Encrypt(loginForm.password, PwdSaltKey);
    Utils.setCookie('u', username, 7);
    Utils.setCookie('p', password, 7);
};
const resetPwd = () => {
    Utils.setCookie('u', '', 0);
    Utils.setCookie('p', '', 0);
};
const doSecondLogin = (key) => {
    switch (key) {
        case 'PASSWORD':
            secondLoginPwdVisible.value = true;
            break;
        case 'PKI':
            doPkiLogin(true);
            break;
        case 'FACE':
            secondLoginFaceVisible.value = true;
            break;
        default:
            break;
    }
};
const handleOpenPwdMod = (opCode) => {
    pwModType.value = opCode;
    pwModVisible.value = true;
};
const login = async () => {
    try {
        const valid = await loginFormRef.value.validate();
        if (!valid) {
            ElMessage.warning('校验不通过，请看错误提示');
            throw new Error(valid);
        }
        // 记住密码
        if (remainPwd.value) {
            doRemainPwd();
        } else {
            resetPwd();
        }
        secondLoginWay.value = getSecondLoginWay(
            props.secondLoginWayStr
        ).filter(item => item?.key !== 'PASSWORD');
        const hasSecondLogin = !!secondLoginWay.value?.length;
        const params = getLoginParams(hasSecondLogin);
        const headers = getLoginHeaders();
        const targetApi = hasSecondLogin ? 'ssoCheck' : 'ssoDoLogin';

        const result = await LoginApi[targetApi](params, {
            headers
        });
        if (result.OpCode === 0) {
            onLoginSuccess({
                hasSecondLogin,
                params,
                result
            });
        } else if (result.OpCode === 1001 || result.OpCode === 1002) {
            onLoginModifyPwd({ result });
        } else {
            onLoginError({ result });
        }
    } catch (error) {
        console.log(
            '%c [ECP-LOGIN] login Caught error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    }
};
const getLoginParams = (hasSecondLogin) => {
    // alg : 加密方式； 1：明文； 2：AES+hex； 3:待拓展；
    // loginType: null||'': 默认标准登录  2: PKI登录；[p：传ST参数，st根据身份证号获得，5s有效期]
    loginForm.alg = '2';
    const time = Date.parse(new Date());
    const username = Utils.Encrypt(
        loginForm.username,
        `PCI${time}`
    );
    const password = Utils.Encrypt(
        loginForm.password,
        `PCI${time}`
    );
    const serviceParam = Utils.getUrlParam('service');
    const service = serviceParam || Utils.stringsToHex(originUrl.value);
    const params = {
        u: Utils.stringsToHex(username),
        p: Utils.stringsToHex(password),
        x: Utils.stringsToHex(loginForm.username),
        service,
        time,
        alg: '2'
    };
    return hasSecondLogin
        ? {
            LoginWay: 1,
            LoginReq: params
        }
        : params;
};
const getLoginHeaders = () => {
    const isNativeClient = window?._env?.isNativeClient; // 是否托盘客户端
    const headers = {
        x_op_client: isNativeClient ? 2 : 1
    };
    return headers;
};
const getSecondLoginWay = (loginWayStr) => {
    const res = loginWayStr
        .split(',')
        .map(item => SecondLoginWayDict[item])
        .filter(item => props.customSecondLogin || !!item);
    console.log('二次登录方式》》', res, loginWayStr);
    return res;
};
const doPkiLogin = async (isSecondLogin) => {
    if (pkiBtnDisabled) {
        return;
    }
    try {
        pkiBtnDisabled = true;
        if (!isSecondLogin) {
            secondLoginWay.value = getSecondLoginWay(
                props.secondLoginWayStr
            ).filter(item => item?.key !== 'PKI');
        }
        const hasSecondLogin = !!(
            secondLoginWay.value && secondLoginWay.value.length
        );
        const result = await PKI.pkiLogin(
            hasSecondLogin && !isSecondLogin
        );
        if (result && result.OpCode === 0) {
            const serviceParam = Utils.getUrlParam('service');
            const service =
                serviceParam || Utils.stringsToHex(originUrl.value);
            const params = {
                service
            };
            onLoginSuccess({
                hasSecondLogin: hasSecondLogin && !isSecondLogin,
                params,
                result
            });
        } else {
            onLoginError({ result, isPKI: true });
        }
    } catch (error) {
        console.log(
            '%c [ECP-LOGIN] doPkiLogin Caught error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    }
    setTimeout(() => {
        pkiBtnDisabled = false;
    }, 200);
};
const configRetry = () => {
    emits('configRetry');
};
const onLoginSuccess = ({ hasSecondLogin, params, result }) => {
    if (hasSecondLogin) {
        if (props.customSecondLogin) {
            emits('onSecondLogin', {
                params,
                result,
                secondLoginWay: secondLoginWay.value,
                secondLoginWayStr: props.secondLoginWayStr,
                isComplexNetwork: props.isComplexNetwork
            });
            if (
                proxy.$slots.secondLogin
            ) {
                secondLoginView.value = true;
            }
            return;
        }
        service.value = params.service;
        userCode.value = result.userCode;
        secondLoginView.value = true;
    } else {
        const redirectUrl = Utils.getRedirectUrl(
            props.redirectUrl ||
            result.Data.redirectUrl ||
            originUrl.value,
            props.isComplexNetwork
        );
        if (props.customLoginSuccess) {
            return emits('onLoginSuccess', {
                params,
                result,
                redirectUrl,
                isComplexNetwork: props.isComplexNetwork
            });
        }
        window.location.href = redirectUrl;
        window.localStorage &&
            window.localStorage.setItem('loginStatus', 'logined');
    }
};
const onLoginError = ({ result, isPKI }) => {
    if (props.customLoginError) {
        return emits('onLoginError', {
            result
        });
    }
    ElMessage.warning(
        result?.OpDesc || `${isPKI ? 'PKI' : ''}登录失败`
    );
};
const onLoginModifyPwd = ({ result }) => {
    if (props.customModifyPwd) {
        return emits('onModifyPwd', {
            loginForm,
            remainPwd: remainPwd.value,
            result
        });
    }
    handleOpenPwdMod(result.OpCode);
};

provide('getPasswordFromCookie', getPasswordFromCookie);

onMounted(() => {
    getPasswordFromCookie();
});
</script>
