<template>
    <app-business-panel class="stock-land-pie-chart" title="按性质占比分析" :interval="false" :intervalHeader="false"
        v-loading="loading">
        <template #default>
            <div class="stock-land-pie-chart__content">
                <template v-if="graphData?.length">
                    <div class="chart-legend">
                        <div v-for="(item,index) in graphData" :key="index" class="chart-legend-item">
                            <i class="icon" :style="{ backgroundColor:item.color }"></i>
                            <div class="chart-legend-item-label">
                                <span class="chart-legend-item-text">{{ item.name }}</span>
                                <span class="chart-legend-item-rate font-number-bold">{{ item.value }}%</span>
                            </div>
                        </div>
                    </div>
                    <ecp-chart-simple-pie v-bind="chartProps" :key="`${graphTimestamp}_pie`" />
                </template>
                <ecp-empty v-else />
            </div>
        </template>
    </app-business-panel>
</template>

<script setup>
import { LandAssetApi } from '@api/asset-panorama/land-asset';
import { CHART_COLOR_LIST } from '@constants/enum-config';

const loading = ref(false);

const legend = ref({
    show: false
});

const graphData = ref([]);

const graphTimestamp = ref(Date.now());

const defaultOptions = {
    legend: {
        show: true,
        orient: 'vertical',
        left: 'left'
    },
    series: [
        {
            type: 'pie',
            label: {
                show: false
            },
            itemStyle: {
                borderColor: '#fff',
                borderWidth: 2
            },
            radius: ['0%', '70%']
        }
    ],
    unit: ['%']
};

const chartProps = computed(() => {
    const defaultProps = {
        type: 2,
        theme: 'whiteTheme',
        listVisible: false,
        list: graphData.value,
        color: CHART_COLOR_LIST,
        tooltipDimensions: ['名称', '占比']
    };
    return {
        ...defaultProps,
        option: {
            ...defaultOptions
        }
    };
});

const getChartData = async () => {
    console.log('%c getChartData', 'font-size:18px;color:gold;font-weight:700;');

    loading.value = true;
    graphData.value = [];

    try {
        const res = await LandAssetApi.getQualityProportion();
        const list = (res || []).map((item, index) => ({
            name: item.DataName,
            value: item.Percentage ? Math.round(item.Percentage * 1000) / 1000 : 0, // 保留三位小数
            color: CHART_COLOR_LIST[index]
        }));

        graphData.value = list;
    } catch (error) {
        console.log('%c getTransferStatDataTrend Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    }

    loading.value = false;
};

onActivated(() => {
    getChartData();
});

</script>

<style scoped lang="scss">
.stock-land-pie-chart {
    overflow: hidden;

    &__content {
        width: 100%;
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: row;

        .chart-legend {
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-right: var(--spacer-large);
            margin-left: var(--spacer-extra-large);

            .chart-legend-item {
                display: flex;
                flex-direction: row;
                margin-bottom: var(--spacer-small);

                &-label {
                    display: flex;
                    flex-direction: column;
                }

                &-text {
                    font-size: 12px;
                    color: var(--color-text-secondary);
                    white-space: nowrap;
                }

                &-rate {
                    font-size: 18px;
                    color: var(--color-text-primary);
                    line-height: var(-font-line-height-larger-2);
                }

                .icon {
                    display: inline-block;
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    margin-right: var(--spacer);
                    margin-top: 5px;
                }
            }
        }

        :deep(.ecp-chart-simple-pie) {
            justify-content: center;
            width: calc(100% - 100px);

            .ecp-chart-simple-pie-left {
                width: 90%;

                .chart-bg {
                    display: none;
                }
            }
        }
    }
}
</style>
