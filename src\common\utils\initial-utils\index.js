import { ElLoadingService, MicroUtils } from '@ecp/ecp-ui-plus';
import useUserStore from '@store/user';
import useGlobalStore from '@store/global-config';
import usePermissionStore from '@store/permission';
import { cloneDeep, get } from 'lodash-es';

import router from '@router';

export * from './login';

const defaultMenuProps = {
    id: 'Id',
    label: 'Text',
    route: 'Value.Target',
    url: 'Url',
    symbol: 'symbol',
    children: 'ChildNodes'
};

/**
 * @method formatMenu 菜单处理
 * @param {Object} params
 *** @property {Array} menu 菜单树
 *** @property {Array} menuProps 菜单键映射
 *** @property {String} symbol 链接前缀匹配规则
 */
export const formatMenu = ({ menu, menuProps, symbol = '/s-' }) => {
    if (!menuProps) {
        menuProps = cloneDeep(defaultMenuProps);
    }
    return menu.map(menuItem => {
        let children = menuItem[menuProps.children];
        const indexSymbol = symbol;
        if (children && children.length) {
            children = formatMenu({
                menu: children,
                menuProps,
                symbol: indexSymbol
            });
        }
        const menuRoute = menuProps.route;
        let route = menuItem[menuRoute];
        // if (+menuItem?.Value?.Funtype === 1) {
        if (menuItem?.type !== 'iframe') {
            route = MicroUtils.formatRoute(get(menuItem, menuRoute), indexSymbol, PACKAGE_NAME);
        }
        // }

        const foramtPath = (data) => {
            let href = data[menuProps?.route || 'route'] || data[menuProps?.url || 'url'];
            if (data.type === 'iframe') {
                const iframeUrl = (data[menuProps?.route || 'route'] || data[menuProps?.url || 'url']).replace(/^\/s-/, '');
                href = `/${PACKAGE_NAME}/#/inner-iframe/${encodeURIComponent(iframeUrl)}`;
            }
            return {
                ...data,
                [menuProps?.route || 'route']: href,
                [menuProps?.url || 'url']: href
            };
        };
        const result = foramtPath({
            ...menuItem,
            symbol: indexSymbol,
            [menuProps.route]: route
        });
        if (children && children.length) {
            result[menuProps.children] = children;
        } else {
            delete result[menuProps.children];
        }
        return result;
    });
};

/**
 * @method systemInitial 系统初始化
 */
export const systemInitial = async ({ loadingTarget, appConfig, configOnly }) => {
    let loadingInstance = ElLoadingService({
        ...(loadingTarget ? { target: loadingTarget } : {}),
        body: !loadingTarget,
        fullscreen: true,
        text: '请稍等...',
        lock: true
    });

    const globalStore = useGlobalStore();
    const userStore = useUserStore();
    const permissionStore = usePermissionStore();

    /**
     * 如需要开启页面缓存，请放开这段注释 START
     */
    const routes = router.options.routes;
    routes.forEach(({ component, ...view }) => {
        view?.path !== '**' &&
        view?.path !== '/:pathMatch(.*)*' &&
            view?.name !== 'inner-iframe' &&
            view?.name !== 'redirect' &&
            view?.name !== 'access-denied' &&
            permissionStore.addCachedView(view);
    });
    /**
     * 如需要开启页面缓存，请放开这段注释 END
     */

    /**
     * 获取配置 START
     */
    try {
        await globalStore.getGlobalConfigs();
        if (loadingInstance) {
            loadingInstance.text.value = globalStore.globalConfigs.IMPORT_CONFIGS.title;
        }
    } catch (error) {
        console.log(
            '%c [SYSTEM INITIAL] getGlobalConfigs Caught Error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    }

    if (configOnly) {
        loadingInstance?.close && loadingInstance.close();
        loadingInstance = null;
        return;
    }
    /**
     * 获取配置 END
     */

    // // 免登录处理, 有需要可以放开这段
    // /**
    //  * 通过链接传入的Usercode登录 START
    //  */
    // const queryStr = location.hash.split('?')[1];
    // const initQuery = qs.parse(queryStr);
    // const userCode = initQuery?.usercode;

    // try {
    //     if (userCode) {
    //         await userStore.dispatch('loginWithUserCode', userCode);
    //     }
    // } catch (error) {
    //     console.log(
    //         `%c [SYSTEM INITIAL] loginWithUserCode ${userCode} Caught Error`,
    //         'font-size:18px;color:red;font-weight:700;',
    //         error
    //     );
    // }
    // /**
    //  * 通过链接传入的Usercode登录 END
    //  */

    /**
     * 获取用户信息 START
     */
    try {
        await userStore.getUserInfo();
        await permissionStore.getPermission();
    } catch (error) {
        console.log(
            '%c [SYSTEM INITIAL] Initial Permission Caught Error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    }
    /**
     * 获取用户信息 END
     */

    loadingInstance?.close && loadingInstance.close();
    loadingInstance = null;
};
