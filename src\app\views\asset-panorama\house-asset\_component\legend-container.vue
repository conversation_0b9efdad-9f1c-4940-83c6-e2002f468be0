<script setup>
const props = defineProps({
    name: {
        type: Array,
        default: () => ['default1', 'default2']
    },
    value: {
        type: Object,
        default: () => ({ default1: 0, default2: 0 })
    }
});
</script>

<template>
    <div class="legend-container">
        <div class="legend-container-surplus">
            <img class="legend-container-surplus__icon" alt="" src="/src/assets/trend/surplus.png">
            <div class="legend-container-surplus-content">
                <div class="legend-container-surplus-content__label">{{ name[0] }}</div>
                <div class="legend-container-surplus-content__value">{{ value[name[0]].toLocaleString() }}</div>
            </div>
        </div>
        <div class="legend-container-loss">
            <img class="legend-container-loss__icon" alt="" src="/src/assets/trend/loss.png">
            <div class="legend-container-loss-content">
                <div class="legend-container-loss-content__label">{{ name[1] }}</div>
                <div class="legend-container-loss-content__value">{{ value[name[1]].toLocaleString() }}</div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.legend-container {
    margin: 16px 0;
    display: flex;
    padding: 1px var(--spacer);
    gap: var(--spacer-extra-large);
    align-items: center;

    &-surplus,
    &-loss {
        display: flex;
        gap: var(--spacer-large-3);

        &__icon {
            width: 40px;
            height: 40px;
            margin: auto 0;
        }

        &-content {
            user-select: none;

            &__label {
                font-size: var(--font-size-small);
                color: var(--color-text-secondary);
            }

            &__value {
                font-family: D-DIN-BOLD;
                font-size: var(--font-size-larger-2);
                font-weight: bold;
                line-height: 30px;
                letter-spacing: 0.8px;
                color: var(--color-black-opacity-8-5);
            }
        }
    }
}
</style>
