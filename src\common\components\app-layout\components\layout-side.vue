<template>
    <el-aside class="layout-side" :class="{ collapse }">
        <LayoutLogo direction="vertical" v-bind="{ name, collapse }" />
        <LayoutMenu direction="vertical" v-bind="{ menu, menuProps }" @select="onSelect" />
        <div class="layout-side-bottom">
            <LayoutUser v-if="!collapse" />
            <div class="layout-side-bottom--item trigger border-top" @click="collapse = !collapse">
                <img :class="['layout-side-bottom--item-icon', collapse ? '' : 'rotate']" :src="ICON_COLLAPSE" alt="">
            </div>
        </div>
    </el-aside>
</template>

<script setup>
import { MENU_PROPS } from '@constants/menu-config';

import LayoutLogo from './layout-logo.vue';
import LayoutMenu from './layout-menu/layout-menu.vue';
import LayoutUser from './layout-user.vue';

import { useMenu } from './layout-menu/use-menu';
const ICON_COLLAPSE = `/${PACKAGE_NAME}/layout/icon-collapse.svg`;

defineComponent({
    name: 'layout-side'
});

const props = defineProps({
    name: {
        type: String
    },
    menu: {
        type: Array,
        default: () => ([])
    },
    menuProps: {
        type: Object,
        default: () => MENU_PROPS
    }
});

const emits = defineEmits(['select']);

const { collapse } = useMenu();

const onSelect = (...args) => {
    emits('select', ...args);
};
</script>

<style lang="scss" scoped>
.layout-side {
    width: 200px;
    height: 100%;
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    background-image: url('/layout/bg-side-bottom.png'), linear-gradient(0deg, #005FC0 100%, #005FC0 100%);
    background-position: bottom left, top left;
    background-size: 100% auto, 100% 100%;
    background-repeat: no-repeat, no-repeat;
    overflow: hidden;
    transition-duration: 150ms;

    .layout-side-bottom {
        width: 100%;
        height: 48px;
        flex: 0 0 auto;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        &--item {
            display: flex;
            justify-content: flex-end;
            padding: var(--spacer-medium-3) var(--spacer-large);

            &.trigger {
                cursor: pointer;
            }

            &-icon {
                height: var(--font-size-large);
                width: var(--font-size-large);
                object-fit: contain;

                transform: rotate(180deg);

                &.rotate {
                    transform: rotate(0deg);
                }
            }
        }
    }

    &.collapse {
        width: 64px;

        .layout-side-bottom {

            &--item {
                width: 64px;
                justify-content: center;
            }
        }
    }
}
</style>
