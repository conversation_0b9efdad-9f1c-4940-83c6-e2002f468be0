<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <template #content>
            <div :class="[`${name}__wrapper`]" v-if="!invisible">
                <app-business-panel mode="card" :showHeader="false" v-if="!maximumModule">
                    <overview-statistic></overview-statistic>
                </app-business-panel>
                <div class="overview-bottom" ref="scrollbarRef">
                    <statistic-map class="overview-bottom__left" @toggleMaximumModule="toggleMaximumModule"
                        v-if="!maximumModule || maximumModule === 'StatisticMap'"
                        :maximumModule="maximumModule"></statistic-map>
                    <div class="overview-bottom__right" v-if="!maximumModule">
                        <overview-charts></overview-charts>
                    </div>
                </div>
            </div>
        </template>
    </app-form-page>
</template>

<script setup>
import OverviewStatistic from '@views/asset-panorama/overview/_components/overview-statistic.vue';
import OverviewCharts from '@views/asset-panorama/overview/_components/overview-charts.vue';
import StatisticMap from '@views/asset-panorama/overview/_components/statistic-map.vue';

const name = 'asset-panorama-overview';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '概览'
        }
    ];
});
const scrollbarRef = ref();
const maximumModule = ref('');

const toggleMaximumModule = (module) => {
    maximumModule.value = !maximumModule.value || maximumModule.value !== module ? module : null;
    nextTick(() => {
        scrollbarRef.value?.setScrollTop?.(0);
    });
};

onMounted(() => {

});

const invisible = ref(false);

onActivated(() => {
    invisible.value = false;
});

onDeactivated(() => {
    invisible.value = true;
});

</script>

<style lang="scss" scoped>
$page-name: asset-panorama-overview;

.#{$page-name} {
    &__wrapper {
        display: flex;
        flex-direction: column;
        flex: 1 1 auto;
    }

    .overview-bottom {
        margin-top: var(--spacer-large);
        flex: 1 1 auto;
        display: flex;
        gap: var(--spacer-large);

        &__left {
            flex: 1;
            display: flex;
        }

        &__right {
            width: 816px;
            display: flex;
        }
    }
}

@media screen and (max-width: 1770px) {
    .#{$page-name} {
        .overview-bottom {
            flex-direction: column;

            &__left {
                flex: 1 1 540px;
            }

            &__right {
                width: 100%;
                flex: 1 1 540px;

                .overview-charts {
                    grid-template-areas:
                        "header header"
                        "main main";
                    grid-template-columns: 1fr 1fr 1fr;
                    grid-template-rows: 1fr 1fr;

                    :deep(.app-business-panel) {
                        width: unset;
                        height: unset;
                    }
                }
            }
        }
    }
}
</style>
