<template>
    <app-form-page class="Menu1" title="Menu 1">
        <app-business-panel title="Menu 1 Content" mode="card">
            <router-link to="/Menu2">TO Menu2</router-link><br />
            <el-button @click="handleClick">弹窗</el-button>
            <!-- <button @click="handleClick">弹窗</button> -->
            <el-input v-model="mode" />

            <el-date-picker v-model="datetimeVal" type="datetimerange" />

            <el-dialog v-model="dialogVisible" title="Tips" width="30%" append-to-body>
                <span>TEST</span>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="dialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="dialogVisible = false">确定</el-button>
                    </span>
                </template>
            </el-dialog>
        </app-business-panel>
    </app-form-page>
</template>

<script>
import SystemService from '@api/system-service';
import useDictionaryStore from '@store/dictionary';
// const instance = getCurrentInstance();
// const { proxy } = instance;

export default {
    name: 'Menu1',
    data () {
        return {
            dialogVisible: false,
            dict: [],
            datetimeVal: '',
            mode: 'abc'
        };
    },
    mounted () {
        this.getDict();
    },
    methods: {
        async getDict () {
            const dictionaryStore = useDictionaryStore();
            try {
                SystemService.getUserInfo();
                const dict = await dictionaryStore.getDictionaryCache(1);
                this.dict = dict;
            } catch (error) {
                console.log(
                    '%c getDict Caught error',
                    'font-size:18px;color:red;font-weight:700;',
                    error
                );
            }
        },
        handleClick () {
            console.log('%c [ handleClick ]-29', 'font-size:13px; background:#b75387; color:#fb97cb;');
            // this.dialogVisible = true;
            this.$msgbox({
                title: '弹窗标题',
                message: '弹窗内容'
            });
        }
    }
};

</script>

<style scoped lang="scss">
.Menu1 {}
</style>
