module.exports = {
    env: {
        browser: true,
        es2021: true
    },
    extends: [
        'plugin:vue/vue3-essential',
        'standard',
        './.eslintrc-auto-import.json'
    ],
    globals: {
        USE_PORTAL: true,
        PACKAGE_NAME: true
    },
    overrides: [
    ],
    parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module'
    },
    plugins: [
        'vue'
    ],
    rules: {
        'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
        'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',

        indent: ['error', 4, { SwitchCase: 1 }],
        'no-unused-vars': 'off',
        quotes: ['error', 'single'],
        semi: ['error', 'always'],
        'no-async-promise-executor': 'off',

        'vue/multi-word-component-names': 'off'
    }
};
