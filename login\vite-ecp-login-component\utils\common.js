import { UserNameSalt<PERSON>ey, PwdSaltKey } from '../constants.dict';

import CryptoJS from 'crypto-js';

// 加密方法
export function Encrypt (content, key) {
    if (!content) { return content; }
    key = CryptoJS.enc.Utf8.parse(key);
    const sContent = CryptoJS.enc.Utf8.parse(content);
    const encrypted = CryptoJS.AES.encrypt(sContent, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
    const res = encrypted.toString();
    return res;
}

export const Decrypt = (content, key) => {
    if (!content) {
        return content;
    }
    key = CryptoJS.enc.Utf8.parse(key);
    const decrypted = CryptoJS.AES.decrypt(content, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
    const res = decrypted.toString(CryptoJS.enc.Utf8);
    return res;
};

// 字符转十六进制
export const stringsToHex = (str, format = '') => {
    const arr = [];
    for (let i = 0; i < str.length; i++) {
        arr[i] = (str.charCodeAt(i).toString(16)).slice(-4);
    }
    return format + arr.join(format);
};

// 十六进制转字符
export const hexToString = (hexCharCodeStr) => {
    const trimedStr = hexCharCodeStr.trim();
    const rawStr = trimedStr.substr(0, 2).toLowerCase() === '0x' ? trimedStr.substr(2) : trimedStr;
    const len = rawStr.length;
    if (len % 2 !== 0) {
        alert('Illegal Format ASCII Code!');
        return '';
    }
    let curCharCode;
    const resultStr = [];
    for (let i = 0; i < len; i = i + 2) {
        curCharCode = parseInt(rawStr.substr(i, 2), 16); // ASCII Code Value
        resultStr.push(String.fromCharCode(curCharCode));
    }
    return resultStr.join('');
};

/**
 * 获取页面链接参数
 * @param name 参数的key值
 */
export const getUrlParam = (name) => {
    const results = new RegExp('[\\?&]' + name + '=([^&#]*)').exec(window.location.href);
    if (!results) {
        return '';
    }
    return results[1];
};

export function loginInterceptors (param) {
    // 子应用时向主应用发消息
    if (!param.isPrimaryApp && window.__POWERED_BY_QIANKUN__) {
        const status = 'loginout-' + param.appName;
        window.localStorage && window.localStorage.setItem('loginStatus', status);
        window.eventBus && window.eventBus.emit('loginStatus', { status, response: param.response });
        return;
    }
    window.localStorage && window.localStorage.setItem('appName', param.appName);
    const serviceUrl = window.location.href;
    const service = `service=${stringsToHex(serviceUrl)}`;
    let rs = `${param.response.Data.ssoServerCheckLoginUrl}?${service}&loginUrl=${stringsToHex(param.loginUrl)}`;
    const isComplexNetwork = param && param.isComplexNetwork;
    if (isComplexNetwork) {
        rs = rs.replace(/(.*)\/sso\/checkLogin(.*)/, '/sso/checkLogin$2');
    }
    window.location.href = rs;
}

export const guid = (function () {
    let counter = 0;
    return function (prefix) {
        let guid = (+new Date()).toString(32);
        for (let i = 0; i < 8; i++) {
            guid += Math.floor(Math.random() * 65535).toString(32);
        }
        return ((prefix || '') + guid + (counter++).toString(32)).substring(0, 32);
    };
})();

export const setCookie = (cname, cvalue, exdays) => {
    const d = new Date();
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
    const expires = 'expires=' + d.toGMTString();
    document.cookie = cname + '=' + cvalue + '; ' + expires;
};

export const getCookie = (cname) => {
    const name = cname + '=';
    const ca = document.cookie.split(';');
    let cItem = ca.find(item => {
        return item.trim().indexOf(name) === 0;
    }) || '';
    cItem = cItem.trim();
    return cItem.substring(name.length, cItem.length) || '';
};

/**
 * @method getOriginUrl 获取处理过的origin链接
 * @param {Boolean} isComplexNetwork 是否复杂网络环境
 * @returns
 */
export const getOriginUrl = (isComplexNetwork) => {
    const pathname = window.location.pathname.split('/')[1] || '';
    return isComplexNetwork
        ? `${window.location.origin}/${
            pathname && !pathname.match(/.html$/) ? pathname : ''
        }`.replace(/\/+$/, '') + '/'
        : window.location.origin;
};

/**
 * @method getRedirectUrl 获取处理过的重定向链接
 * @param {String} redirectUrl 重定向链接
 * @param {Boolean} isComplexNetwork 是否复杂网络环境
 * @returns
 */
export const getRedirectUrl = (redirectUrl, isComplexNetwork) => {
    return isComplexNetwork
        ? redirectUrl.replace(
            /(.*)\/sso\/redirect(.*)/,
            '/sso/redirect$2'
        )
        : redirectUrl;
};

/**
 * @method getPasswordFromCookie 从Cookie中获取账号密码
 * @returns
 */
export const getPasswordFromCookie = () => {
    const username = Decrypt(getCookie('u'), UserNameSaltKey);
    const password = Decrypt(getCookie('p'), PwdSaltKey);
    return {
        username,
        password
    };
};
