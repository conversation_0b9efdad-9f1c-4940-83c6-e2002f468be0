import { ElMessageBox } from 'element-plus';

/**
 * @method DataImport Excel 文件导入
 * @param {Event} event input控件的事件
 * @param {String} fileField 文件字段Key
 * @param {Object} options 配置项
 * @param {Object} options.checkSize 是否检查文件大小
 * @param {Object} options.checkType 是否检查文件类型
 * @returns Promise
 */
export const DataImport = (event, fileField = 'file', { checkSize = true, checkType = true } = {}) => {
    const file = event.target.files[0];
    // 验证文件格式和大小
    const reg = /\.xl.{1,2}$/; // xls|xl|xla|xlt|xlm|xlc|xlw
    if (checkType && !reg.test(file.name)) {
        ElMessageBox({
            type: 'warning',
            title: '提示',
            message: '仅支持excel表文件格式'
        });
        return false;
    }
    if (checkSize && file.size > (50 * 1024 * 1024)) {
        ElMessageBox({
            type: 'warning',
            title: '提示',
            message: '文件大小不能超过50M'
        });
        return false;
    }
    const formData = new FormData();
    formData.append(fileField, file);
    return formData;
};

/**
 * @method JsonDataImport JSON 文件导入
 * @param {Event} event input控件的事件
 * @param {String} fileField 文件字段Key
 * @param {Object} options 配置项
 * @param {Object} options.checkSize 是否检查文件大小
 * @param {Object} options.checkType 是否检查文件类型
 * @returns Promise
 */
export const JsonDataImport = (event, fileField = 'file', { checkSize = true, checkType = true } = {}) => {
    const file = event.target.files[0];
    // 验证文件格式和大小
    const reg = /\.json$/; // json
    if (checkType && !reg.test(file.name)) {
        ElMessageBox({
            type: 'warning',
            title: '提示',
            message: '仅支持json文件格式'
        });
        return false;
    }
    if (checkSize && file.size > (50 * 1024 * 1024)) {
        ElMessageBox({
            type: 'warning',
            title: '提示',
            message: '文件大小不能超过50M'
        });
        return false;
    }
    const formData = new FormData();
    formData.append(fileField, file);
    return formData;
};

/**
 * @method DataExport 导出
 * @param {String} url 下载链接
 * @returns Promise
 */
export const DataExport = (url) => {
    let link = document.createElement('a');
    link.href = '/api' + url;
    link.click();
    link = null;
};
/**
 * @method asyncDataExport 导出
 * @param {String} url 下载链接
 * @returns Promise
 */
export const asyncDataExport = async (url) => {
    DataExport(url);
};
