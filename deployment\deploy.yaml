# Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
    name: asset-manage-web
    namespace: default
    labels:
        app: asset-manage-web
    annotations:
        kubesphere.io/description: 资产分析决策引擎-前端-原始端口 30768
spec:
    selector:
        matchLabels:
            app: asset-manage-web
    replicas: 1
    template:
        metadata:
            labels:
                app: asset-manage-web
        spec:
            containers:
                - name: asset-manage-web
                  image: harbor.pcitech.com/rtbigdata/frontend/asset-manage-web:latest
                  ports:
                      - containerPort: 30768
                  env:
                      # 是否启用nacos
                      - name: USE_NACOS
                        value: "true"
                      # nacos 服务ip
                      - name: NACOS_SERVER
                        valueFrom:
                            configMapKeyRef:
                                name: global-config
                                key: NACOS_SERVER
                  envFrom:
                      - configMapRef:
                            name: global-config
                  lifecycle:
                      preStop:
                          exec:
                              command: ["/bin/sh", "bin/run.sh stop"]
                  livenessProbe:
                      initialDelaySeconds: 5 # 5秒后开始检测，默认0秒
                      periodSeconds: 10 # 检测周期为 10秒，默认10秒
                      timeoutSeconds: 3 # 检测超时时间为3秒，默认1秒
                      successThreshold: 1 # 检测1次成功算成功，默认1次
                      failureThreshold: 3 # 检测3次失败算失败，默认3次
                      exec:
                          command: ["/bin/sh", "bin/healthcheck.sh"]
                  resources: # 添加资源限制
                      limits: # 资源限制
                          cpu: "1" # 限制为1个CPU核心
                          memory: "512Mi" # 限制为512MB内存
                      requests: # 资源请求
                          cpu: "0.5" # 请求0.5个CPU核心
                          memory: "256Mi" # 请求256MB内存

---
# Pod 绑定的 Service
apiVersion: v1
kind: Service
metadata:
    name: asset-manage-web
    namespace: default
    annotations:
        kubesphere.io/description: 资产分析决策引擎-前端-原始端口 30768
spec:
    # type: NodePort
    ports:
        - port: 30768 # Service 本身端口
          protocol: TCP
          targetPort: 30768 # Pod 监听的端口
    selector:
        app: asset-manage-web
