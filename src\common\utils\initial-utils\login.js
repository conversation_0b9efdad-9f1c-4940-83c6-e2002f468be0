import { LoginUtils } from '@ecp/ecp-login-component-vite';

// SSO 默认登录链接
const SSOLoginUrl = window.location.origin + '/sso/login';
/**
 * @method getLoginUrl 获取登录链接
 */
export const getLoginUrl = () => {
    // 如使用自定义登录页, 用这个↓
    // eslint-disable-next-line
    const currentContext = `${window.location.pathname}`.replace(/\//g, '').replace(/^s-/, '');
    const context = window.__POWERED_BY_QIANKUN__ || window.__POWERED_BY_WUJIE__ ? currentContext || PACKAGE_NAME : PACKAGE_NAME;
    const loginUrl = `${window.location.origin}/${context}/login/login.html`;

    // // 默认采用 sso 的默认登录页↓
    // const loginUrl = '';

    return loginUrl || SSOLoginUrl;
};

/**
 * @method goToLoginPage 调整至登录页
 *
 * @param { Object } resData 403 接口返回数据
 */
export const goToLoginPage = (resData) => {
    const param = {
        response: resData,
        loginUrl: getLoginUrl(),
        appName: PACKAGE_NAME,
        isComplexNetwork: true // 如应用部署网络环境复杂(例如: 需要支持非内网访问等), 请放开isComplexNetwork
    };
    if (USE_PORTAL && !window.__POWERED_BY_QIANKUN__ && !window.__POWERED_BY_WUJIE__) {
        window?.PortalTabsActions?.clear?.();
    }
    LoginUtils.loginInterceptors(param);
};
