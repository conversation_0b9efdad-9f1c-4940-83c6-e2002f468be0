# asset-manage-web

资产分析决策引擎

## 目录结构

见 [directory.md](./directory.md)

## 技术栈

-   [Vite 4](https://cn.vitejs.dev/)
-   [Vue 3](https://cn.vuejs.org/)
-   [Vue-router](https://router.vuejs.org/zh/)
-   [Pinia](https://pinia.vuejs.org/zh/)
-   [Element-plus](https://element-plus.org/zh-CN/#/zh-CN)
-   [Ecp-ui-plus](http://frontend.pcitech.online/ecp-ui-plus/)
-   [Wujie](https://wujie-micro.github.io/doc/)
-   [Lodash](https://www.lodashjs.com)

## 使用命令

### 安装依赖

```bash
pnpm install
```

### 本地开发

```bash
# 普通模式
pnpm serve

# 微前端模式
pnpm serve:uni
```

### 构建

```bash
pnpm build:uni
```

### 修复 ESLint 的问题

```bash
pnpm lint
```

## 推荐 VSCode 插件

-   [PCI IDEBoost](http://frontend.pcitech.online/)
-   [Vue - Official](https://marketplace.visualstudio.com/items?itemName=vue.volar) (需要禁用 Vetur)
-   [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
-   [GitHub Copilot](https://marketplace.visualstudio.com/items?itemName=github.copilot)
