<template>
    <app-business-panel mode="card">
        <ecp-layout-pagination class="app-panel-content-wrapper" :total="tableData.total"
            v-model:current-page="pagination.pageNo" v-model:page-size="pagination.pageSize"
            layout="prev, pager, next, sizes, jumper" @query="getShowAssertList" v-loading="loading">
            <template #head>
                <div class="box-container">
                    <span class="title">资产列表</span>
                    <ecp-button class="export-button" icon="ecp-icon-export" text="导出" :loading="loading"
                        @click="exportList" />
                </div>
            </template>
            <template #content>
                <app-dynamic-table class="table-container" style="height: 100%;" :tableData="tableData.list"
                    :tableConfig="{ cols: tableCols }">
                </app-dynamic-table>
            </template>
        </ecp-layout-pagination>
    </app-business-panel>
</template>

<script setup>
import * as Api from '@api/index';
import { defineProps, reactive, watch, onActivated } from 'vue';
import { OperatingAsset } from '@api/asset-panorama/operating-asset';
import { LineAssetAnalysis } from '@api/asset-analysis/line-asset-analysis';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';

const props = defineProps({
    selectedLine: String, // 选择的线路
    isInitialLoad: Boolean
});

const loading = ref(false);

const empty = reactive({
    chart1: false
});

const tableData = reactive({
    total: 0,
    list: []
});

const pagination = reactive({
    pageNo: 1,
    pageSize: 10
});

const columns = [
    { prop: 'createDate', label: '盘点时间' },
    { prop: 'assetCategory', label: '资产大类' },
    { prop: 'assetSubcategory', label: '资产中类' },
    { prop: 'assetSubtype', label: '资产小类' },
    { prop: 'associatedRoute', label: '所属路线' },
    { prop: 'assetLocation', label: '资产位置' },
    { prop: 'tokenDept', label: '使用部门' },
    { prop: 'totalAmount', label: '总数量' },
    { prop: 'usingAmount', label: '在用数量' },
    { prop: 'freeAmount', label: '闲置数量' },
    { prop: 'uselessAmount', label: '报废数量' }
];

const tableCols = computed(() => columns);

// 在页面被激活时重新调用
onActivated(() => {
    getShowAssertList();
});

const getShowAssertList = async () => {
    if (props.isInitialLoad) {
        return;
    }
    loading.value = true;
    try {
        const params = {
            pageNum: pagination.pageNo,
            pageSize: pagination.pageSize
            // udLine: props.selectedLine.line
        };
        if (props.selectedLine.line !== 'all') {
            params.udLine = props.selectedLine.line;
        }
        const response = await LineAssetAnalysis.getShowAssertList(params);
        console.log('表格数据', response);
        if (response && response.Data) {
            empty.chart1 = false;
            tableData.total = response.Total;
            tableData.list = response.Data.map(item => ({
                // assetNum: item.AssetNum,
                createDate: item.CreateDate,
                assetCategory: item.AssetCategory,
                assetSubcategory: item.AssetSubcategory,
                assetSubtype: item.AssetSubtype,
                associatedRoute: item.AssociatedRoute,
                assetLocation: item.AssetLocation,
                tokenDept: item.TokenDept,
                totalAmount: item.TotalAmount,
                usingAmount: item.UsingAmount,
                freeAmount: item.FreeAmount,
                uselessAmount: item.UselessAmount
            }));
        } else {
            empty.chart1 = true;
            console.error('响应数据不是数组:', response.Data);
        }
    } catch (error) {
        empty.chart1 = true;
        console.error('获取表格数据失败:', error);
        console.error('错误信息:', error.message);
        console.error('错误堆栈:', error.stack);
    } finally {
        loading.value = false;
    }
};

// 导出
const exportList = async () => {
    try {
        const response = await LineAssetAnalysis.exportList({ udLine: parseInt(props.selectedLine.line) });
        downloadBlobData(response);
        ElMessage.success('导出成功');
    } catch (error) {
        console.log('%c exportList error', 'color: red', error);
        ElMessage.success('导出失败');
    }
};

watch([() => pagination.pageNo, () => pagination.pageSize, () => props.selectedLine.line], () => {
    getShowAssertList();
});
</script>

<style scoped lang="scss">
app-business-panel {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;

}

.box-container {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    /* margin-bottom: 16px; */
    align-items: center;
    width: 100%;
}

.title {
    position: absolute;
    font-size: 16px;
    color: var(--color-text-primary);
    top: 0;
    z-index: 10;
    /* 确保标题层级在表格之上 */
}

.export-button {
    margin-left: auto;
    display: flex;
    align-items: center;
    padding: 5px 10px;
    /* margin-bottom: 16px; */
}

/* i {
    margin-right: 8px;
} */
:deep(.app-panel-content-wrapper) {
    display: flex;
    flex-direction: column;
    overflow: auto;
}

:deep(.ecp-layout-pagination) {
    display: flex;
    flex-direction: column;
    height: auto;
}

:deep(.app-dynamic-table) {
    height: 100%;
    /* margin-top: 16px; */
    overflow-x: auto;
    overflow: auto;
}

:deep(.app-dynamic-table thead) {
    position: sticky;
    top: 0;
    z-index: 10;
    /* 确保表头在表格内容之上 */
}

:deep(.ecpp-layout-pagination-content) {
    display: flex;
    position: relative;
    flex-direction: column;
    flex: 1 1 auto;
    overflow: hidden;
}

:deep(.content-main) {
    flex: 1 1 auto;
    overflow: hidden;
}

:deep(.ecpp-pagination.ecpp-layout-pagination-content-pagination) {
    flex-shrink: 0;
    /* margin-bottom: 16px; */
}
</style>
