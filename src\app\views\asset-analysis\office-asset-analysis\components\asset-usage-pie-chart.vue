<template>
    <app-business-panel class="asset-usage-pie-chart" title="资产使用情况" mode="card" v-loading="loading">
        <template #default>
            <app-chart-pie :graphData="graphData" chartType="ring" />
        </template>
    </app-business-panel>
</template>

<script setup>
import { OfficeAssetAnalysis } from '@api/asset-analysis/office-asset-analysis';
import { CHART_COLOR_LIST } from '@constants/enum-config';
import { onMounted } from 'vue';

const props = defineProps({
    assetUnit: {
        type: [String, Number],
        default: ''

    },
    isInitialLoad: Boolean
});

const loading = ref(false);

const graphData = ref([]);

const getChartData = async () => {
    console.log('%c getChartData1111111', 'font-size:18px;color:gold;font-weight:700;');
    console.log(props.isInitialLoad, 'props.isInitialLoad');
    console.log(props.assetUnit, 'props.assetUnit');
    if (props.isInitialLoad) {
        return;
    }
    loading.value = true;
    graphData.value = [];

    try {
        const params = {
            assetUnit: props.assetUnit
        };
        const res = await OfficeAssetAnalysis.getUsageRatio(params);
        const list = (res?.Data || []).map((item, index) => ({
            name: item.DataName,
            value: item.Percentage ? Math.round(item.Percentage * 1000) / 1000 : 0, // 保留三位小数
            color: CHART_COLOR_LIST[index]
        }));

        graphData.value = list;
    } catch (error) {
        console.log('%c getTransferStatDataTrend Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    }

    loading.value = false;
};

onMounted(() => {
    getChartData();
});

watch(
    () => props.assetUnit,
    (newData) => {
        if (newData) {
            getChartData();
        }
    },
    { immediate: true, deep: true }
);

</script>

<style scoped lang="scss">
.asset-usage-pie-chart {}
</style>
