import { ExampleRoutes } from './example/example.route';

import { AssetPanoramaRoutes } from './asset-panorama/asset-panorama.route';
import { AssetAnalysisRoutes } from './asset-analysis/asset-analysis.route';
import { AssetDetailsRoutes } from './asset-details/asset-details.route';
import { DataAcquisitionRoutes } from './data-acquisition/data-acquisition.route';

// Iframe
import { IframeRoutes } from './iframe/iframe.route';
// Exceptions
import { ExceptionRoutes } from './exception/exception.route';

export const ViewRoutes = [
    ...ExampleRoutes(),

    ...AssetPanoramaRoutes(),
    ...AssetAnalysisRoutes(),
    ...AssetDetailsRoutes(),
    ...DataAcquisitionRoutes(),

    /* Iframe 嵌套路由 */
    ...IframeRoutes(),
    /* Exceptions 放最后 */
    ...ExceptionRoutes()
];
