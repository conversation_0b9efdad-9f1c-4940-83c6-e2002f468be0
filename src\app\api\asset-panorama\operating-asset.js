import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

export const OperatingAsset = {
    /**
     * 运营资产
     * @method getOperatingAssetType 按类型查找数目
     */
    getOperatingAssetType (params) {
        return request.post(`${AssetsAnalysisService}/asset/queryByType`, params).then(
            ({ data }) => data
        );
    },
    /**
     * 运营资产
     * @method getLineCode 获取当前线路代码集合
     */
    getLineCode (params) {
        return request({
            url: `${AssetsAnalysisService}/asset/getLineCode`,
            method: 'get',
            params
        }).then(({ data }) => data);
    },
    /**
     * 运营资产
     * @method getAssetList 获取运营资产大类信息集合
     */
    getAssetList (params) {
        return request({
            url: `${AssetsAnalysisService}/asset/getAssetList`,
            method: 'get',
            params
        }).then(({ data }) => data);
    },
    /**
     * 运营资产
     * @method getOperatingAssetRoute 按路线查找数目
     */
    getOperatingAssetRoute (params) {
        return request.post(`${AssetsAnalysisService}/asset/queryByLine`, params).then(
            ({ data }) => data
        );
    },
    /**
     * 运营资产
     * @method getAnalyseByLine 按路线进行资产的多指标分析
     */
    getAnalyseByLine (params) {
        return request.post(`${AssetsAnalysisService}/asset/analyseByLine`, params).then(
            ({ data }) => data
        );
    },
    /**
     * 运营资产
     * @method getAssetStatusList 获取运营资产状态信息集合
     */
    getAssetStatusList (params) {
        return request({
            url: `${AssetsAnalysisService}/asset/getAssetStatusList`,
            method: 'get',
            params
        }).then(({ data }) => data);
    }

};
