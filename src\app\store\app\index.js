const useAppStore = defineStore('app', {
    state: () => ({
        pageRouteList: [],
        waitCloseUid: null
    }),
    actions: {
        pushPageRoute (pageRoute) {
            this.pageRouteList.push(pageRoute);
        },
        getCurrentPageRoute () {
            return this.pageRouteList[this.pageRouteList.length - 1];
        },
        removePageRoute (_uid) {
            this.pageRouteList = this.pageRouteList.filter(({ id }) => id !== _uid);

            this.waitCloseUid = null;
        }
    }
});

export default useAppStore;
