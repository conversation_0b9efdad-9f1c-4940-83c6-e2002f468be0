import { h } from 'vue';

import usePermissionStore from '@store/permission';

export default {
    name: 'redirect',
    render () {
        return h('i');
    },
    beforeRouteEnter (to, from, next) {
        const permissionStore = usePermissionStore();

        next(vm => {
            // 通过 `vm` 访问组件实例
            const { params, query } = vm.$route;
            const paramsUrl = params?.url;
            const path = `/${(paramsUrl || '').replace(/^\/+/, '')}` || '';
            const cachedViews = permissionStore.cachedViews;
            const matchedItem = cachedViews.find((view) => path.match(view.path));

            // console.log(`%c matchedItem`, 'font-size:18px;color:purple;font-weight:700;', path, matchedItem);

            matchedItem && permissionStore.delCachedView(matchedItem);
            vm.$nextTick(() => {
                matchedItem && permissionStore.addCachedView(matchedItem);
                vm.$nextTick(() => {
                    vm.$router.replace({
                        path,
                        query
                    });
                });
            });
        });
    }
};
