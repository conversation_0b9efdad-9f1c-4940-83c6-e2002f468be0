<script setup>
import { defineProps } from 'vue';
import { ElMessage } from 'element-plus';
// 路由映射表
const context = 'asset-panorama';
const routerMap = {
    房产: `${context}-house-asset`,
    土地: `${context}-land-asset`,
    物业: `${context}-property-asset`,
    文体: `${context}-advertisement-asset`,
    运营: `${context}-operating-asset`,
    办公: `${context}-office-asset`
};
const tempTitle = {
    房产: '房产详情页面',
    土地: '土地详情页面',
    物业: '物业详情页面',
    文体: '文体广告详情页面',
    运营: '运营资产详情页面',
    办公: '办公资产详情页面'
};
const props = defineProps({
    LineCode: {
        type: String,
        default: '线路代码'
    },
    LineName: {
        type: String,
        default: '线路名称'
    },
    // 房产
    TotalArea: {
        type: Number,
        default: 0
    },
    // 土地
    TotalLandArea: {
        type: Number,
        default: 0
    },
    // 物业
    TotalPropertyArea: {
        type: Number,
        default: 0
    },
    // 文体
    TotalAdvert: {
        type: Number,
        default: 0
    },
    // 运营
    TotalOperationAsset: {
        type: Number,
        default: 0
    },
    // 办公
    TotalWorkAsset: {
        type: Number,
        default: 0
    },
    router: {
        type: Object,
        required: true
    }
});
const goto = (type) => {
    if (tempTitle[type]?.startsWith('暂未开放')) {
        ElMessage.info({ message: '该功能暂未开放', showClose: true, grouping: true });
        return;
    }
    const routeName = routerMap[type];
    if (props.router && props.router.push && routeName) {
        props.router.push({ name: routeName });
    } else {
        console.error('路由未正确初始化或未定义对应路由名称');
    }
};
</script>

<template>
    <div class="map-pop">
        <div class="map-pop-title">{{ LineName }}</div>
        <div class="map-pop-item" @click="goto('房产')">
            <span>房产</span>
            <span>{{ TotalArea }}万平方</span>
        </div>
        <div class="map-pop-item" @click="goto('土地')">
            <span>土地</span>
            <span>{{ TotalLandArea }}亩</span>
        </div>
        <div class="map-pop-item" @click="goto('物业')">
            <span>物业</span>
            <span>{{ TotalPropertyArea }}万平方</span>
        </div>
        <div class="map-pop-item" @click="goto('文体')">
            <span>文体</span>
            <span>{{ TotalAdvert }}个</span>
        </div>
        <div class="map-pop-item" @click="goto('运营')">
            <span>运营</span>
            <span>{{ TotalOperationAsset }}项</span>
        </div>
        <div class="map-pop-item" @click="goto('办公')">
            <span>办公</span>
            <span>{{ TotalWorkAsset }}项</span>
        </div>
    </div>
</template>

<style scoped lang="scss">
.map-pop {
    display: flex;
    flex-direction: column;
    width: 168px;
    padding: 16px;
    gap: 12px;
    border-radius: 6px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid #2A61FF;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(25, 15, 15, 0.07), 0 0 1px 0 rgba(0, 0, 0, 0.08);

    &-title {
        color: #2A61FF;
    }

    &-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer; // 添加鼠标指针样式

        &:hover {
            background-color: #f5f7fa; // 添加悬停效果
        }
    }
}
</style>
