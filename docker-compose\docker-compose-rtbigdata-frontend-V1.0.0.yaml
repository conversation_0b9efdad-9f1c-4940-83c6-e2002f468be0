# Description: 资产分析决策引擎 前端服务 V1.0.0 docker-compose 配置文件
# Path: docker-compose/docker-compose-asset-manage-web-V1.0.0.yaml

version: "3.7"
services:
    asset-manage-web:
        restart: always
        image: harbor.pcitech.com/rtbigdata/frontend/asset-manage-web:1.0.0-amd64
        container_name: wuhan_asset-manage-web
        privileged: true
        # 出现端口号被占用服务无法启动的问题时，可以尝试注释掉network_mode、取消注释ports
        #ports:
        #    - 30768:30768
        network_mode: host
        entrypoint:
            - "sh"
            - "-c"
            - "sh bin/run.sh start"
        deploy:
            resources:
                limits:
                    cpus: "0.5"
                    memory: 200M
        healthcheck:
            test: ["CMD", "sh", "-c", "sh /opt/base/bin/healthcheck.sh"]
            interval: 30s
            timeout: 5s
            retries: 5
            start_period: 30s
        environment:
            - "NACOS_SERVER=${NACOS_SERVER}"
        volumes:
            - /data-logs:/data-logs
            - /data-logs/wuhan/asset-manage-web/logs:/opt/base/logs
