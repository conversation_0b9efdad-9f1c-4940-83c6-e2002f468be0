<template>
    <app-form-page class="coming-soon" :show-header="false" :page-scroller="false">
        <template #content>
            <app-status class="coming-soon-status app-form-page-content-panel" size="large" title="暂未开放，敬请期待~"
                        :subtitle="title" />
        </template>
    </app-form-page>
</template>

<script>
import useGlobalStore from '@store/global-config';
export default {
    name: 'coming-soon',
    setup () {
        const { globalConfigs } = useGlobalStore();
        return {
            title: globalConfigs?.IMPORT_CONFIGS?.title
        };
    },
    data () {
        return {};
    },
    methods: {}
};
</script>

<style scoped lang="scss">
.coming-soon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    &-status {
        flex: 0 0 auto;
        justify-content: center;
        // padding-bottom: 200px;
    }
}
</style>
