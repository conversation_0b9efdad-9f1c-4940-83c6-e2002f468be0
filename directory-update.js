import fs from 'fs';
import path from 'path';
import ignore from 'ignore';

// 定义一个异步函数来读取和解析.gitignore文件
async function getGitIgnorePatterns (gitIgnorePath) {
    const gitIgnoreContent = await fs.promises.readFile(gitIgnorePath, 'utf8');
    const ig = ignore().add(gitIgnoreContent);
    return ig;
}

// 定义一个异步函数来获取目录结构
async function getDirectoryStructure (dirPath, gitIgnore, depth = 0, parentIsLast = []) {
    let result = ''; // 初始化结果字符串
    const items = await fs.promises.readdir(dirPath); // 读取目录中的所有项目

    // 对项目进行排序，目录在前，文件在后，同类型按字母顺序排序
    const sortedItems = items.sort((a, b) => {
        const fullPathA = path.join(dirPath, a);
        const fullPathB = path.join(dirPath, b);
        const statA = fs.statSync(fullPathA);
        const statB = fs.statSync(fullPathB);

        if (statA.isDirectory() && statB.isDirectory()) {
            return a.localeCompare(b);
        } else if (statA.isDirectory()) {
            return -1;
        } else if (statB.isDirectory()) {
            return 1;
        } else {
            return a.localeCompare(b);
        }
    });

    // 遍历所有项目
    for (let i = 0; i < sortedItems.length; i++) {
        const item = sortedItems[i];
        const fullPath = path.join(dirPath, item);
        const relativePath = path.relative(dirPath, fullPath); // 相对于当前目录的路径

        // 如果项目在.gitignore文件中，或者项目是.git目录 或*.d.ts文件，则跳过
        if (gitIgnore.ignores(relativePath) || item === '.git' || item.match(/\.d\.ts$/)) {
            continue;
        }

        const stat = await fs.promises.stat(fullPath);
        // 根据项目在目录中的位置，选择前缀
        const prefix = parentIsLast.map(isLast => (isLast ? '    ' : '|   ')).join('') + (i === sortedItems.length - 1 ? '└── ' : '├── ');

        // 如果项目是目录，则递归调用函数处理
        if (stat.isDirectory()) {
            result += prefix + item + '\n';
            result += await getDirectoryStructure(fullPath, gitIgnore, depth + 1, [...parentIsLast, i === sortedItems.length - 1]);
        } else {
            // 如果项目是文件，则直接添加到结果字符串中
            result += prefix + item + '\n';
        }
    }

    // 返回结果字符串
    return result;
}

// 获取.gitignore中的模式
const gitIgnore = await getGitIgnorePatterns('.gitignore');
// 获取当前目录的目录结构
const directoryStructure = await getDirectoryStructure('.', gitIgnore);
// 将目录结构写入到带有时间戳的.md文件中
await fs.promises.writeFile(`directory-${Date.now()}.md`, `\`\`\`bash
${directoryStructure}
\`\`\`
`);
