/* Color Default Variables */
@use "sass:map";

// types
$types: primary, success, warning, danger, error, info;

// Color
$theme-colors: (
    "white": #ffffff,
    "black": #000000,
    "primary": ("base": #0367FC,
    ),
    "success": ("base": #11B062,
    ),
    "warning": ("base": #faad14,
    ),
    "danger": ("base": #f5222d,
    ),
    "error": ("base": #f5222d,
    ),
    "info": ("base": rgba(#000000, 45%),
    ),
);

$text-color: (
    "light": var(--color-black),
    "primary": var(--color-black-opacity-8-5),
    "regular": var(--color-black-opacity-6-5),
    "secondary": var(--color-black-opacity-4-5),
    "placeholder": var(--color-black-opacity-2-5),
    "mark": var(--color-black-opacity-1-5),
    "shadow": var(--color-black-opacity-0-9),
    "light-darken": var(--color-white),
    "disabled": var(--color-black-opacity-2-5),
);
$text-color-reverse: (
    "light-reverse": var(--color-white),
    "primary-reverse": var(--color-white-opacity-8-5),
    "regular-reverse": var(--color-white-opacity-6-5),
    "secondary-reverse": var(--color-white-opacity-4-5),
    "placeholder-reverse": var(--color-white-opacity-2-5),
    "mark-reverse": var(--color-white-opacity-1-5),
    "shadow-reverse": var(--color-white-opacity-0-9),
    "light-darken-reverse": var(--color-black),
    "disabled-reverse": var(--color-white-opacity-2-5),
);

$border-color: (
    "": var(--color-black-opacity-1-5),
    "base": var(--color-black-opacity-1-5),
    "light": var(--color-black-opacity-0-9),
    "lighter": var(--color-black-opacity-0-4),
    "extra-light": var(--color-black-opacity-0-2),
    "dark": rgba(var(--color-black-rgb), 14%),
    "darker": var(--color-black-opacity-2),
    "none": var(--color-black-opacity-0),
    "dash": var(--color-black-opacity-1-5),
    "disabled": var(--color-black-opacity-0-9),
    "hover": var(--color-primary),
);

$fill-color: (
    "": #f0f2f5,
    "base": var(--color-white),
    "light": #f5f7fa,
    "lighter": #fafafa,
    "extra-light": #fafcff,
    "dark": #ebedf0,
    "darker": #e6e8eb,
    "blank": var(--color-white),
    "primary": var(--color-black-opacity-6-5),
    "secondary": var(--color-black-opacity-4-5),
    "placeholder": var(--color-black-opacity-2-5),
);

$bg-color: (
    "": var(--color-white),
    "page": #F2F4F8,
    "base": var(--color-black-opacity-0-4),
    "light": var(--color-black-opacity-0-2),
    "primary": var(--color-white),
    "dialog": var(--background-color-primary),
    "overlay": var(--background-color-primary),
);

// Border
$border-width: 1px;
$border-style: solid;
$border: (
    "width": $border-width,
    "width-base": $border-width,
    "style": $border-style,
    "style-base": $border-style,
    "": $border-width $border-style var(--border-color-base),
    "base": $border-width $border-style var(--border-color-base),
    "dash": $border-width dashed var(--border-color-base),
    "hover": $border-width $border-style var(--border-color-hover),
);

// Box-shadow
$box-shadow-color-base: var(--color-black-opacity-1-5);
$box-shadow-color-light: rgba(var(--color-black-rgb), 12%);
$box-shadow: (
    "": (0px 12px 32px 4px rgba(0, 0, 0, 0.04),
        0px 8px 20px rgba(0, 0, 0, 0.08),
    ),
    "base": 0px 2px 8px 0px $box-shadow-color-base,
    "bottom-select": 0px 2px 8px 0px var(--box-shadow-color-base),
    "bottom": 0px 4px 12px 0px var(--box-shadow-color-base),
    "left": -2px 0px 8px 0px var(--box-shadow-color-base),
    "right": 2px 0px 8px 0px var(--box-shadow-color-base),
    "top": 0px -2px 8px 0px var(--box-shadow-color-base),
    "input-focus": 0px 0px 0px 2px var(--color-primary-opacity-2-5),
    "light": 0px 0px 12px $box-shadow-color-light,
    "lighter": 0px 0px 6px $box-shadow-color-light,
    "dark": (0px 16px 48px 16px var(--color-black-opacity-0-8),
        0px 12px 32px $box-shadow-color-light,
        0px 8px 16px -8px rgba(var(--color-black-rgb), 16%),
    ),
);

// Disable default
$disabled: (
    "bg-color": var(--background-color-base),
    "text-color": var(--color-text-placeholder),
    "border-color": var(--border-color-light),
    "fill-color": var(--background-color-base),
    "color-base": var(--color-text-placeholder),
);

// overlay
$overlay-color: (
    "": var(--color-black-opacity-8),
    "light": var(--color-black-opacity-7),
    "lighter": var(--color-black-opacity-5),
);

// mask
$mask-color: (
    "": var(--color-white-opacity-9),
    "extra-light": var(--color-white-opacity-3),
);

// Scrollbar
$scrollbar: (
    "opacity": 0,
    "bg-color": map.get($theme-colors, "black"),
    "bg-color-webkit": transparent,
    "color": map.get($theme-colors, "black"),
    "visible-opacity": 0.15,
    "visible-bg-color-webkit": var(--color-black-opacity-1-5),
    "hover-opacity": 0.25,
    "hover-bg-color": map.get($theme-colors, "black"),
    "hover-bg-color-webkit": var(--color-black-opacity-2-5),
    "color-hover": var(--color-black-opacity-2-5),
);

// Table
$table: (
    "header-bg-color": var(--color-primary-light-9),
    "row-hover-bg-color": var(--color-primary-light-9),
    "row-striped": var(--color-primary-light-9-5),
);