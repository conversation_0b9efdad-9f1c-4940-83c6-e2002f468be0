import { createRouter, createWebHashHistory } from 'vue-router';
import { MicroUtils } from '@ecp/ecp-ui-plus';

import { ViewRoutes } from '@views/view-route';

// // 作为乾坤子模块不能添加默认路由
// // TODO 存疑, 不加载默认路由怎么处理子应用页面404的情况？
// if (window.__POWERED_BY_QIANKUN__) {
//     ViewRoutes.pop();
// }

const router = createRouter({
    history: createWebHashHistory(window.__POWERED_BY_QIANKUN__ ? '/asset-manage-web' : '/'),
    routes: ViewRoutes
});

let initComplete = false;
router.beforeEach((to, from, next) => {
    const uri = MicroUtils.getUri(window.location.href);
    const isWujieSub = USE_PORTAL && (!window.__POWERED_BY_WUJIE__ && !(uri || '').match(PACKAGE_NAME));
    if (isWujieSub && !initComplete) {
        initComplete = true;
        next(false);
    }
    next();
});

export default router;
