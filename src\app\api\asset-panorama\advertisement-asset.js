import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';
export const AdvertisementAsset = {
 
     /**
     * 出租-待出租率top10车站
     */
     getAdvertisingTop10(params) {
        return request({
            url: `${AssetsAnalysisService}/Advertising/Top10Station`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    },

      /**
     * 其他资源
     */
      getAdvertisingOther() {
        return request({
            url: `${AssetsAnalysisService}/Advertising/Other`,
            method: "get",
        }).then(({ data }) => data);
    },

      /**
     * 线路广告资源租赁情况-已租赁面积
     */
      getLineAlreadyLeaseArea(params) {
        return request({
            url: `${AssetsAnalysisService}/Advertising/LineAlreadyLeaseArea`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    },

      /**
     * 线路广告资源租赁情况-可租赁面积
     */
      getLineCanLeaseArea(params) {
        return request({
            url: `${AssetsAnalysisService}/Advertising/LineCanLeaseArea`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    },

        /**
     * 线路广告资源租赁情况-出租率
     */
          getLineCanLeaseArea(params) {
            return request({
                url: `${AssetsAnalysisService}/Advertising/LineOccupancyRate`,
                method: "get",
                params: params
            }).then(({ data }) => data);
        },


    /**
     * 线路
     * @method getLineCode 获取当前线路代码集合
     */
    getLineCode (params) {
        return request({
            url: `${AssetsAnalysisService}/asset/getLineCode`,
            method: 'get',
            params
        }).then(({ data }) => data);
    },

    /**
     * 车站广告资源租赁情况-已租赁面积
     */
          getStationAlreadyLeaseArea(params) {
            return request({
                url: `${AssetsAnalysisService}/Advertising/StationAlreadyLeaseArea`,
                method: "get",
                params: params
            }).then(({ data }) => data);
        },
    
          /**
         * 车站广告资源租赁情况-可租赁面积
         */
          getStationCanLeaseArea(params) {
            return request({
                url: `${AssetsAnalysisService}/Advertising/StationCanLeaseArea`,
                method: "get",
                params: params
            }).then(({ data }) => data);
        },
    
            /**
         * 车站广告资源租赁情况-出租率
         */
              getStationCanLeaseArea(params) {
                return request({
                    url: `${AssetsAnalysisService}/Advertising/StationOccupancyRate`,
                    method: "get",
                    params: params
                }).then(({ data }) => data);
            },


    /**
     * 线路广告资源租赁情况
     */
      getLineAdSituation(params) {
        return request({
            url: `${AssetsAnalysisService}//Advertising/LineAdSituation`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    },


       /**
         * 车站广告资源租赁情况
         */
       getStationAdSituation(params) {
        return request({
            url: `${AssetsAnalysisService}/Advertising/StationAdSituation`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    },
};
