<template>
    <div class="menu-4" v-loading="loading">
        <el-auto-resizer>
            <template #default="{ height, width }">
                <el-table-v2 :columns="columns" :data="data" :width="width" :height="height" fixed @column-sort="onSort" />
            </template>
        </el-auto-resizer>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { TableV2FixedDir, TableV2SortOrder } from 'element-plus';
const generateColumns = (length = 10, prefix = 'column-', props) =>
    Array.from({ length }).map((_, columnIndex) => ({
        ...props,
        key: `${prefix}${columnIndex}`,
        dataKey: `${prefix}${columnIndex}`,
        title: `Column ${columnIndex}`,
        width: 250
    }));

const data = ref([]);
const loading = ref(false);

const generateData = (
    columns,
    length = 200,
    prefix = 'row-'
) =>
    Array.from({ length }).map((_, rowIndex) => {
        return columns.reduce(
            (rowData, column, columnIndex) => {
                rowData[column.dataKey] = `Row ${rowIndex} - Col ${columnIndex}`;
                return rowData;
            },
            {
                id: `${prefix}${rowIndex}`,
                parentId: null
            }
        );
    });

const columns = generateColumns(10);

columns[0].fixed = true;
columns[1].fixed = TableV2FixedDir.LEFT;
columns[9].fixed = TableV2FixedDir.RIGHT;
columns[0].width = 150;
columns[1].width = 150;
columns[9].width = 150;

for (let i = 0; i < 3; i++) columns[i].sortable = true;

const sortBy = ref({
    key: 'column-0',
    order: TableV2SortOrder.ASC
});

const onSort = (_sortBy) => {
    data.value = data.value.reverse();
    sortBy.value = _sortBy;
};

onMounted(() => {
    loading.value = true;
    setTimeout(() => {
        loading.value = false;
        data.value = generateData(columns, 200);
    }, 1500);
});
</script>
<style lang="scss" scoped>
.menu-4 {
    display: block;
    width: 100%;
    height: 100%;
}
</style>
