import { ElMessage } from 'element-plus';
// import * as Prefix from './prefix.config';

import { BaseUrl } from '@api/prefix.config.js';
import { goToLoginPage } from '@utils/initial-utils/login';

const server = axios.create({
    baseURL: BaseUrl
});

// 设置默认请求头信息
// Axios.defaults.headers.post['Content-Type'] = 'application/json;charset=UTF-8';
// Axios.defaults.headers.delete['Content-Type'] =
//     'application/json;charset=UTF-8';
// Axios.defaults.headers.put['Content-Type'] = 'application/json;charset=UTF-8';

// 新建请求拦截器
server.interceptors.request.use(
    // 正常请求拦截
    requestConfig => {
        // requestConfig.baseURL = Prefix.BaseUrl;
        return requestConfig;
    },
    // 错误请求拦截
    error => {
        console.log('%c [ error ]-35', 'font-size:13px; background:#dfebde; color:#ffffff;', error);
        return Promise.reject(error);
    }
);

async function fileToJson (file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = res => {
            const { result } = res.target; // 得到字符串
            try {
                const data = JSON.parse(result); // 解析成json对象
                resolve(data);
            } catch {
                resolve(file);
            }
        }; // 成功回调
        reader.onerror = err => {
            reject(err);
        }; // 失败回调
        reader.readAsText(new Blob([file]), 'utf-8'); // 按照utf-8编码解析
    });
}

// 新建响应拦截器
server.interceptors.response.use(
    async response => {
        // mock接口数据返回数据为随机数据，不做拦截
        if (response.config.url.includes('mock')) {
            return response;
        }

        if (response.data instanceof Blob) {
            response.data = await fileToJson(response.data);
        } else {
            // 登录拦截处理
            if (response?.data?.OpCode === 403) {
                goToLoginPage(response.data);
                return response;
            }

            if (response.data.OpCode !== 0) {
                ElMessage.error({
                    message: response.data.OpDesc || '服务访问异常',
                    showClose: true,
                    grouping: true
                });
                return Promise.reject(new Error(response.data.OpDesc));
            }
        }
        return response;
    },
    // 错误响应拦截
    error => {
        if (!axios.isCancel(error)) {
            ElMessage.error({
                message: error.message || '服务访问异常',
                showClose: true,
                grouping: true
            });
        }
        return Promise.reject(error);
    }
);

export default server;
