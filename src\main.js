import App from './App.vue';

import { createLifecyle } from 'vite-plugin-legacy-qiankun';

import ElementPlus, { useGlobalConfig } from 'element-plus';
import EcpUIPlus, { ElementDefaultConfig } from '@ecp/ecp-ui-plus';

import EcpChart from 'ecp-chart/v3';
import 'ecp-chart/lib/v3/es/style.css';

import CommonPart from '@common';

import router from '@/app/router';
import store from '@store';

import useGlobalStore from '@store/global-config';

import '@styles/index';
import 'virtual:svg-icons-register';

import * as InitialUtils from '@/common/utils/initial-utils';

// 必须在调用 ElementPlus 指令类组件（如 ElMessage）之前配置
const ElGlobalConfig = useGlobalConfig();
ElGlobalConfig.value = ElementDefaultConfig;

const initApp = async (container) => {
    try {
    // 如果还有其它渲染前置处理，请在 src、common、utils、initial-utils、index.js 的 systemInitial 里面添加
        await InitialUtils.systemInitial({
            loadingTarget: (container || document).querySelector('#app')
        });
    } catch (error) {
        console.log(
            '%c systemInitial Caught Error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    }
    return Promise.resolve();
};

const renderApp = async (props = {}) => {
    const VueApp = createApp(App, {
        ...props
    });
    VueApp.use(router);
    VueApp.use(store);
    VueApp.use(ElementPlus);
    VueApp.use(EcpUIPlus);
    VueApp.use(EcpChart);
    VueApp.use(CommonPart);

    await initApp(props.componentName && props.container);

    // 单应用需要更新文档title
    if (!window.__POWERED_BY_QIANKUN__ && !window.__POWERED_BY_WUJIE__) {
        const globalStore = useGlobalStore();
        const globalConfigs = globalStore?.globalConfigs || {};

        document.title = globalConfigs.IMPORT_CONFIGS.title;
    }

    VueApp.mount(props.container
        ? props.container.querySelector('#app')
        : '#app');
    return VueApp;
};

let instance = null;

const lifeCycle = {
    bootstrap () {
        console.log('%c [asset-manage-web bootstrap]', 'font-size:18px;color:purple;font-weight:700;', PACKAGE_NAME);
    },
    async mount (props) {
        instance = await renderApp(props);
        console.log('%c [asset-manage-web mount]: props from main framework', 'font-size:18px;color:purple;font-weight:700;', PACKAGE_NAME, props);
    },
    unmount () {
        console.log('%c [asset-manage-web unmount]', 'font-size:18px;color:purple;font-weight:700;', PACKAGE_NAME);
        instance?.unmount?.();
    }
};

if (window.__POWERED_BY_QIANKUN__) { // 当前应用是乾坤子应用
    // vite-plugin-legacy-qiankun 插件 bug，无法支持破折号 注意！！！
    const packageNameQiankun = PACKAGE_NAME.replace(/-/g, '_');

    createLifecyle(packageNameQiankun, lifeCycle);
} else if (window.__POWERED_BY_WUJIE__) { // 当前应用是无界子应用
    // 需要将 router 挂载到 window 上，以便 HistoryApp 代处理切换子应用但子应用内页面没更新的问题
    window.__MICRO_APP_ROUTER__ = router;

    window.__WUJIE_MOUNT = () => lifeCycle.mount();
    window.__WUJIE_UNMOUNT = () => lifeCycle.unmount();

    requestAnimationFrame(() => {
        console.log('%c [asset-manage-web mount manual]', 'font-size:18px;color:indianred;font-weight:700;', window.__WUJIE);
        window.__WUJIE.mount();
    });
} else { // 单应用
    renderApp();
}
