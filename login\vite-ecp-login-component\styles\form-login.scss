@use "./config.scss" as *;

.ecpp-login-form {
    --ecpp-login-form-width: 284px;
    --ecpp-login-form-padding-top: 0;
    --ecpp-login-form-padding-bottom: 0;
    --ecpp-login-form-padding-left: 24px;
    --ecpp-login-form-padding-right: 24px;

    --ecpp-login-form-pki-button-width: auto;
    --ecpp-login-form-pki-button-height: 40px;
    --ecpp-login-form-pki-button-background: var(--ecpp-login-border-color-light);
    --ecpp-login-form-pki-button-background-hover: var(--ecpp-login-border-color-light);
}

// 登录主体组件样式
.ecpp-login-form {
    width: var(--ecpp-login-form-width);
    background: var(--ecpp-login-background-base);

    &-wrapper {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .#{$namespace}-form {
        width: 100%;
        flex: 1 0 auto;
        padding: var(--ecpp-login-form-padding-top) var(--ecpp-login-form-padding-right) var(--ecpp-login-form-padding-bottom) var(--ecpp-login-form-padding-left);
    }

    &-input {
        &.#{$namespace}-input {

            .#{$namespace}-input__wrapper {
                padding: 0 16px;
                height: 40px;

                .elp-input__prefix {
                    color: var(--ecpp-login-color-black);
                }

                .elp-input__password {
                    color: var(--ecpp-login-text-secondary);
                }
            }

            .#{$namespace}-input__inner {
                caret-color: var(--ecpp-login-color-primary);
                color: var(--ecpp-login-color-black);
            }

            &:focus-within {
                &::after {
                    width: 100%;
                }
            }
        }
    }

    &-button {
        width: 100%;

        &.#{$namespace}-button--medium {
            height: 40px !important;
            padding: 0 16px !important;
            font-size: 14px !important;
        }

        &.#{$namespace}-button--small {
            height: 32px !important;
            padding: 0 16px !important;
            font-size: 14px !important;
        }
    }

    .#{$namespace}-form-item.is-error {
        .ecpp-login-form-input {
            &.#{$namespace}-input {

                .#{$namespace}-input__inner {
                    caret-color: var(--ecpp-login-color-danger);
                }

                &::after {
                    background-color: var(--ecpp-login-color-danger);
                }
            }
        }
    }

    .icon {
        position: absolute;
        left: 5px;
    }

    // .#{$namespace}-input__inner {
    //     padding-left: 32px !important;
    // }
    .remember-password {
        .#{$namespace}-checkbox__label {
            padding-left: 8px;
        }
    }

    .btn-wrap {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        margin-top: 64px;
    }

    .only-pki-button {
        padding: var(--ecpp-login-form-padding-top) var(--ecpp-login-form-padding-right) var(--ecpp-login-form-padding-bottom) var(--ecpp-login-form-padding-left);
        margin-bottom: 32px;
    }

    .footer-pki-button {
        font-size: 14px;
        width: var(--ecpp-login-form-pki-button-width);
        height: var(--ecpp-login-form-pki-button-height);
        line-height: var(--ecpp-login-form-pki-button-height);
        // color: var(--ecpp-login-text-regular);
        text-align: center;
        background-color: var(--ecpp-login-border-color-light);
        border: none;
        border-radius: 0;

        &:hover {
            background-color: var(--ecpp-login-border-color-light);
        }
    }

    .second-login-view {
        width: var(--ecpp-login-form-width);
        padding: 16px;

        .back-btn {
            cursor: pointer;
            width: max-content;

            &:hover {
                .#{$namespace}-icon-back {
                    color: var(--ecpp-login-color-primary);
                }
            }
        }

        .tips {
            margin-top: 16px;
            padding: 0 8px;
            font-size: 14px;
            color: var(--ecpp-login-text-regular);
        }

        .login-way-box {
            max-height: 228px;
            overflow: auto;
            margin-top: 24px;
            padding: 0 8px;

            .login-way-card {
                cursor: pointer;
                height: 108px;
                border: var(--ecpp-login-border-base);
                border-style: dashed;
                display: flex;
                border-radius: 4px;

                &-main {
                    flex: 1 1 auto;
                    padding: 8px 16px;

                    .second-title,
                    .button {
                        font-size: 14px;
                        color: var(--ecpp-login-text-secondary);
                    }

                    .title {
                        margin-top: 8px;
                        font-size: 18px;
                        color: var(--ecpp-login-text-regular);
                    }

                    .button {
                        margin-top: 16px;
                    }
                }

                &-decoration {
                    flex: 0 0 90px;

                    .background {
                        height: 53px;
                        border-bottom-left-radius: 80%;
                        border-bottom-right-radius: 10%;
                        background: linear-gradient(0,
                                var(--ecpp-login-color-primary) 0%,
                                transparent 100%);
                        opacity: 0.06;
                    }

                    .ecpp-login-iconfont {
                        font-size: 50px;
                        color: var(--ecpp-login-color-primary);
                        opacity: 0.35;
                        position: relative;
                        top: -37px;
                        left: 17px;
                    }
                }

                &:not(:first-of-type) {
                    margin-top: 8px;
                }

                &:hover {
                    border: 1px solid var(--ecpp-login-color-primary);
                }
            }
        }
    }
}