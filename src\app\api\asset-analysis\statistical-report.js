import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

const prefix = `${AssetsAnalysisService}/statisticalReport`;

export const StatisticalReport = {
    /**
     * 统计报表
     * @method GetAssetChangeTable 资产变化情况统计表
     */
    GetAssetChangeTable (params) {
        return request({ url: `${prefix}/GetAssetChangeTable`, method: 'get', params }).then(({ data }) => data);
    },
    /**
     * 统计报表
     * @method ExportAssetChangeTable 导出资产变化情况统计表
     */
    ExportAssetChangeTable (params) {
        return request({ url: `${prefix}/ExportAssetChangeTable`, method: 'post', params, responseType: 'blob' });
    },
    /**
     * 统计报表
     * @method GetAssetUseTable 获取资产使用情况统计表
     */
    GetAssetUseTable (params) {
        return request({ url: `${prefix}/GetAssetUseTable`, method: 'get', params }).then(({ data }) => data);
    },
    /**
     * 统计报表
     * @method ExportAssetUseTable 导出资产变化情况统计表
     */
    ExportAssetUseTable (params) {
        return request({ url: `${prefix}/ExportAssetUseTable`, method: 'post', params, responseType: 'blob' });
    },
    /**
     * 统计报表
     * @method ShowLineList 查询线路设备使用情况表
     */
    ShowLineList (params) {
        return request({ url: `${prefix}/showLineList`, method: 'get', params }).then(({ data }) => data);
    },
    /**
     * 统计报表
     * @method ExportLineDeviceUseTable 导出线路设备使用情况表
     */
    ExportLineDeviceUseTable (params) {
        return request({ url: `${prefix}/exportLineDeviceUseTable`, method: 'post', params, responseType: 'blob' });
    },
    /**
     * 统计报表
     * @method ShowLandStats 查询土地统计情况表
     */
    ShowLandStats (params) {
        return request({ url: `${prefix}/ShowLandStats`, method: 'get', params }).then(({ data }) => data);
    },
    /**
     * 统计报表
     * @method ExportLandStats 导出土地统计表
     */
    ExportLandStats (params) {
        return request({ url: `${prefix}/ExportLandStats`, method: 'post', params, responseType: 'blob' });
    },
    /**
     * 统计报表
     * @method ShowOfficeAssetStats 查询办公资产情况表
     */
    ShowOfficeAssetStats (params) {
        return request({ url: `${prefix}/ShowOfficeAssetStats`, method: 'get', params }).then(({ data }) => data);
    },
    /**
     * 统计报表
     * @method ExportOfficeAssetStats 导出办公资产统计表
     */
    ExportOfficeAssetStats (params) {
        return request({ url: `${prefix}/ExportOfficeAssetStats`, method: 'post', params, responseType: 'blob' });
    },
    /**
     * 统计报表
     * @method ShowRealEstateTable 查询房产统计表
     */
    ShowRealEstateTable (params) {
        return request({ url: `${prefix}/showRealEstateTable`, method: 'get', params }).then(({ data }) => data);
    },
    /**
     * 统计报表
     * @method ExportRealEstateTable 导出房产统计表
     */
    ExportRealEstateTable (params) {
        return request({ url: `${prefix}/exportRealEstateTable`, method: 'post', params, responseType: 'blob' });
    },
    /**
     * 统计报表
     * @method ShowAdvertisingStats 查询文体活动统计情况表
     */
    ShowAdvertisingStats (params) {
        return request({ url: `${prefix}/ShowAdvertisingStats`, method: 'get', params }).then(({ data }) => data);
    },
    /**
     * 统计报表
     * @method ExportAdvertisingStats 导出文体活动统计表
     */
    ExportAdvertisingStats (params) {
        return request({ url: `${prefix}/ExportAdvertisingStats`, method: 'post', params, responseType: 'blob' });
    },
    /**
     * 统计报表
     * @method ShowCertificateHandlingTable 查询权证办理统计表
     */
    ShowCertificateHandlingTable (data) {
        return request({ url: `${prefix}/showCertificateHandlingTable`, method: 'post', data }).then(({ data }) => data);
    },
    /**
     * 统计报表
     * @method ExportCertificateHandlingTable 导出权证办理统计表
     */
    ExportCertificateHandlingTable (params) {
        return request({ url: `${prefix}/exportCertificateHandlingTable`, method: 'post', params, responseType: 'blob' });
    },
    /**
     * 统计报表
     * @method ShowLineListTable 查询线路设备使用情况表
     */
    ShowLineListTable (params) {
        return request({ url: `${prefix}/showLineList`, method: 'get', params }).then(({ data }) => data);
    },
    // /**
    //  * 统计报表
    //  * @method ExportLineDeviceUseTable 导出线路设备使用情况表
    //  */
    // ExportLineDeviceUseTable (params) {
    //     return request({ url: `${prefix}/exportLineDeviceUseTable`, method: 'post', params, responseType: 'blob' });
    // },
    /**
     * 统计报表
     * @method ShowFaultEstateTable 查询故障资产统计表
     */
    ShowFaultEstateTable (params) {
        return request({ url: `${prefix}/showFaultEstateTable`, method: 'get', params }).then(({ data }) => data);
    },
    /**
     * 统计报表
     * @method ExportFaultEstateTable 导出故障资产统计表
     */
    ExportFaultEstateTable (params) {
        return request({ url: `${prefix}/exportFaultEstateTable`, method: 'post', params, responseType: 'blob' });
    }
};
