<script setup>
import { ADVERTISEMENT_RESOURCES_DETAILS_TABLE } from '../_constants/index';
import * as Api from '@api/index';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
const total = ref(0);
const loading = reactive({
    table: false,
    export: false
});
const tableData = reactive({
    total: 0,
    list: []
});
const searchForm = reactive({
    line: '', // 所属路线
    type: '' // 广告类型
});
const pagination = reactive({
    pageSize: 10,
    pageNum: 1
});
const lineDescOptions = ref([]);
// 获取所属线路
const getLineDesc = async () => {
    try {
        const { Data } = await Api.AssetDetails.getLineDesc();
        lineDescOptions.value = Data;
    } catch (error) {
    }
};
// 获取广告明细
const getAdvertisingDetailList = async () => {
    try {
        loading.table = true;
        const response = await Api.AssetDetails.getAdvertisingDetailList({ ...searchForm, ...pagination });
        const { Total, Data } = response;
        tableData.list = Data;
        tableData.total = Total;
    } catch (error) {
        console.log(error);
    } finally {
        loading.table = false;
    }
};
// 获取广告类型
const advertisingTypeOptions = ref([]);
const getAdvertisingType = async () => {
    try {
        const { Data } = await Api.AssetDetails.getAdvertisingType();
        advertisingTypeOptions.value = Data;
    } catch (error) {
    }
};
// 导出
const ExportEvent = async () => {
    try {
        loading.export = true;
        if (tableData.total > 0) {
            const response = await Api.AssetDetails.exportAdvertisingDetailList({
                pageSize: 10000,
                pageNum: 1,
                ...searchForm
            });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (error) {
        console.log(error);
    } finally {
        loading.export = false;
    }
};
const SearchEvent = () => {
    pagination.pageNum = 1;
    pagination.pageSize = 10;
    getAdvertisingDetailList();
};
const ResetEvent = () => {
    searchForm.line = null;
    searchForm.type = null;
    pagination.pageNum = 1;
    pagination.pageSize = 10;
    getAdvertisingDetailList();
};
onMounted(() => {
    getLineDesc();
    getAdvertisingDetailList();
    getAdvertisingType();
});
</script>

<template>
    <ecp-layout-pagination content-scroll :total="tableData.total" v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize" :top-pagination="false" layout="prev, pager, next, sizes, jumper"
        @query="getAdvertisingDetailList()">
        <!-- <template #head>
        </template> -->
        <template #content>
            <div style="display: flex; flex-wrap: nowrap;">
                <el-form inline v-model="searchForm" ref="formRef">
                    <el-form-item label="广告类型" prop="type">
                        <el-select v-model="searchForm.type">
                            <el-option v-for="item in advertisingTypeOptions" :key="item" :label="item" :value="item" />
                        </el-select>
                        <!-- <div v-for="item in advertisingTypeOptions" :key="item">{{ item }}</div> -->
                    </el-form-item>
                    <el-form-item label="所属线路" prop="line">
                        <el-select v-model="searchForm.line">
                            <el-option v-for="item in lineDescOptions" :key="item.LineCode" :label="item.LineName"
                                :value="item.LineCode" />
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item> -->

                    <!-- </el-form-item> -->
                </el-form>
                <div class="button-group"><ecp-button type="primary" text="查询" @click="SearchEvent"
                        :loading="loading.table" />
                    <ecp-button text="重置" @click="ResetEvent" :loading="loading.table" />
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading.export" @click="ExportEvent" />
                </div>
            </div>

            <app-dynamic-table :loading="loading.table" :table-data="tableData.list"
                :table-config="ADVERTISEMENT_RESOURCES_DETAILS_TABLE" style="height: calc(100vh - 310px);" />
        </template>
    </ecp-layout-pagination>
</template>

<style scoped lang="scss">
/* 添加样式以使按钮组居右 */
.button-group {
    display: flex;
    flex-grow: 1;
    justify-content: flex-end;
}
</style>
