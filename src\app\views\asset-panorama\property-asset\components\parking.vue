<template>
    <div class="parking">
        <app-chart-treemap v-if="parkingData?.length" :options="parking.options" text="车位" unit="个" />
        <ecp-empty v-else />
    </div>
</template>

<script setup>
import { ref, reactive, defineProps, watch } from 'vue';
import { Propertyasset } from '@api/asset-panorama/property-asset';

const props = defineProps({
    parkingData: Array
});

const parking = reactive({
    options: {
        series: [
            {
                type: 'treemap',
                width: '93%',
                height: '93%',
                itemStyle: {
                    borderWidth: 1,
                    borderColor: 'transparent'
                },
                breadcrumb: {
                    show: false
                },
                data: props.parkingData
            }
        ]
    }
});

watch(() => props.parkingData, (newData) => {
    if (newData) {
        parking.options.series[0].data = newData;
    }
}, { immediate: true });
</script>

<style lang="scss" scoped>
.parking {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 12px;
}

.demo-container {
    width: 100%;
    height: 100%;
}

.chart {
    width: 100%;
    height: 100%;
}
</style>
