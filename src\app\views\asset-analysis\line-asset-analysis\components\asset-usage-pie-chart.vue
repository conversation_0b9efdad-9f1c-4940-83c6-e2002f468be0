<template>
    <app-business-panel class="asset-usage-pie-chart" title="资产使用情况" mode="card" v-loading="loading">
        <template #default>
            <app-chart-pie :graphData="graphData" chartType="ring" />
        </template>
    </app-business-panel>
</template>

<script setup>
import { LineAssetAnalysis } from '@api/asset-analysis/line-asset-analysis';
import { CHART_COLOR_LIST } from '@constants/enum-config';

const props = defineProps({
    selectedLine: String, // 选择的线路
    isInitialLoad: Boolean
});

const loading = ref(false);

const graphData = ref([]);

const getChartData = async () => {
    console.log('%c getChartData', 'font-size:18px;color:gold;font-weight:700;');
    if (props.isInitialLoad) {
        return;
    }
    loading.value = true;
    graphData.value = [];

    try {
        const params = {};
        if (props.selectedLine.line !== 'all') {
            params.udLine = props.selectedLine.line;
        }
        const res = await LineAssetAnalysis.getpowerRate(params);
        const list = (res?.Data || []).map((item, index) => ({
            name: item.StatusType,
            value: item.Rate ? Math.round(item.Rate * 100 * 1000) / 1000 : 0, // 保留三位小数
            color: CHART_COLOR_LIST[index]
        }));

        graphData.value = list;
    } catch (error) {
        console.log('%c getTransferStatDataTrend Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    }

    loading.value = false;
};

onActivated(() => {
    getChartData();
});

watch([() => props.selectedLine.line], ([newLine]) => {
    getChartData();
});

</script>

<style scoped lang="scss">
.asset-usage-pie-chart {}
</style>
