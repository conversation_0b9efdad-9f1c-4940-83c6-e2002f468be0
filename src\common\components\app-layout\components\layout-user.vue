<template>
    <div class="layout-user-wrapper" :class="[type]">
        <el-popover popper-class="layout-user-popover" width="auto" :disabled="type === 'tabs'" :offset="4">
            <template #reference>
                <div class="layout-user">
                    <img class="layout-user-icon" src="/layout/user.png" />
                    <div class="layout-user-text">
                        <div class="layout-user-text-name">{{ userName }}</div>
                        <div class="layout-user-text-code">{{ userCode }}</div>
                    </div>
                </div>
            </template>
            <template #default>
                <div class="layout-user-content">
                    <el-link class="layout-user-content-item layout-user-btn danger" :underline="false"
                        @click="onLogout" v-if="type !== 'tabs'"><ecp-icon class="layout-user-btn-icon"
                            icon="ecp-icon-power"></ecp-icon>退出登录</el-link>
                </div>
            </template>
        </el-popover>

        <template v-if="type === 'tabs'">
            <el-link class="layout-user-btn danger" :underline="false" @click.stop="onLogout"><ecp-icon
                    class="layout-user-btn-icon" icon="ecp-icon-power"></ecp-icon></el-link>
        </template>
    </div>
</template>

<script setup>
import useUserStore from '@store/user';

import { LoginApi } from '@api/login';
import { ElMessageBox } from 'element-plus';

defineComponent({
    name: 'layout-user'
});

const props = defineProps({
    type: {
        type: String,
        default: 'header'
    }
});

const userStore = useUserStore();

const userName = computed(() => userStore?.userInfo?.UserName || '--');
const userCode = computed(() => userStore?.userInfo?.UserCode || '--');

const onLogout = async () => {
    try {
        await ElMessageBox.confirm('确定退出登录？', {
            title: '温馨提示',
            type: 'warning'
        });

        await LoginApi.logout();
    } catch (error) {
        console.log('%c onLogout Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    }
};

</script>

<style lang="scss" scoped>
$layout-user: layout-user;

.#{$layout-user} {
    cursor: pointer;

    &,
    &-wrapper {
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        gap: var(--spacer);
    }

    &-wrapper {
        padding: 0 var(--spacer-large) 0 var(--spacer-large-3);
        align-items: stretch;
    }

    &-icon {
        width: var(--font-size-larger-1);
        height: var(--font-size-larger-1);
        flex: 0 0 auto;
        pointer-events: none;
    }

    &-text {
        width: auto;
        flex: 0 0 auto;
        font-size: var(--font-size-small);
        line-height: var(--font-line-height-extra-small);
        color: var(--color-text-light-darken);

        &-code {
            color: var(--color-white-opacity-6-5);
        }
    }

    &-content {
        width: 100%;
        // min-width: 220px;
        // max-width: 25vw;
        display: flex;
        flex-direction: column;

        &-item {
            padding: var(--spacer) var(--spacer-medium);
            align-items: flex-start;
            justify-content: flex-start;
            color: var(--color-text-regular);
            line-height: var(--font-line-height-large);

            &.border-top {
                border-top: var(--border-base);
            }
        }
    }

    &-btn {
        align-items: flex-start;
        justify-content: flex-start;
        color: var(--color-text-regular);
        line-height: var(--font-line-height-large);
        border-radius: 0;
        text-decoration: none;

        &-icon {
            margin-right: var(--spacer-small);
            line-height: 1em;
        }

        &.danger {
            --elp-link-hover-text-color: var(--color-danger);
            align-self: stretch;
            align-items: center;
        }
    }

    &+.#{$layout-user}-btn {
        padding: 0 var(--spacer-large-3);

        .#{$layout-user}-btn-icon {
            margin-right: 0;
        }
    }

    &.header {}

    &-wrapper.tabs {
        padding-right: 0;

        .#{$layout-user} {
            cursor: default;
        }

        .#{$layout-user}-text {
            color: var(--color-text-regular);
        }
    }
}
</style>

<style lang="scss">
.elp-popper.layout-user-popover {
    --elp-popover-padding: 0;
}
</style>
