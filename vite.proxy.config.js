const API_URL = '172.25.20.2:9081'; // 旧公服
// const API_URL = 'dev.dev.pcitech.com'; // 公服3.0-k8s

const defaultProxy = {
    '^/(api|sso|sysmanager)': {
        target: `http://${API_URL}`,
        changeOrigin: true
    },

    // 公服前端代理
    '^/(common-frontend|s-common-fronten)': {
        target: `http://${API_URL}`,
        changeOrigin: true
    }
};

const getProxy = (useUniMode, usePortal) => ({
    ...defaultProxy,
    ...(useUniMode && usePortal
        ? {

            '^/asset-manage-web': {
                target: 'http://localhost:30768',
                changeOrigin: true,
                rewrite: (string) => string.replace('/asset-manage-web', '')
            }
        }
        : {})
});

export default getProxy;
