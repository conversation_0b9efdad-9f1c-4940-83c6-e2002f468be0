<template>
    <div class="doughnut-chart-container">
        <div class="chart-legend">
            <div v-for="(item, index) in chartData" :key="index" class="chart-legend-item">
                <i class="icon" :style="{ backgroundColor: item.color }"></i>
                <div class="chart-legend-item-label">
                    <span class="chart-legend-item-text">{{ item.name }}</span>
                    <span class="chart-legend-item-rate font-number-bold">{{ item.value }}%
                    </span>
                </div>
            </div>
        </div>
        <div ref="chart" class="chart-wrapper"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
    usageData: {
        type: Array,
        required: true
    }
});

const chart = ref(null);
let chartInstance = null;

const chartData = ref([]);

const renderChart = () => {
    if (!chart.value) return;

    if (!chartInstance) {
        chartInstance = echarts.init(chart.value);
    }

    const labels = props.usageData.map((item) => item.category);
    const data = props.usageData.map((item) => item.usage);
    const seriesData = labels.map((label, index) => ({
        name: label,
        value: data[index]
    }));

    chartData.value = seriesData.map((item, index) => ({
        name: item.name,
        value: item.value,
        color: ['#0367fc', '#ffd022', '#11b062', '#487690'][index % 4]
    }));

    const option = {
        tooltip: {
            trigger: 'item',
            textStyle: {
                fontSize: 10
            },
            extraCssText:
                    'padding: 0; border-radius: 0.4em; border: 0; overflow: hidden;',
            backgroundColor: '#fff',
            formatter: (params) => {
                const color = params.color;
                return `
        <div class="ecp-chart-tooltip-wrapper is-white">
            <div class="ecp-chart-tooltip-head">${params.name}</div>
            <div class="ecp-chart-tooltip-item">
                <span class="ecp-chart-tooltip-label" style="--color: ${color}">百分比</span>
                <span class="ecp-chart-tooltip-value">
                    <i class="ecp-chart-tooltip-value-num">${params.value}</i>
                    <i class="ecp-chart-tooltip-value-unit">%</i>
                </span>
            </div>
        </div>
        `;
            }
        },
        series: [
            {
                type: 'pie',
                radius: ['50%', '70%'],
                data: seriesData,
                label: {
                    show: false
                },
                itemStyle: {
                    normal: {
                        color: (params) => chartData.value[params.dataIndex].color,
                        borderWidth: 3,
                        borderColor: '#fff'
                    }
                }
            }
        ]
    };

    chartInstance.setOption(option);
    window.addEventListener('resize', () => {
        chartInstance.resize();
    });
};

let resizeObserver = null;

onMounted(() => {
    nextTick(() => {
        renderChart();
    });

    resizeObserver = new ResizeObserver(() => {
        if (chartInstance) {
            chartInstance.resize();
        }
    });
    resizeObserver.observe(chart.value);
});

watch(
    () => props.usageData,
    (newData) => {
        if (newData && newData.length > 0) {
            renderChart();
        }
    },
    { immediate: true, deep: true }
);

onBeforeUnmount(() => {
    if (chartInstance) {
        chartInstance.dispose();
    }
    if (resizeObserver) {
        resizeObserver.disconnect();
    }
});
</script>

<style scoped lang="scss">
.doughnut-chart-container {
    padding-top: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;

    .chart-legend {
        display: flex;
        margin-top: var(--spacer-large-1);
        justify-content: space-around;
        gap: 6px;

        .chart-legend-item {
            display: flex;
            flex-direction: row;

            &-label {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
            }

            &-text {
                font-size: 12px;
                color: var(--color-text-secondary);
                margin-bottom: var(--spacer-small);
                white-space: nowrap;
            }

            &-rate {
                font-size: var(--font-size-larger-1);
                color: var(--color-text-primary);
                white-space: nowrap;
            }

            .icon {
                display: inline-block;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                margin-right: var(--spacer);
                margin-top: 5px;
            }
        }
    }

    .chart-wrapper {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

@media (max-width: 1380px) {
    .doughnut-chart-container {
        .chart-legend-item {
            &-text {
                font-size: 11px;
            }

            &-rate {
                font-size: 10px;
            }

            .icon {
                width: 10px;
                height: 10px;
            }
        }
    }
}

@media (max-width: 768px) {
    .doughnut-chart-container {
        .chart-legend-item {
            &-text {
                font-size: 8px;
            }

            &-rate {
                font-size: 10px;
            }

            .icon {
                width: 6px;
                height: 6px;
            }
        }
    }
}

@media (max-width: 480px) {
    .doughnut-chart-container {
        .chart-legend-item {
            &-text {
                font-size: 9px;
            }

            &-rate {
                font-size: 10px;
            }

            .icon {
                width: 6px;
                height: 6px;
            }
        }
    }
}
</style>
