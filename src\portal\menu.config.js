export const Menu = [
    {
        Text: '公共服务',
        Target: '/common-frontend/',
        ChildNodes: [
            {
                Text: '用户管理',
                Target: '/common-frontend/#/SystemConfig/UserManage'
            },
            {
                Text: '部门管理',
                Target: '/common-frontend/#/SystemConfig/ApartmentManage'
            },
            {
                Text: '角色管理',
                Target: '/common-frontend/#/SystemConfig/RoleManage'
            },
            {
                Text: '菜单管理',
                Target: '/common-frontend/#/SystemConfig/MenuManage'
            },
            {
                Text: '用户组管理',
                Target: '/common-frontend/#/SystemConfig/UserGroupManage'
            }
        ]
    },
    {
        Text: '资源管理',
        Target: '/omof-frontend/',
        ChildNodes: [
            {
                Text: '设备接入管理',
                Target: '/omof-frontend#/device-management'
            },
            {
                Text: '档案定义',
                Target: '/omof-frontend#/metadata-manage'
            },
            {
                Text: '平台管理',
                Target: '/omof-frontend#/platform-manage'
            },
            {
                Text: '档案信息共享',
                Target: '/omof-frontend#/file-info-share'
            }
        ]
    },
    {
        Text: 'Menu1',
        Target: '/asset-manage-web#/Menu1'
    },
    {
        Text: 'Menu2',
        Target: '/asset-manage-web#/Menu2'
    },
    {
        Text: 'Menu3',
        Target: '/asset-manage-web#/Menu3'
    },
    {
        Text: '1展示长菜单情况',
        Target: '/asset-manage-web#/Menu4'
    },
    {
        Text: '2展示长菜单情况',
        Target: '/asset-manage-web#/Menu5'
    },
    {
        Text: '3展示长菜单情况',
        Target: '/asset-manage-web#/Menu6',
        ChildNodes: [
            {
                Text: '子菜单3-1',
                Target: '/asset-manage-web#/Menu61'
            },
            {
                Text: '子菜单3-2',
                Target: '/asset-manage-web#/Menu62'
            }
        ]
    },
    {
        Text: '4展示长菜单情况',
        Target: '/asset-manage-web#/Menu7',
        ChildNodes: [
            {
                Text: '子菜单4-1',
                Target: '/asset-manage-web#/Menu',
                ChildNodes: [
                    {
                        Text: '子菜单4-1-1',
                        Target: '/asset-manage-web#/Menu71'
                    }
                ]
            },
            {
                Text: '子菜单4-2',
                Target: '/asset-manage-web#/Menu72'
            }
        ]
    }
];
