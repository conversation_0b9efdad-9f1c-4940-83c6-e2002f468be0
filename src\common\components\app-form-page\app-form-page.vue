<!--
<docs>
# app-form-page
- 主要提供带返回面板及底部按钮的统一面板

## slot
- head-right 顶部导航右边内容
- head-append 顶部额外内容
- content 主体内容
- footer 底部内容

## props
- visible 控制面板显隐 可用**v-model**
- title 面板app-nav-crumb需要展示的标题
- showNav 是否展示面包屑导航
- showFooter 是否展示底部栏
- isRoute 是否是路由跳转
- isRelatParent 是否相对于父元素定位

## events
- buttonClick 监听底部按钮点击事件，回调参数{action};(**action**: **confirm** 确定操作|**cancel** 取消操作)
</docs>
 -->

<template>
    <transition name="fade-transform" mode="out-in">
        <div class="app-form-page"
            :class="{ 'with-header-append': $slots['header-append'] || $slots['head-append'], 'with-right': !splitRight && $slots.right, 'show-nav': showNav, 'page-scroller': pageScroller, 'no-padding': noPadding }"
            :style="{ position: position, height: height, '--slot-right-width': `${slotRightWidth}px`, '--pattern-url': patternImage ? `url(${patternImage})` : '', '--pattern-url-dark': patternImageDark ? `url(${patternImageDark})` : '' }"
            v-resize="onResize" v-if="visible">
            <ecp-panel class="app-form-page-panel" :interval="!pageScroller && !noPadding" :full-height="fullHeight"
                :class="{ 'table-layout': tableLayout, 'no-header': !showHeader || !showNav && !$slots['header-append'] && !$slots['head-append'] }"
                :style="{ '--app-form-page-interval': noPadding ? 0 : null }">
                <template #header>
                    <div class="app-panel-header">
                        <app-nav-crumb @onBack="onClosePanel" :is-route="isRoute" :title="title" class="app-nav-crumb"
                            :class="{ divide: (!!$slots['head-append']) && !patternHeader, 'no-border': noPadding || noBorder }"
                            :is-show-back-icon="!!showTopBack" v-if="showNav">
                            <template #nav-right>
                                <slot name="header-right" v-if="$slots['header-append']"></slot>
                                <slot name="head-right" v-else></slot>
                            </template>
                        </app-nav-crumb>
                        <div class="app-panel-header--head-append">
                            <slot name="header-append" v-if="$slots['header-append']"></slot>
                            <slot name="head-append" v-else></slot>
                        </div>
                    </div>
                </template>
                <template #content>
                    <component :is="pageScroller ? 'el-scrollbar' : 'div'" class="app-form-page-container"
                        v-bind="{ native: pageScroller ? false : null }" ref="appFormPageContainer">
                        <slot name="content-prepend" v-if="$slots['content-prepend']"></slot>
                        <div class="app-form-page-content" :class="{ interval: pageScroller && !noPadding }"
                            :style="{ '--app-form-page-content-height': contentHeight }">
                            <slot name="content" v-if="$slots.content"></slot>
                            <slot v-else></slot>
                        </div>
                        <slot name="content-append" v-if="$slots['content-append']"></slot>
                    </component>
                </template>
                <template #footer v-if="showFooter">
                    <div class="footer">
                        <slot name="footer" v-if="$slots.footer"></slot>
                        <ecp-button-group type="button" :ui-strict="false" v-else>
                            <template v-if="buttonList.length > 0">
                                <ecp-button v-bind="item.props" :title="item.name" :text="item.name"
                                    :key="item.tag || index" v-for="(item, index) in buttonList"
                                    v-on="item.events"></ecp-button>
                            </template>
                            <template v-else>
                                <ecp-button class="sub-actions-btn" v-ripple text="取 消" data-action="cancel"
                                    @click.stop="onButtonTap"></ecp-button>
                                <ecp-button class="sub-actions-btn" v-ripple text="保 存" data-action="confirm"
                                    @click.stop="onButtonTap" type="primary"></ecp-button>
                            </template>
                        </ecp-button-group>
                    </div>
                </template>
            </ecp-panel>
            <div class="app-form-page--right-wrapper"
                :class="{ 'without-page-header': fullHeightRight || !showHeader || !showNav }" ref="slotRight"
                v-resize="onRightResize" v-if="$slots.right">
                <slot name="right"></slot>
            </div>
        </div>
    </transition>
</template>

<script>
import { MicroUtils } from '@ecp/ecp-ui-plus';

export default {
    name: 'app-form-page',
    model: {
        prop: 'visible',
        event: 'changeVisible'
    },
    props: {
        visible: {
            type: Boolean,
            default: true
        },
        title: [String, Array],
        patternHeader: Boolean,
        patternImage: {
            type: String,
            default: ''
        },
        patternImageDark: {
            type: String,
            default: ''
        },
        tableLayout: Boolean,
        showFooter: Boolean,
        isRoute: {
            type: Boolean,
            default: true
        },
        beforeClose: Function,
        buttonList: {
            type: Array,
            default: () => []
        },
        position: {
            type: String,
            default: 'absolute'
        },
        showTopBack: Boolean,
        noPadding: Boolean,
        fullHeight: {
            type: Boolean,
            default: true
        },
        showHeader: {
            type: Boolean,
            default: true
        },
        showNav: Boolean,
        appendToEl: Boolean,
        targetEl: {
            type: String,
            default: '.el-scrollbar__view'
        },
        fullHeightRight: Boolean,
        splitRight: Boolean,
        pageScroller: {
            type: Boolean,
            default: true
        },
        noBorder: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            loading: false,
            height: 'calc(100vh - 64px)',
            contentHeight: '100%',

            slotRightWidth: 0,
            portalTabItem: null
        };
    },
    computed: {
    },
    watch: {
        tableLayout: {
            handler (newVal) {
                this.setTableLayoutHeight();
            },
            immediate: true
        },
        title: {
            handler (newVal) {
                const currTitle = this.getCurrTitle();
                if (this.portalTabItem && Array.isArray(newVal) && currTitle) {
                    this.portalTabItem.name = currTitle;
                }
            },
            immediate: true,
            deep: true
        }
    },
    mounted () {
        this.setTableLayoutHeight();
        try {
            if (this.appendToEl) {
                const pageEl = this.$el;
                const el = this.findAssignParentEl(pageEl, this.targetEl);
                if (el) {
                    el.querySelector('div:first-child').appendChild(pageEl);
                }
            }
            // setTimeout(() => {
            //     requestAnimationFrame(() => {
            // const navEl =
            //     document.querySelector && document.querySelector('.layout-nav');
            // this.height = `calc(100vh - ${navEl?.clientHeight || 0}px)`;
            //     });
            // }, 500);
            this.addPortalTab();
        } catch { }
    },
    activated () {
        this.addPortalTab();
    },
    methods: {
        getCurrTitle () {
            if (this.isRoute) {
                if (Array.isArray(this.title) && this.title.length) {
                    return this.title[this.title.length - 1]?.label;
                }
                if (typeof this.title === 'string') {
                    return this.title;
                }
            }
            return '';
        },
        getPortalTabUrl (url) {
            const currentFullPath = this.$route.fullPath;
            const baseUrl = (PACKAGE_NAME || '').replace(/\/$/, '');
            const targetUrl = `${baseUrl}/#${url || currentFullPath}`;
            const microUrl = MicroUtils.formatRoute(targetUrl, '/s-');

            return window.__POWERED_BY_QIANKUN__ || window.__POWERED_BY_WUJIE__ ? microUrl : `/${targetUrl}`;
        },
        addPortalTab () {
            const currTitle = this.getCurrTitle();

            const portalTabUrl = this.getPortalTabUrl();

            if (currTitle && window.PortalTabsActions) {
                this.portalTabItem = window.PortalTabsActions.getByUrl(portalTabUrl);
                if (!this.portalTabItem) {
                    const listenerKey = window.PortalTabsActions.on('add', (event) => {
                        this.portalTabItem = event.validTabItem;
                        window.PortalTabsActions.off('add', listenerKey);
                    });
                    window.PortalTabsActions.add({
                        name: currTitle,
                        url: portalTabUrl
                    });
                }
            }
        },
        removePortalTab () {
            if (window.PortalTabsActions && this.portalTabItem) {
                const prevTitle = this.title?.length > 1 ? this.title[this.title.length - 2] : null;
                const nextActiveItem = prevTitle.to
                    ? window.PortalTabsActions.getByUrl(prevTitle.to) || {
                        name: prevTitle.label,
                        url: this.getPortalTabUrl(prevTitle.to)
                    }
                    : null;
                window.PortalTabsActions.remove(this.portalTabItem, {
                    // block: !!nextActiveItem,
                    nextActiveItem
                });
            }
        },
        onResize (e) {
            if (this.visible) {
                requestAnimationFrame(() => {
                    this.$emit('onResize');
                    this.setTableLayoutHeight();
                });
            }
        },
        setTableLayoutHeight () {
            const pageRef = this?.$el;
            // const parentRef = this?.$el?.parentElement;
            const parentRef = document.querySelector('#app-layout-content');
            const headerRef = pageRef?.querySelector?.('.ecpp-panel-header');
            if (pageRef) {
                const height = (parentRef || pageRef).scrollHeight;
                // const height = (parentRef || pageRef).offsetHeight;
                if (this.position === 'relative') {
                    if (height) {
                        this.height = `${height}px`;
                    } else {
                        this.height = '100vh';
                    }
                } else {
                    this.height = '100%';
                }

                if (headerRef) {
                    if (this.tableLayout) {
                        const tableLayoutRef = this.$refs.appFormPageContainer;
                        const tableLayoutParentRef = tableLayoutRef?.parentElement;
                        const tableLayoutParentRefStyles = tableLayoutParentRef ? window.getComputedStyle(tableLayoutParentRef) : {};
                        let {
                            paddingTop = 0,
                            paddingBottom = 0
                        } = tableLayoutParentRefStyles;
                        paddingTop = parseFloat(paddingTop);
                        paddingBottom = parseFloat(paddingBottom);
                        const contentHeight = height - headerRef.scrollHeight - (paddingTop || 0) - (paddingBottom || 0);
                        if (contentHeight) {
                            this.contentHeight = `${contentHeight}px`;
                        }
                    } else {
                        this.contentHeight = '100%';
                    }
                }
            }
        },
        onButtonTap (e) {
            const self = this;
            const { action } = e.currentTarget.dataset;
            try {
                if (typeof self.beforeClose === 'function') {
                    self.loading = true;
                    self.beforeClose(action).then(canClose => {
                        self.loading = false;
                        if (canClose) self.onClosePanel(1);
                    });
                } else {
                    if (action === 'cancel') {
                        self.onClosePanel(1);
                    }

                    self.$emit('buttonClick', {
                        action
                    });
                }
            } catch { }
        },
        onClosePanel (tag) {
            const self = this;
            if (typeof self.beforeClose === 'function') {
                self.beforeClose();
                return;
            }
            // if (self.isRoute) {
            //     self.$router.back();
            // } else {
            self.$emit('changeVisible', false);
            self.$emit('update:visible', false);
            self.$emit('close-panel', tag);
            // }
        },
        findAssignParentEl (el, name) {
            if (!el.parentNode) return null;

            const parent = el.parentNode;
            if (
                parent.tagName === name ||
                parent.classList.contains(name.replace('.', '')) ||
                parent.id === name.replace('#', '')
            ) {
                return parent;
            } else {
                return this.findAssignParentEl(parent, name);
            }
        },
        onRightResize () {
            this.$nextTick(() => {
                requestAnimationFrame(() => {
                    this.slotRightWidth = this.$refs.slotRight.clientWidth;
                });
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.app-form-page {
    --app-form-page-interval: var(--spacer-large-3);
    --app-form-page-interval-top: 0;
    --app-form-page-interval-right: var(--app-form-page-interval);
    --app-form-page-interval-bottom: var(--app-form-page-interval);
    --app-form-page-interval-left: var(--app-form-page-interval);
}

.app-form-page {
    position: absolute;
    top: 0;
    left: 0;
    flex: 1 0 auto;
    display: flex;
    flex-direction: row;
    width: 100%;
    // min-height: calc(720px - 64px);
    min-height: 100%;
    height: calc(100vh - 64px);
    z-index: 10;
    background-color: var(--background-color-page);

    &:is(.sub-module > .app-form-page, .el-dialog .app-form-page) {
        height: 100% !important;
    }

    :deep(.ecpp-panel) {
        background-color: var(--background-color-page);
        flex: 1 1 auto;
        overflow-x: hidden;

        & > .ecpp-panel-header {
            padding: 0;
            z-index: 1;
            background: #F7F8FA;
            border-bottom: none;

            .ecpp-panel-header-left {
                width: 100%;
            }
        }

        & > .ecpp-panel-content {
            position: relative;
            z-index: 1;
            overflow-x: hidden;
            overflow-y: hidden;
            background: #F7F8FA;

            &.interval {
                // padding: var(--spacer-large);
                padding: var(--app-form-page-interval-top) var(--app-form-page-interval-right) var(--app-form-page-interval-bottom) var(--app-form-page-interval-left);
            }

            & > .app-form-page-container {
                &.elp-scrollbar {
                    height: 100%;

                    > .elp-scrollbar__wrap {
                        height: 100%;

                        > .elp-scrollbar__view {
                            min-height: 100%;
                            display: flex;
                            flex-direction: column;
                        }
                    }
                }

                .app-form-page-content {
                    &.interval {
                        // padding: var(--app-form-page-interval);
                        padding: var(--app-form-page-interval-top) var(--app-form-page-interval-right) var(--app-form-page-interval-bottom) var(--app-form-page-interval-left);
                    }
                }
            }

            & > .ecpp-panel-content-white {
                display: flex;
                flex-direction: column;
                flex: 1 0 auto;
                border-radius: var(--border-radius-base);
                overflow: hidden;

                & > .app-form-page-container {
                    display: flex;
                    flex-direction: column;
                    flex: 1 0 auto;

                    & > .app-form-page-content {
                        background-color: transparent;
                        display: flex;
                        flex-direction: column;
                        flex-grow: 1;

                        // flex: 1 0 auto;
                        &.interval {
                            padding: var(--app-form-page-interval-top) var(--app-form-page-interval-right) var(--app-form-page-interval-bottom) var(--app-form-page-interval-left);
                        }
                    }
                }

                .table-query {
                    border-bottom: 0;
                }

                // .ecpp-layout-pagination-head,
                // .ecpp-layout-pagination-content {
                //     border-bottom: none;
                //     background-color: var(--background-color);
                //     border-radius: var(--border-radius-base);
                // }

                .ecpp-layout-pagination-content {
                    .search-result-table-content {
                        height: 100%;
                        overflow: auto;
                    }
                }

                .ecpp-layout-pagination-head {
                    // margin-bottom: 0;
                    margin-bottom: var(--spacer-large);
                }
            }
        }

        &.table-layout {
            & > .ecpp-panel-content {
                display: flex;
                flex-direction: column;

                & > .ecpp-panel-content-white {
                    // height: auto !important;
                    flex: 1 0 auto;
                }
            }

            .table-layout-content {

                &,
                & > .app-business-panel-content {
                    max-height: var(--app-form-page-content-height, 100%);
                    flex-grow: 1;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                }

                &-header {
                    box-sizing: border-box;
                    flex: 0 0 auto;
                    display: flex;
                    overflow: hidden;
                    padding-bottom: var(--spacer-large);

                    &.pagination-header {
                        width: 100%;
                        justify-content: space-between;
                        // padding-bottom: 0;
                    }
                }

                &-body {
                    height: 100%;
                    flex: 1 1 auto;
                    overflow: hidden;
                    position: relative;
                }
            }
        }
    }

    :deep(.app-panel-header) {
        display: flex;
        flex-direction: column;

        .app-nav-crumb {
            padding: 0 var(--spacer-large-3);
            border-bottom: 1px solid var(--background-color-page);
            height: 56px;
            align-items: center;

            &.no-border {
                border-bottom: none;
            }
        }

        .app-nav-title {
            font-family: Source Han Sans SC;
            font-size: var(--font-size-large);
            font-weight: 500;
            line-height: var(--font-line-height-primary);
            letter-spacing: 0px;
            color: #1D2137;
        }
    }

    :deep(.app-form-page-container) {
        // padding: 24px;
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;

        .app-form-page-content {
            display: flex;
            flex-direction: column;
            width: 100%;
            min-height: 100%;
            flex-grow: 1;
        }
    }

    :deep(.divide) {
        box-shadow: 0 1px 0 0 var(--border-color);
        z-index: 1;
    }

    &.with-right {
        width: 100%;

        .app-panel-header--head-append,
        .footer {
            width: calc(100% - var(--slot-right-width));
        }

        .app-form-page--right-wrapper {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            box-shadow: 0 6px 12px 0 var(--border-color-light);
        }

        :deep(.ecpp-panel) {
            z-index: 1;

            & > .ecpp-panel-content {
                width: calc(100% - var(--slot-right-width));
            }
        }

        :deep(.app-form-page--right-wrapper) {
            z-index: 2;

            & > .el-drawer__wrapper.no-modal.no-animation {
                box-shadow: none;
            }
        }

        &.show-nav {
            & > .app-form-page--right-wrapper {
                top: 41px;

                &:is([data-theme='dark'] .app-form-page--right-wrapper) {
                    box-shadow: 0 2px 8px var(--color-black-opacity-2-5),
                        0 0 12px var(--color-black-opacity-0-9);
                }

                &.without-page-header {
                    top: 0;
                }
            }
        }
    }

    &.page-scroller {
        :deep(.ecpp-panel) {
            background-color: var(--background-color-page);

            & > .ecpp-panel-content {
                // overflow-y: auto;

                & > .ecpp-panel-content-white {
                    height: auto !important;
                    min-height: 100%;
                    background-clip: content-box;
                    display: flex;
                    flex-direction: column;
                    flex: 1 0 auto;
                }
            }
        }
    }

    &.no-padding {
        .app-form-page-content {
            background-color: transparent;
        }
    }
}
</style>
