pipeline {
    agent {
        node {
            // 前端构建使用的节点
            label 'frontend'
        }
    }

    environment {
        // git 仓库地址，注意必须带上 .git 后缀
        REPO = 'http://*************/rail-transit-bigdata/wuhan-asset-frontend.git'
    }

    // 定义构建参数
    parameters {
        // 部署节点所在命名空间
        string(name: 'NAME_SPACE', defaultValue: 'default', description: '命名空间')

        // 镜像锁定版本号的类型
        choice(
            name: 'LOCK_VERSION',
            choices: ['disable', 'enable', 'date', 'random'],
            description: '镜像锁定版本号的类型，详见 http://************:4873/-/web/detail/image-builder'
        )

        // 目标镜像的操作系统架构
        choice(
            name: 'IMAGE_ARCH',
            choices: ['amd64', 'arm64'],
            description: '镜像架构'
        )
    }

    stages {
        stage('获取基础配置') {
            steps {
                script {
                    // 注意这里的 deployment/base.groovy、deployment/Dockerfile 所在的 deployment 目录，是位于项目工程根目录下的 deployment 目录，请根据项目具体情况修改
                    withCredentials([usernamePassword(credentialsId: 'ftp', passwordVariable: 'PASSWORD', usernameVariable: 'USERNAME')]) {
                        sh """
                        curl -o deployment/base.groovy ftp://************/%C7%B0%B6%CB%B9%B2%CF%ED/%C7%B0%B6%CB%B2%BF%CA%F0%D7%CA%D4%B4/centos/Jenkins/base.groovy -u $USERNAME:$PASSWORD

                        curl -o deployment/Dockerfile ftp://************/%C7%B0%B6%CB%B9%B2%CF%ED/%C7%B0%B6%CB%B2%BF%CA%F0%D7%CA%D4%B4/centos/Jenkins/Dockerfile -u $USERNAME:$PASSWORD
                        """
                        base = load 'deployment/base.groovy'
                    }

                    base.basicStage()
                }
            }
        }
    
        stage('拉取代码') {
            steps {
                script {
                    base.gitStage()
                }
            }
        }


        stage('编译打包') {
            steps {
                script {
                    // 这里的 '' 代表项目工程根目录，请根据项目具体情况修改
                    dir('') {
                        base.buildStage()
                    }
                }
            }
        }

        stage('文件压缩') {
            steps {
                script {
                    // 这里的 '' 代表项目工程根目录，请根据项目具体情况修改
                    dir('') {
                        base.tarStage()
                    }
                }
            }

        }

        stage('镜像推送') {
            steps {
                script {
                    // 这里的 '' 代表项目工程根目录，请根据项目具体情况修改
                    dir('') {
                        base.dockerStage()
                    }
                }
            }
        }

        stage('部署') {
            agent {
                node {
                    label 'base'
                }
            }
            steps {
                container('base') {
                    script {
                        // 这里的 '' 代表项目工程根目录，请根据项目具体情况修改
                        dir('') {
                            base.deployStage()
                        }
                    }
                }
            }
        }
    }
}
