<template>
    <el-dialog v-model="dialogVisible" v-bind="dialogProps" @close="onClose()" @open="onOpen"
        custom-class="app-dialog actual-height">
        <ecp-layout-pagination :total="pageConfig.total" v-model:current-page="pageConfig.pageNum"
            v-model:page-size="pageConfig.pageSize" @query="getRecordList" content-scroll :loading="loading">
            <template #head>
                <el-select v-model="uploadType" placeholder="数据表类型" filterable clearable style="width:200px"
                    class="mr-2" @change="getRecordList">
                    <el-option v-for="item in uploadTypeList" :key="item.Id" :label="item.UploadTypeName"
                        :value="item.Id"></el-option>
                </el-select>
                <el-date-picker v-model="selectedDate" type="month" value-format="YYYY-MM" placeholder="所属月份"
                    @change="handleDateChange" :disabled-date="disabledFutureDates" style="width:150px" />
            </template>
            <template #content>
                <app-dynamic-table :tableData="tableData" :tableConfig="tableConfig">
                    <template #upload-status="{ row, column }">
                        <el-tag class="dot-only" effect="status"
                            :type="getUploadStatusConfig(row[column.property]).type"
                            v-if="!isEmptyValue(row[column.property])">{{
                            getUploadStatusConfig(row[column.property]).label
                            }}</el-tag>
                    </template>
                    <template #file-name="{ row, column }">
                        <ecp-button type="text" :loading="row.loading"></ecp-button>
                        <span :class="['empty-cell',row.StoreFileName ? 'link':'' ]" @click="downloadFile(row)">{{
                            row[column.property] }}</span>
                    </template>
                </app-dynamic-table>
            </template>
        </ecp-layout-pagination>
    </el-dialog>
</template>
<script setup>
import { DataAcquisition } from '@api/data-acquisition';
import dayjs from 'dayjs';
import { TABLE_CONFIG, UPLOAD_STATUS } from '../constants.js';
import { isEmptyValue } from '@utils/helper';
import { downloadBlobData } from '@utils/download';
defineOptions({
    name: 'history-record-dialog'
});

const props = defineProps({
    visible: Boolean,
    uploadTypeList: Array
});

const emits = defineEmits(['update:visible']);

const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => {
        emits('update:visible', val);
    }
});

const dialogProps = computed(() => ({
    title: '历史上传记录',
    width: 800,
    appendToBody: true,
    closeOnClickModal: false,
    closeOnPressEscape: false
}));

const loading = ref(false);

const uploadType = ref('');
const selectedDate = ref('');

// 禁用未来日期
const disabledFutureDates = (date) => {
    return date.getTime() > new Date().getTime();
};

const pageConfig = reactive({
    pageNum: 1,
    pageSize: 20,
    total: 0
});
const tableConfig = TABLE_CONFIG;
const tableData = ref([]);

// 日期选择
const handleDateChange = (date) => {
    if (date) {
        selectedDate.value = dayjs(date).format('YYYY-MM');
    } else {
        selectedDate.value = '';
    }
    getRecordList();
};

// 获取上传记录列表
const getRecordList = async () => {
    try {
        loading.value = true;
        const res = await DataAcquisition.getUploadRecordList({
            uploadMonth: selectedDate.value,
            uploadTypeId: uploadType.value,
            pageNum: pageConfig.pageNum,
            pageSize: pageConfig.pageSize
        });
        tableData.value = res.Data;
        pageConfig.total = res.Total;
    } catch (error) {
        console.error(error);
    } finally {
        loading.value = false;
    }
};
const getUploadStatusConfig = (UploadStatus) => {
    return UPLOAD_STATUS.find(item => item.value === +UploadStatus) || {};
};

const downloadFile = async (row) => {
    if (!row || !row.StoreFileName) {
        return;
    }
    const index = tableData.value.findIndex(item => item.Id === row.Id);
    try {
        tableData.value[index].loading = true;
        const res = await DataAcquisition.downloadErrorDetail({ fileName: row.StoreFileName });
        downloadBlobData(res);
    } catch (error) {
        console.log('%c exportEvent error', 'color: red', error);
    } finally {
        tableData.value[index].loading = false;
    }
};

// 打开弹窗
const onOpen = () => {
    getRecordList();
};

// 关闭弹窗
const onClose = () => {
    tableData.value = [];
    pageConfig.pageNum = 1;
    pageConfig.total = 0;
    pageConfig.pageSize = 20;
    uploadType.value = '';
    selectedDate.value = '';
};

</script>

<style lang="scss" scoped>
.history-record-dialog {
    .filter-container {
        display: flex;

        .elp-select {
            width: 200px;
            margin-right: var(--spacer);
        }
    }
}
</style>
