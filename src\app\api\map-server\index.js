import { CommonReource } from '../prefix.config';

const AxiosService = axios.create();
AxiosService.defaults.withCredentials = true; // 让ajax携带cookie

export const MapServerApi = {
    /**
     * 图层
     * @method getLayerMetroLine 获取地铁线路图层
     * @param { Any } params
     * @returns { GeoJSON }
     */
    getLayerMetroLine (params) {
        return AxiosService.get(`${CommonReource}/map/layers/layer-metro-line.json`, { params }).then(res => res.data);
    },
    /**
     * 图层
     * @method getLayerMetroStation 获取地铁站点图层
     * @param { Any } params
     * @returns { GeoJSON }
     */
    getLayerMetroStation (params) {
        return AxiosService.get(`${CommonReource}/map/layers/layer-metro-station.json`, { params }).then(res => res.data);
    },

    /**
     * 图层
     * @method getLayerMetroLineSimple 获取地铁线路简化图层
     * @param { Any } params
     * @returns { GeoJSON }
     */

    getLayerMetroLineSimple (params) {
        return AxiosService.get(`${CommonReource}/map/layers/layer-metro-line-simple.json`, { params }).then(res => res.data);
    },
    /**
     * 图层
     * @method getLayerMetroStationSimple 获取地铁站点简化图层
     * @param { Any } params
     * @returns { GeoJSON }
     */
    getLayerMetroStationSimple (params) {
        return AxiosService.get(`${CommonReource}/map/layers/layer-metro-station-simple.json`, { params }).then(res => res.data);
    }
};
