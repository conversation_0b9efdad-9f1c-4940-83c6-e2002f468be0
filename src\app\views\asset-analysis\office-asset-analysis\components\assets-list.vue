<template>
    <div class="details-container" v-if="currentPage">
        <ecp-layout-pagination content-scroll class="app-panel-content-wrapper" :total="total"
            v-model:current-page="localCurrentPage" v-model:page-size="localPageSize"
            layout="prev, pager, next, sizes, jumper" @current-change="handlePageChange"
            @size-change="handlePageSizeChange">

            <template #head>
                <div class="box-container">
                    <span class="title">资产列表</span>
                    <ecp-button class="export-button" @click="exportOffAssetInfo" v-if="list.length !== 0"
                        icon="ecp-icon-export" text="导出" />
                </div>
            </template>

            <template #content>
                <template v-if="list.length === 0">
                    <ecp-empty description="暂无数据" class="empty-centered"></ecp-empty>
                </template>
                <template v-else>
                    <app-dynamic-table :tableData="list" :tableConfig="tableConfig" :pageConfig="pageConfig"
                        :emptySize="'large'" style="height: 100%;">

                        <template v-for="(column, index) in columns" :key="index">
                            <dynamic-table-column :prop="column.prop" :label="column.label"
                                :min-width="column.minWidth || '100px'" :align="column.align || 'center'" />
                        </template>
                    </app-dynamic-table>
                </template>
            </template>
        </ecp-layout-pagination>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    tableData: {
        type: Object,
        required: true
    },
    pagination: {
        type: Object,
        default: () => ({
            pageNo: 1,
            pageSize: 10
        })
    }
});

const emit = defineEmits(['update-pagination', 'export-offAssetInfo']);

const currentPage = computed(() => props.pagination.pageNo);
const pageSize = computed(() => props.pagination.pageSize);
const total = computed(() => props.tableData.total);
const list = computed(() => props.tableData.list || []);

const localCurrentPage = ref(props.pagination.pageNo);
const localPageSize = ref(props.pagination.pageSize);

const tableConfig = ref({
    cols: [
        // { prop: 'AssetNum', label: '资产编码' },
        { prop: 'CreateTime', label: '盘点时间' },
        { prop: 'AssetCategory', label: '资产大类' },
        { prop: 'AssetSubcategory', label: '资产中类' },
        { prop: 'AssetSubtype', label: '资产小类' },
        { prop: 'AssetLocation', label: '资产位置' },
        { prop: 'TokenDept', label: '使用部门' },
        { prop: 'TotalAmount', label: '总数量' },
        { prop: 'UsingAmount', label: '在用数量' },
        { prop: 'FreeAmount', label: '闲置数量' },
        { prop: 'UselessAmount', label: '报废数量' }
    ]
});
const pageConfig = ref({});

const columns = computed(() => tableConfig.value.cols);

watch(() => props.pagination.pageNo, (newPageNo) => {
    if (newPageNo !== localCurrentPage.value) {
        localCurrentPage.value = newPageNo;
    }
});
watch(() => props.pagination.pageSize, (newPageSize) => {
    if (newPageSize !== localPageSize.value) {
        localPageSize.value = newPageSize;
    }
});

watch(localCurrentPage, (newPageNo) => {
    emit('update-pagination', newPageNo, localPageSize.value);
});

watch(localPageSize, (newPageSize) => {
    emit('update-pagination', localCurrentPage.value, newPageSize);
});

const exportOffAssetInfo = () => {
    emit('export-offAssetInfo');
};
</script>

<style scoped lang="scss">
.details-container {
    width: 100%;
    height: 100%;
    max-height: 580px;
    // min-height: 300px;
    display: flex;
    flex-direction: column;
    position: relative;

    .empty-centered {
        height: 100px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .box-container {
        display: flex;
        justify-content: space-between;
        gap: 8px;
        // margin-bottom: 10px;
        align-items: center;
        width: 100%;
    }

    .title {
        position: absolute;
        font-size: 16px;
        color: var(--color-text-primary);
        top: 0;
        z-index: 10;
        /* 确保标题层级在表格之上 */
    }

    .export-button {
        margin-left: auto;
        display: flex;
        align-items: center;
        padding: 5px 10px;

        i {
            margin-right: 8px;
        }
    }

    :deep(.app-panel-content-wrapper) {
        display: flex;
        flex-direction: column;
        overflow: auto;
    }

    :deep(.ecp-layout-pagination) {
        display: flex;
        flex-direction: column;
        margin-top: 16px;
    }

    :deep(.el-table) {
        margin-top: 16px;
    }

    :deep(.app-panel-content-wrapper) {
        display: flex;
        flex-direction: column;

        .ecpp-layout-pagination-content {
            display: flex;
            flex-direction: column;
            flex: 1 1 auto;
            overflow: hidden;

            .content-main {
                flex: 1 1 auto;
            }
        }
    }

    // :deep(.ecpp-pagination.ecpp-layout-pagination-content-pagination) {
    //     flex-shrink: 0;
    // }

}
</style>
