<!-- <docs>
# 页面内上传文件组件

## props
- initFileList：初始化文件列表，用于回显，数组的对象字段需要与上传附件返回的格式一致
- showUploadBtn：是否显示上传附件按钮
- showDownloadAllFile：是否显示下载全部附件按钮

## events
- change 上传附件/附件列表发生改变时的回调，参数1 file：当前上传的文件对象，参数2 fileList：已经上传的文件列表
</docs> -->

<template>
    <component :is="tiny ? 'el-popover' : 'div'" class="app-file-upload" :class="{ 'brief-layout': briefLayout }"
        v-bind="tiny ? { popperClass: `app-file-upload tiny ${briefLayout ? 'brief-layout' : ''}`, trigger: 'hover', placement: 'bottom-start' } : {}">
        <template #reference v-if="tiny">
            <div class="tiny-reference">
                <ecp-button class="tiny-reference-btn" type="text" icon="ecp-icon-annex" text="查看" />
            </div>
        </template>
        <template v-bind="tiny ? { slot: 'default' } : {}">
            <el-upload :show-file-list="false" v-bind="{ limit, ...uploadConfig }" :action="action"
                :on-exceed="onExceed" :before-upload="handleBeforeUpload" :on-progress="onProgress"
                :on-success="handleSuccess" :on-error="handleError" :file-list="elFileList"
                :disabled="disabledUpload || fileList.length > limit" v-if="showUploadBtn && !tiny">
                <ecp-button plain text="上传附件" :loading="uploadLoading" icon="#ecp-icon-upload"
                    :disabled="disabledUpload || fileList.length >= limit" />
                <template #tip>
                    <div class="elp-upload__tip">
                        <slot name="tip">
                            <span>{{ tipMessage }}</span>
                        </slot>
                    </div>
                </template>
            </el-upload>
            <div class="download-multi-attachment" v-if="fileList.length > 0 && showDownloadAllFile && !briefLayout">
                <span class="download-multi-attachment-label" v-if="showLabel">附件：</span>
                <ecp-button type="text" text="下载全部附件" :loading="downloadMultiLoading"
                    @click="downloadDocument('multi')" />
            </div>
            <div class="file-list">
                <template v-for="(item, index) in fileList" :key="`${item.PkSysFileId}_${index}`">
                    <div class="file-list-item">
                        <i class="file-list-item-icon ecp-icon-annex"></i>
                        <app-cell-tooltip class="file-list-item-name" :show-aid="false" placement="top">
                            <span class="file-list-item-name-text" :class="{ 'brief-layout': briefLayout }"
                                @click="!tiny && briefLayout ? autoPreview(item) : () => { }">{{ item.FileName }}</span>
                        </app-cell-tooltip>
                        <span class="file-list-item-size" v-if="!hideFileSize">({{ computedFileSize(item.FileSize)
                            }})</span>
                        <ecp-button-group class="file-list-item-button-group" type="text" :ui-strict="false" :limit="4"
                            v-if="!briefLayout">
                            <ecp-button class="file-list-item-btn" type="text" text="删除" @click="deleteDocument(index)"
                                v-if="showUploadBtn" />
                            <template v-if="!tiny">
                                <ecp-button class="file-list-item-btn" type="text" text="预览"
                                    :disabled="!getPreviewAvailable(item.StoragePath)" @click="previewDocument(item)" />
                                <ecp-button class="file-list-item-btn" type="text" text="下载"
                                    :loading="downloadSingleLoading && whichFile.PkSysFileId === item.PkSysFileId"
                                    @click="downloadDocument('single', item)" />
                            </template>
                            <template v-else>
                                <ecp-button class="file-list-item-btn" type="text" text="查看"
                                    @click="autoPreview(item)" />
                            </template>
                        </ecp-button-group>
                    </div>
                </template>
                <el-image class="preview-image" lazy :preview-src-list="imgList" ref="imagePreviewer" v-show="false" />
            </div>
        </template>
    </component>
</template>

<script>
import { cloneDeep } from 'lodash-es';
export default {
    name: 'app-file-upload',
    props: {
        initFileList: {
            type: Array,
            default: () => []
        },
        showUploadBtn: {
            type: Boolean,
            default: true
        },
        showLabel: {
            type: Boolean,
            default: true
        },
        showDownloadAllFile: {
            type: Boolean,
            default: true
        },
        uploadConfig: {
            type: Object,
            default: () => ({})
        },
        limit: Number,
        briefLayout: Boolean,
        tiny: Boolean,
        hideFileSize: Boolean,
        tipMessage: {
            type: String,
            default: '支持扩展名：.rar .zip .doc .docx .pdf .jpg...'
        },
        beforeDelete: Function
    },
    data () {
        return {
            uploadLoading: false,
            downloadSingleLoading: false,
            downloadMultiLoading: false,
            action: '',
            fileList: [],
            whichFile: {},

            elFileList: [],

            imgList: []
        };
    },
    watch: {
        initFileList: {
            handler (newVal) {
                this.fileList = newVal.filter(item => item);
            },
            deep: true,
            immediate: true
        },
        fileList: {
            handler (fileList) {
                this.elFileList = (fileList || []).map(
                    ({ FileName: name, StoragePath: url }) => ({
                        name,
                        url
                    })
                );
            },
            deep: true,
            immediate: true
        }
    },
    computed: {
        disabledUpload ({ limit, fileList }) {
            return (
                typeof limit === 'number' &&
                limit > 0 &&
                fileList.length >= limit
            );
        }
    },
    mounted () {
        this.action = this.$api.AttachmentService.attachmentUpload();
    },
    methods: {
        getPreviewAvailable (suffix) {
            return (
                this.$utils.getFileType(suffix, /image|audio|video/) !==
                'file' ||
                (suffix && suffix.match(/pdf$/i))
            );
        },
        isImage (suffix) {
            return this.$utils.getFileType(suffix, /image/) === 'image';
        },
        onExceed (files, fileList) {
            console.log(
                '%c onExceed',
                'font-size:18px;color:red;font-weight:700;',
                files,
                fileList
            );
        },
        // 上传文件前
        handleBeforeUpload (...args) {
            const { beforeUpload } = this.uploadConfig || {};
            if (typeof beforeUpload === 'function') {
                return beforeUpload(...args);
            }
        },
        onProgress () {
            if (!this.uploadLoading) {
                this.uploadLoading = true;
            }
        },
        // 上传成功后
        handleSuccess (res) {
            this.uploadLoading = false;
            if (res.OpCode === 0 || res.OpCode === '0') {
                const data = res.Data || {};
                this.fileList.push(data);
                this.$emit('change', data, this.fileList);
            } else {
                this.$message.error(res.OpDesc);
                this.$set(this, 'fileList', cloneDeep(this.fileList));
            }
        },
        handleError (error) {
            console.log(
                '%c handleError',
                'font-size:18px;color:red;font-weight:700;',
                error
            );
            this.uploadLoading = false;
        },
        // 计算附件大小
        computedFileSize (size) {
            if (Number.isNaN(Number(size))) return size;
            const kb = 1024;
            const unit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
            if (`${size}` === '0') {
                return `0${unit[0]}`;
            }
            const i = Math.floor(Math.log(size) / Math.log(kb));
            const unitStr = unit[i];
            const precision = unitStr === unit[0] ? 4 : 3;
            const unitSize = size / Math.pow(kb, i);
            return `${unitSize.toPrecision(precision)} ${unitStr || ''}`;
        },
        autoPreview (item) {
            this.getPreviewAvailable(item.StoragePath)
                ? this.previewDocument(item)
                : this.downloadDocument('single', item);
        },
        // 预览文件
        previewDocument (file) {
            if (this.isImage(file.StoragePath)) {
                this.imgList = [file.StoragePath];
                this.$nextTick(() => {
                    this.$refs.imagePreviewer.clickHandler();
                });
                return;
            }
            let opener = window.open(file.StoragePath);
            opener = null;
        },
        async deleteDocument (index) {
            try {
                if (typeof this.beforeDelete === 'function') {
                    await this.beforeDelete(
                        this.fileList[index],
                        this.fileList
                    );
                }
                const data = this.fileList.splice(index, 1);
                this.$emit('change', data, this.fileList);
            } catch (error) { }
        },
        // 下载文件
        async downloadDocument (type, file) {
            switch (type) {
                case 'single':
                    try {
                        this.whichFile = file;
                        this.downloadSingleLoading = true;
                        const result =
                            await this.$api.AttachmentService.attachmentDownload(
                                {
                                    path: file.StoragePath
                                }
                            );
                        this.$utils.blobDownload(result, () => file.FileName);
                    } catch (error) {
                        console.log(
                            '%c AttachmentService.attachmentDownload Caught Error',
                            'font-size:18px;color:red;font-weight:700;',
                            error
                        );
                    }
                    this.downloadSingleLoading = false;
                    break;
                case 'multi':
                    try {
                        this.downloadMultiLoading = true;
                        const result =
                            await this.$api.AttachmentService.attachmentDownloadBatch(
                                {
                                    ids: this.fileList
                                        .map(item => item.PkSysFileId)
                                        .join(','),
                                    name: '附件'
                                }
                            );
                        this.$utils.blobDownload(result);
                    } catch (error) {
                        console.log(
                            '%c AttachmentService.attachmentDownloadBatch Caught Error',
                            'font-size:18px;color:red;font-weight:700;',
                            error
                        );
                    }
                    this.downloadMultiLoading = false;
                    break;
            }
        }
    }
};
</script>

<style scoped lang="scss">
.app-file-upload {
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .elp-upload__tip {
        opacity: 0.75;
        font-size: var(--font-size-base);
        line-height: var(--font-line-height-primary);
        margin-bottom: var(--spacer);
    }

    .file-list {
        .file-list-item {
            display: flex;
            align-items: baseline;
            margin-bottom: 0;

            & + .file-list-item {
                margin-top: 6px;
            }

            .file-list-item-icon {
                width: 18px;
                font-size: 18px;
                line-height: 18px;
                margin-right: 4px;
                transform: translateY(2px);
                flex: 0 0 auto;
            }

            .file-list-item-name {
                opacity: 0.95;
                font-size: 14px;
                flex: 0 1 auto;
                width: auto;
                overflow: hidden;
                // text-overflow: ellipsis;
            }

            .file-list-item-size {
                opacity: 0.65;
                margin: 0 20px 0 8px;
                flex: 0 0 auto;
            }

            .file-list-item-button-group {
                display: flex;
                flex: 0 0 auto;
                height: var(--font-line-height-primary);
                margin-left: var(--spacer-medium);

                .file-list-item-btn {
                    flex: 0 0 auto;
                    height: auto !important;
                    line-height: var(--font-line-height-primary);
                    padding: 0 !important;
                }
            }
        }
    }

    &.brief-layout {
        .file-list {
            .file-list-item {
                .file-list-item-icon {
                    margin-bottom: 1px;
                }

                .file-list-item-name {}

                .file-list-item-size {
                    margin-right: 0;
                }
            }
        }

        .file-list-item-button-group {
            margin-left: var(--spacer-medium);
        }
    }
}

.tiny-reference {
    display: inline-flex;
    align-items: baseline;

    :deep(.tiny-reference-btn) {
        .ecp-button-icon {
            margin-right: var(--spacer-small) !important;
            font-size: var(--font-size-medium) !important;
        }
    }
}

.file-list-item-name-text.brief-layout {
    display: inline;
    color: var(--color-primary);
    cursor: pointer;
}

.app-file-upload.tiny {
    .file-list {
        max-width: 600px;
        max-height: 500px;
        margin: calc(var(--spacer-small) * -1) 0;
        overflow-y: auto;

        .file-list-item {
            cursor: default;
            padding: var(--spacer-small) var(--spacer);
            padding-left: 6px;

            .file-list-item-button-group {
                visibility: hidden;
                flex: 1 0 auto;
                justify-content: flex-end;
                margin-left: 0;
            }

            & + .file-list-item {
                margin-top: 0;
            }

            &:hover {
                background: rgba(var(--color-primary-rgb), 12%);

                .file-list-item-button-group {
                    visibility: visible;
                    margin-left: var(--spacer-medium);
                }
            }
        }
    }
}

.download-multi-attachment {
    padding-bottom: var(--spacer-small);
}
</style>
<style lang="scss">
.elp-popover.app-file-upload.tiny {
    padding-left: var(--spacer);
    padding-right: var(--spacer);
}
</style>
