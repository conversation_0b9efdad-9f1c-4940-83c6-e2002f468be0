import { cloneDeep } from 'lodash-es';
import { Helper } from '@ecp/ecp-ui-plus';

import { isEmptyValue } from '@utils/helper';

export const {
    labelValueFormatter,
    // estimateWidth,

    formatNumberRound,
    formatBankNumber,

    strGetDotted,

    camelCaseToKebabCase,
    camelCaseToPascalCase,
    kebabCaseToCamelCase,
    kebabCaseToPascalCase,
    pascalCaseToCamelCase,
    pascalCaseToKebabCase
} = Helper;

/**
 * 字符串 - 获取字符串文本宽度
 * @param {String} str 需要计算的字符串
 * @returns {String}
 */
export const estimateWidth = (str) => {
    if (!str || typeof str !== 'string') return 0;

    let countZh = 0;
    let countOther = 0;

    for (let i = 0; i < str.length; i++) {
        const charCode = str.charCodeAt(i);
        if (
            (charCode >= 0x4e00 && charCode <= 0x9fff) || // 汉字范围
            (charCode >= 0x3400 && charCode <= 0x4dbf) || // 扩展A区汉字
            (charCode >= 0x20000 && charCode <= 0x2a6df) || // 扩展B区汉字
            (charCode >= 0x2a700 && charCode <= 0x2b73f) || // 扩展C区汉字
            (charCode >= 0x2b740 && charCode <= 0x2b81f) || // 扩展D区汉字
            (charCode >= 0xf900 && charCode <= 0xfaff) || // 兼容汉字
            (charCode >= 0x2f800 && charCode <= 0x2fa1d) || // 扩展E区汉字
            (charCode >= 0x3000 && charCode <= 0x303f) || // 中文符号
            (charCode >= 0xff00 && charCode <= 0xffef) // 全角符号
        ) {
            // 扩展E区汉字
            countZh += 2; // 中文字符算作2个英文字符宽度
        } else {
            countOther += 1; // 非中文字符算作1个英文字符宽度
        }
    }

    return countZh + countOther;
};

export const unitConfig = [
    {
        min: 100000000,
        unit: '亿'
    },
    {
        min: 10000,
        unit: '万'
    }
];

/**
 * 数字格式化 - 大数值加单位格式化
 * @param { Number|String } num 需要格式化的数字
 * @param { Object } customUnitConfig 自定义单位配置
 * @returns { Object }
 */
export const formatUnitNumber = (num, customUnitConfig) => {
    num = parseFloat(num);
    if (Number.isNaN(num)) {
        return null;
    }
    const targetUnitConfig = customUnitConfig || unitConfig;
    let matchUnitConfig = null;
    for (const item of targetUnitConfig) {
        if (num >= item.min) {
            matchUnitConfig = item;
            break;
        }
    }
    return matchUnitConfig
        ? {
            value: num / matchUnitConfig.min,
            min: matchUnitConfig.min,
            unit: matchUnitConfig.unit
        }
        : {
            value: num,
            unit: ''
        };
};

/**
 * 数字格式化 - 多数值获取最适单位
 * @param { Array<Number> } numList 需要计算的数字
 * @param { Object } customUnitConfig 自定义单位配置
 * @returns { Array }
 */
export const formatUnitNumberList = (numList, customUnitConfig) => {
    if (!numList || !Array.isArray(numList)) {
        return null;
    }
    const targetUnitConfig = customUnitConfig || unitConfig;
    return numList.reduce((prev, curr) => {
        const num = parseFloat(curr);
        if (Number.isNaN(num)) {
            return prev;
        }
        let matchUniIndex = null;
        for (const index in targetUnitConfig) {
            const item = targetUnitConfig[index];
            if (num >= item.min) {
                matchUniIndex = index;
                break;
            }
        }
        if (matchUniIndex === null) {
            matchUniIndex = prev.length - 1;
        }
        if (!prev[matchUniIndex].count) {
            prev[matchUniIndex].count = 0;
        }
        prev[matchUniIndex].count += 1;
        return prev;
    }, [
        ...cloneDeep(targetUnitConfig),
        {
            unit: ''
        }
    ]).reduce((prev, curr) => !prev ||
    (prev?.count || 0) <= (curr?.count || 0)
        ? curr
        : prev, null);
};

/**
 * @method joinStr 拼合为字符串
 *
 * @param { Array<String|Number|Null|Undefined> } valueArr 需要拼合的值数组
 * @param { Object|String } options 拼合的配置项
 * @param { String } options.seperator 分隔符
 * @param { Boolean } options.keepEmpty 是否保留空值
 * @param { String } options.emptyText 空值替换文本
 *
 * @returns { String }
 */
export const joinStr = (valueArr, options) => {
    let seperator = '';
    let keepEmpty = false;
    let emptyText = '';
    if (typeof options === 'string') {
        seperator = options;
    } else {
        seperator = !isEmptyValue(options?.seperator) ? options?.seperator : '';
        keepEmpty = !!options?.keepEmpty || false;
        emptyText = !isEmptyValue(options?.emptyText) ? options?.emptyText : '';
    }
    return valueArr
        .filter((str) => keepEmpty || !isEmptyValue(str))
        .map(str => isEmptyValue(str) ? (keepEmpty ? emptyText || `${str}` : '') : str)
        .join(seperator);
};

/**
 * @method formatData 详情展示格式化
 * @param { Object } row 数据
 * @param { Object } col 配置
 */
export const formatData = (row, col, isLabel) => {
    let value = '';
    if (isLabel && typeof col?.labelFormatter === 'function') {
        value = col.labelFormatter(row, col);
    } else if (!isLabel && typeof col?.formatter === 'function') {
        value = col.formatter(
            row,
            col
        );
    } else {
        value = isLabel ? col?.label : row[col.key];
    }
    return !isEmptyValue(value) ? value : '';
};

/**
 * @method formatXAxis 格式化x轴文字
 * @param { String } value 文本
 * @param { String } max 一行显示最大长度
 */
export const formatXAxis = (value, max = 6) => {
    value = value + '';
    // 超过max个字符需要换行, 超过max*2个字符显示前max*2-2个字符
    if (value.length > max*2) {
        return value.slice(0, max*2-2).replace(new RegExp(`(.{${max}})`, 'g'), '$1\n') + '...';
    }
    return value.replace(new RegExp(`(.{${max}})`, 'g'), '$1\n');
};
