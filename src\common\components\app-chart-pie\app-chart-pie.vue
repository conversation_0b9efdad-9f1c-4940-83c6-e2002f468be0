<template>
    <div class="app-pie-chart">
        <template v-if="graphData?.length">
            <div class="chart-legend">
                <div v-for="(item,index) in graphData" :key="index" class="chart-legend-item">
                    <i class="icon" :style="{ backgroundColor:item.color }"></i>
                    <div class="chart-legend-item-label">
                        <span class="chart-legend-item-text">{{ item.name }}</span>
                        <span class="chart-legend-item-rate font-number-bold">{{ item.value }}%</span>
                    </div>
                </div>
            </div>
            <ecp-chart-simple-pie v-bind="chartProps" :key="`${graphTimestamp}_pie`" />
        </template>
        <ecp-empty v-else />
    </div>
</template>

<script setup>
import { CHART_COLOR_LIST } from '@constants/enum-config';

const props = defineProps({
    graphData: {
        type: Array,
        default: () => []
    },
    chartType: {
        type: String,
        default: 'pie'
    },
    borderWidth: {
        type: Number,
        default: 4
    }
});

const graphTimestamp = ref(Date.now());

const defaultOptions = {
    series: [
        {
            type: 'pie',
            label: { show: false },
            itemStyle: {
                borderColor: '#fff',
                borderWidth: props.borderWidth
            },
            radius: props.chartType === 'ring' ? ['60%', '80%'] : ['0%', '80%']
        }
    ],
    unit: ['%']
};

const chartProps = computed(() => {
    const defaultProps = {
        type: 2,
        theme: 'whiteTheme',
        listVisible: false,
        list: props.graphData,
        color: CHART_COLOR_LIST,
        tooltipDimensions: ['名称', '占比']
    };
    return {
        ...defaultProps,
        option: {
            ...defaultOptions
        }
    };
});

</script>

<style scoped lang="scss">
.app-pie-chart {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .chart-legend {
        display: flex;
        justify-content: center;
        margin-top: var(--spacer-large-3);
        justify-content: space-around;

        .chart-legend-item {
            display: flex;
            flex-direction: row;

            &-label {
                display: flex;
                flex-direction: column;
            }

            &-text {
                font-size: 12px;
                color: var(--color-text-secondary);
                margin-bottom: var(--spacer-small);
            }

            &-rate {
                font-size: var(--font-size-larger-2);
                color: var(--color-text-primary);
                line-height: var(-font-line-height-larger-2);
            }

            .icon {
                display: inline-block;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                margin-right: var(--spacer);
                margin-top: 5px;
            }
        }
    }

    :deep(.ecp-chart-simple-pie) {
        justify-content: center;

        .ecp-chart-simple-pie-left .chart-bg {
            display: none;
        }
    }
}
</style>
