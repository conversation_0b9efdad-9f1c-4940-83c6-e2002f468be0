<script setup>
import { PARKING_LOT_DETAILS } from '../_constants/index';
import * as Api from '@api/index';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
const total = ref(0);
const loading = reactive({
    table: false,
    export: false
});
const tableData = reactive({
    total: 0,
    list: []
});
const searchForm = reactive({
    resourceType: null // 停车场类型
});
const pagination = reactive({
    pageSize: 10,
    pageNum: 1
});
// 获取停车场列表
const getParkingLotDetailsList = async () => {
    try {
        loading.table = true;
        const response = await Api.AssetDetails.getParkingLotDetailsList({ ...searchForm, ...pagination });
        const { Total, Data } = response;
        tableData.list = Data;
        tableData.total = Total;
    } catch (error) {
        console.log(error);
    } finally {
        loading.table = false;
    }
};
const parkingLotTypeList = ref([]);
// 获取停车场类型
const getParkingLotTypeList = async () => {
    try {
        const { Data } = await Api.AssetDetails.getParkingLotTypeList();
        parkingLotTypeList.value = Data;
    } catch (error) {
        console.log(error);
    }
};
// 搜索事件
const SearchEvent = () => {
    pagination.pageNum = 1;
    pagination.pageSize = 10;
    getParkingLotDetailsList();
};
// 重置
const ResetEvent = () => {
    searchForm.resourceType = null;
    pagination.pageNum = 1;
    pagination.pageSize = 10;
    getParkingLotDetailsList();
};
// 导出
const ExportEvent = async () => {
    try {
        loading.export = true;
        if (tableData.total > 0) {
            const response = await Api.AssetDetails.exportParkingLotDetailsList({
                pageSize: 10000,
                pageNum: 1,
                ...searchForm
            });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (error) {
        console.log(error);
    } finally {
        loading.export = false;
    }
};
onMounted(() => {
    getParkingLotTypeList();
    getParkingLotDetailsList();
});
</script>

<template>
    <ecp-layout-pagination content-scroll :total="tableData.total" v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize" :top-pagination="false" layout="prev, pager, next, sizes, jumper"
        @query="getParkingLotDetailsList()">
        <!-- <template #head>
            <div class="header-toolbar">

            </div>
        </template> -->
        <template #content>
            <div style="display: flex; flex-wrap: nowrap;">
                <el-form inline v-model="searchForm" ref="formRef">
                    <el-form-item label="停车场类型" prop="resourceType">
                        <el-select v-model="searchForm.resourceType">
                            <el-option v-for="i in parkingLotTypeList" :key="i" :label="i" :value="i"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>

                <div class="button-group">
                    <ecp-button type="primary" text="查询" @click="SearchEvent" :loading="loading.table" />
                    <ecp-button text="重置" @click="ResetEvent" :loading="loading.table" />
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading.export" @click="ExportEvent" />
                </div>
            </div>
            <app-dynamic-table :loading="loading.table" :table-data="tableData.list" :table-config="PARKING_LOT_DETAILS"
                style="height: calc(100vh - 310px);" />
        </template>
    </ecp-layout-pagination>
</template>

<style scoped lang="scss">
/* 添加样式以使按钮组居右 */
.button-group {
    display: flex;
    flex-grow: 1;
    justify-content: flex-end;
}
</style>
