<script setup>
import * as Api from '@api/index';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';

const props = defineProps({
    disabledFutureDates: {
        type: Function,
        default: () => true
    }
});
const _createDate = ref(new Date());
const loading = ref(false);
const tableData = ref([
    { LandCategory: '可经营性土地(亩)', LandCategory1: '已出让' },
    { LandCategory: '可经营性土地(亩)', LandCategory1: '剩余' },
    { LandCategory: '不可经营性土地(亩)', LandCategory1: '空包、重包、规划不符' }
]);
const tableHeader = ref([]);
const tableConfig = computed(() => ({
    cols: [
        { label: '土地类别', prop: 'LandCategory', width: '200px' },
        { label: '土地类别', prop: 'LandCategory1', width: '200px' },
        ...Array.from(tableHeader.value, value => ({
            label: value,
            prop: value,
            align: 'right',
            width: 'auto',
            formatter: (row, column) => {
                const value = row[column.property];
                return (value && Number.parseInt(value).toFixed(2)) || 0;
            }
        }))
    ],
    props: {
        headerCellStyle: ({ row, column, rowIndex, columnIndex }) => {
            if (rowIndex === 0 && columnIndex === 0) {
                return { display: 'none' };
            }
            if (rowIndex === 0 && columnIndex === 1) {
                column.colSpan = 2;
                return column;
            }
            return column;
        },
        spanMethod: ({ row, column, rowIndex, columnIndex }) => {
            if (rowIndex === 0 && columnIndex === 0) {
                return [2, 1];
            }
            if (rowIndex === 1 && columnIndex === 0) {
                return [0, 1];
            }
            if (rowIndex === 1 && columnIndex === 2) {
                return [1, (tableHeader.value.length - 1) || 1];
            }
            if (rowIndex === 1 && columnIndex === tableHeader.value.length + 1) {
                return [1, 1];
            }
            if (rowIndex === 1 && columnIndex > 2) {
                return [1, 0];
            }
            return [1, 1];
        }
    }
}));
const ExportEvent = async () => {
    try {
        if (tableData.value.length > 0) {
            const createDate = dayjs(_createDate.value).format('YYYY-MM');
            const response = await Api.AssetAnalysis.ExportLandStats({ createDate });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (e) {
        console.log('%c ExportEvent', 'font-size:18px;color:green;font-weight:700;', e);
        ElMessage.error('导出失败');
    }
};

const GetLandResourcesTable = async () => {
    try {
        loading.value = true;
        const createDate = dayjs(_createDate.value).format('YYYY-MM');
        const response = await Api.AssetAnalysis.ShowLandStats({ createDate });
        const { 土地类别, 剩余, 已出让 } = response.Data;
        tableHeader.value = 土地类别 || [];
        if (!剩余 || !已出让) {
            tableData.value = [
                { LandCategory: '可经营性土地(亩)', LandCategory1: '已出让' },
                { LandCategory: '可经营性土地(亩)', LandCategory1: '剩余' },
                { LandCategory: '不可经营性土地(亩)', LandCategory1: '空包、重包、规划不符' }
            ];
            return;
        }
        tableHeader.value.forEach((item, index) => {
            tableData.value[0][item] = 已出让[index] || '--';
            tableData.value[1][item] = 剩余[index] || '--';
            tableData.value[2][item] = response.Data['空包、重复包、规划不符'][index] || '--';
        });
        tableData.value[1]['合计'] = 剩余[0] || 0;
    } catch (e) {
        console.log('%c GetLandResourcesTable', 'font-size:18px;color:green;font-weight:700;', e);
    } finally {
        loading.value = false;
    }
};

onMounted(GetLandResourcesTable);
</script>

<template>
    <div class="real-estate-resources" style="height: 100%;width: 100%;">
        <ecp-layout-pagination>
            <template #head>
                <div class="header-toolbar">
                    <div class="header-toolbar__selector">
                        <el-date-picker v-model="_createDate" type="month" placeholder="选择年月"
                            :disabled-date="disabledFutureDates" style="width: 150px" :clearable="false"
                            @change="GetLandResourcesTable" />
                    </div>
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading" @click="ExportEvent" />
                </div>
            </template>
            <template #content>
                <app-dynamic-table :loading="loading" :table-data="tableData" :table-config="tableConfig"
                    :border="true" />
            </template>
        </ecp-layout-pagination>
    </div>
</template>

<style scoped lang="scss"></style>
