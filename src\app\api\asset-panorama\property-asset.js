import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

export const Propertyasset = {
    /**
     * 物业
     * @method getmetroOverheadDevelopment 地铁上盖租赁情况
     */
    getmetroOverheadDevelopment (params) {
        return request({
            url: `${AssetsAnalysisService}/property/metroOverheadDevelopment`,
            method: 'get',
            params
        }).then(({ data }) => data);
    },
    /**
     * 物业
     * @method getofficeBuilding 写字楼租赁情况
     */
    getofficeBuilding (params) {
        return request({
            url: `${AssetsAnalysisService}/property/officeBuilding`,
            method: 'get',
            params
        }).then(({ data }) => data);
    },
    /**
     * 物业
     * @method getconcourseRetailProperty 站厅商铺物业租赁情况
     */
    getconcourseRetailProperty (params) {
        return request({
            url: `${AssetsAnalysisService}/property/concourseRetailProperty`,
            method: 'get',
            params
        }).then(({ data }) => data);
    },
    /**
     * 物业publicSpaceRetailKiosk
     * @method getundergroundCommercialStreet 地下商业街租赁情况
     */
    getundergroundCommercialStreet (params) {
        return request({
            url: `${AssetsAnalysisService}/property/undergroundCommercialStreet`,
            method: 'get',
            params
        }).then(({ data }) => data);
    },
    /**
     * 物业
     * @method getpublicSpaceRetailKiosk
公共空间利用临时商亭租赁情况
     */
    getpublicSpaceRetailKiosk (params) {
        return request({
            url: `${AssetsAnalysisService}/property/publicSpaceRetailKiosk`,
            method: 'get',
            params
        }).then(({ data }) => data);
    },
    /**
     * 物业
     * @method getparkingLotManage 停车场数量管理情况
     */
    getparkingLotManage (params) {
        return request({
            url: `${AssetsAnalysisService}/property/parkingLotManage`,
            method: 'get',
            params
        }).then(({ data }) => data);
    },
    /**
     * 物业
     * @method getretailUnit 商铺租赁情况
     */
    getretailUnit (params) {
        return request({
            url: `${AssetsAnalysisService}/property/retailUnit`,
            method: 'get',
            params
        }).then(({ data }) => data);
    }
};
