/* eslint-disable no-unused-vars */
export default {
    name: 'demo',
    // 在绑定元素的父组件挂载之前调用
    beforeMount (el, { instance, arg, value, oldValue, modifiers, dir }) {},
    // 绑定元素的父组件挂载时调用
    mounted (el, { instance, arg, value, oldValue, modifiers, dir }) {
        console.log('%c [ demo- directive arg: value ]-10', 'font-size:13px; background:#5bd00c; color:#9fff50;', `${arg}: ${value}`);
    },
    // 在包含组件的 VNode 更新之前调用
    beforeUpdate (el, { instance, arg, value, oldValue, modifiers, dir }) {},
    // 在包含组件的 VNode 及其子组件的 VNode 更新之后调用
    updated (el, { instance, arg, value, oldValue, modifiers, dir }) {},
    // 在绑定元素的父组件卸载之前调用
    beforeUnmount (el, { instance, arg, value, oldValue, modifiers, dir }) {},
    // 卸载绑定元素的父组件时调用
    unmounted (el, { instance, arg, value, oldValue, modifiers, dir }) {}
};
