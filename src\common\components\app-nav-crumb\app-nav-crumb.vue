<!--
<docs>
# nav-crumb页面导航返回
- 带返回按钮的面包屑导航

## props
- isRoute 是否是路由跳转
- title 导航标题
- onBack 监听返回事件
</docs>
 -->

<template>
    <div class="app-nav-crumb">
        <div class="app-nav-container">
            <template v-if="isShowBackIcon">
                <i class="ecp-icon-left app-nav-link nav-back-link" @click.stop="onBackButtonClick"></i>
            </template>
            <div class="app-nav-title" :class="{ 'single': navTitleList.length < 2 }">
                <nav-crumb-item v-for="(nav, index) in navTitleList" v-bind="nav" :key="nav.key" :tag="nav.key"
                                :is-route="isRoute" :is-current="index === navTitleList.length - 1"
                                @onCrumbItemClick="onCrumbItemClick">{{ nav.label }}</nav-crumb-item>
            </div>
        </div>
        <slot name="nav-right"></slot>
    </div>
</template>

<script>
import NavCrumbItem from './nav-crumb-item.vue';
export default {
    name: 'app-nav-crumb',
    components: {
        'nav-crumb-item': NavCrumbItem
    },
    props: {
        isRoute: {
            type: Boolean,
            default: false
        },
        title: [String, Array],
        isShowBackIcon: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            navTitleList: []
        };
    },
    methods: {
        onBackButtonClick () {
            const self = this;
            try {
                if (self.isRoute) {
                    self.$router.back();
                }
            } catch { }
            self.$emit('onBack');
        },
        onCrumbItemClick (e) {
            const self = this;
            self.$emit('onBack', e);
        }
    },
    watch: {
        title: {
            handler (newVal) {
                const self = this;
                if (!newVal) return;
                if (typeof newVal === 'string') {
                    self.navTitleList = newVal
                        .split('>')
                        .map((item, index) => ({
                            label: item,
                            key: index + 1
                        }));
                } else {
                    self.navTitleList = [...newVal].map((item, index) => ({
                        ...item,
                        key: Object.hasOwnProperty.call(item, 'key')
                            ? item.key
                            : index + 1
                    }));
                }
            },
            immediate: true
        }
    }
};
</script>

<style scoped lang="scss">
.app-nav-crumb {
    height: var(--spacer-extra-large);
    display: flex;

    .app-nav-container {
        // padding: 4px 0;
        flex-grow: 1;
        display: flex;
        align-items: center;

        .nav-back-link {
            padding-right: var(--spacer-large) + 1px;
            margin-right: var(--spacer-large);
            font-size: var(--font-size-medium);
            line-height: var(--font-line-height-medium);
            color: var(--color-text-secondary);
            position: relative;
            cursor: pointer;

            &:hover {
                color: var(--color-primary-light-5);
            }

            &::after {
                width: 1px;
                height: 100%;
                content: '';
                background-color: var(--color-text-mark);
                position: absolute;
                top: 0;
                right: 0;
            }
        }
    }
}
</style>
