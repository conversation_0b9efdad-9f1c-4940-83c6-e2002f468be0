<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <!-- <template #head-right>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template> -->
        <!-- <template #content> -->
        <el-card class="card">
            <ecp-layout-pagination content-scroll :total="total" v-model:current-page="pagination.pageNum"
                v-model:page-size="pagination.pageSize" layout="prev, pager, next, sizes, jumper"
                @current-change="SearchEvent">
                <template #head>
                </template>
                <template #content>
                    <div class="header-toolbar">
                        <el-form inline v-model="searchForm" ref="formRef" style="display: flex;flex-wrap: nowrap;">
                            <!-- <el-form-item label="时间" prop="queryTime">
                                    <el-date-picker v-model="searchForm.queryTime" type="date" placeholder="选择日期"
                                        format="YYYY-MM-DD" value-format="YYYY-MM-DD">
                                    </el-date-picker>
                                </el-form-item> -->
                            <el-form-item label="项目分类" prop="propertyType">
                                <el-select v-model="searchForm.propertyType">
                                    <el-option v-for="(item, index) in projectTypeList" :key="index" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="所属路线" prop="udLineCode">
                                <el-select v-model="searchForm.udLineCode">
                                    <el-option v-for="item in lineList" :key="item.value" :label="item.LineName"
                                        :value="item.LineCode"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                        <div class="header-toolbar-right">
                            <ecp-button type="primary" text="查询" @click="getTableData" />
                            <ecp-button text="重置" @click="ResetEvent" />
                            <ecp-button icon="ecp-icon-export" text="导出" :loading="loading.export"
                                @click="ExportEvent" />
                        </div>
                    </div>
                    <app-dynamic-table :loading="loading.table" :table-data="tableData"
                        :table-config="HOUSE_RESOURCES_TABLE" style="height: calc(100vh - 220px);">
                    </app-dynamic-table>
                </template>
            </ecp-layout-pagination>
        </el-card>
        <!-- </template> -->
    </app-form-page>
</template>

<script setup>
import dayjs from 'dayjs';
import { HOUSE_RESOURCES_TABLE } from '../_constants/index';
import * as Api from '@api/index';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';

const name = 'asset-details-house-resources';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '房产资源'
        }
    ];
});

const projectTypeList = ref([]);
const lineList = ref([]);

onMounted(async () => {
    getTableData();
    const { Data: Data1 } = await Api.AssetDetails.getRouteInfo();
    console.log(Data1, 'Data1');
    lineList.value = Data1;
    const { Data: Data2 } = await Api.AssetDetails.getProjectType();
    console.log(Data2, 'Data2');

    // 使用 Object.entries 遍历对象的键值对，并使用 map 创建新数组
    projectTypeList.value = Object.entries(Data2).map(([key, value]) => ({
        value: key, // 使用键作为新对象的 value
        label: value // 使用值作为新对象的 label
    }));
});

const getTableData = async () => {
    loading.table = true;
    const { Data, Total } = await Api.AssetDetails.getRealEstateDetail({
        ...searchForm,
        ...pagination
    });
    console.log(Data);
    tableData.value = Data;
    total.value = Total;
    loading.table = false;
};

const total = ref(0);
const formRef = ref(null);
const loading = reactive({
    table: false,
    export: false
});
const searchForm = reactive({
    propertyType: '', // 项目分类
    udLineCode: '' // 所属路线
});
const pagination = reactive({
    pageSize: 10,
    pageNum: 1
});
const tableData = ref([]);

const SearchEvent = async (newPageNum) => {
    // console.log('12343');
    pagination.pageNum = newPageNum;
    getTableData();
};

const ResetEvent = async () => {
    pagination.pageNum = 1;
    searchForm.propertyType = '';
    searchForm.udLineCode = '';
    getTableData();
};

const ExportEvent = async () => {
    try {
        if (total.value > 0) {
            const response = await Api.AssetDetails.exportRealEstateDetail({ ...searchForm, ...pagination, pageSize: 10000 });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (error) {
        console.log('%c exportEvent error', 'color: red', error);
        ElMessage.error('导出失败');
    }
};

watch(() => pagination.pageSize, () => {
    getTableData();
});
</script>

<style lang="scss" scoped>
$page-name: asset-details-house-resources;

.#{$page-name} {
    :deep(.elp-select) {
        width: 120px;
    }

    .card {
        flex: 1 1 auto;
        display: flex;
        overflow: auto;
        height: 1vh;

        :deep(.elp-card__body) {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            padding-top: 0;
            width: 100%;
        }

        .header-toolbar {
            display: flex;

            .header-toolbar-right {
                display: flex;
                flex-grow: 1;
                justify-content: flex-end;
            }
        }
    }
}
</style>