<template>
    <div class="app-dynamic-table" ref="containerRef" v-resize="changeTableMaxHeight">
        <div class="app-dynamic-table-header" v-if="$slots.header">
            <slot name="header"></slot>
        </div>
        <el-table ref="tableInstance" class="app-dynamic-table-content" :border="false" v-bind="tableAttrs"
            v-loading="loading">
            <dynamic-table-column v-for="(tableColumn, index) in tableCols" :page-config="pageConfig" :col="tableColumn"
                :key="`${tableColumn.prop}_${index}`">
                <template #[getCustomSlotName(tableColumn.slot)]="defaultData" v-if="tableColumn.slot">
                    <slot :name="getCustomSlotName(tableColumn['slot'])" v-bind="defaultData"></slot>
                </template>
                <template #[getCustomSlotHeaderName(tableColumn.slotHeader)]="headerData" v-if="tableColumn.slotHeader">
                    <slot :name="getCustomSlotName(tableColumn['slotHeader'], true)" v-bind="headerData"></slot>
                </template>
                <template v-for="(child, childIndex) in tableColumn.cols"
                    :key="`${tableColumn.prop}_${index}_${childIndex}`"
                    v-slot:[getCustomSlotName(child.slot)]="defaultData">
                    <slot :name="getCustomSlotName(child.slot)" v-bind="defaultData" v-if="child.slot"></slot>
                </template>
            </dynamic-table-column>
            <template #empty>
                <slot name="empty">
                    <ecp-empty :size="emptySize"></ecp-empty>
                </slot>
            </template>
        </el-table>
    </div>
</template>

<script setup>
import { cloneDeep } from 'lodash-es';
import DynamicTableColumn from './dynamic-table-column.vue';

defineComponent({
    name: 'app-dynamic-table'
});

const props = defineProps({
    tableData: {
        type: Array,
        default: () => []
    },
    tableConfig: {
        type: Object,
        default: () => ({})
    },
    loading: {
        type: Boolean,
        default: false
    },
    pageConfig: {
        type: Object,
        default: () => ({})
    },
    emptySize: {
        type: String
    }
});

const $rootApp = inject('$app');
const maxHeight = ref('');
const attrs = useAttrs();

const containerRef = ref();
const tableInstance = ref();

const tableCols = computed(() =>
    Array.isArray(props.tableConfig.cols) ? cloneDeep(props.tableConfig.cols) : []
);

const tableAttrs = computed(() => {
    const tableProps = props.tableConfig.props || {};
    const currMaxHeight = tableProps.maxHeight || tableProps.height || maxHeight.value;
    return {
        highlightCurrentRow: true,
        stripe: true,
        ...tableProps,
        ...attrs,
        ...(currMaxHeight ? { maxHeight: currMaxHeight } : {}),
        data: props.tableData
    };
});

const getTableInstance = () => tableInstance.value;

const getDeepSlot = (tableColumn) => {
    const flatter = (arr) => {
        if (!arr || !(arr instanceof Array)) return [];
        return arr.reduce((prev, value) => {
            value.cols && arr.push(...flatter(value.cols));
            return arr;
        }, arr);
    };
    return flatter(tableColumn.cols).filter((item) => !!item.slot);
};

const getMatchedRelativeElem = (elem, matcher) => {
    let matchedElem = null;
    if (elem) {
        matchedElem =
            (elem.parentNode && elem.parentNode.querySelector(matcher)) || null;
        if (!matchedElem) {
            matchedElem = getMatchedRelativeElem(elem.parentNode, matcher);
        }
    }
    return matchedElem;
};

const getCustomSlotName = (templateName, isHeader) => {
    if (!templateName) return;

    return (isHeader ? `${templateName}-header` : templateName).toLowerCase();
};

const getCustomSlotHeaderName = (templateName) =>
    getCustomSlotName(templateName, true);

const getWrapper = (el, selector) => {
    if (!selector) return null;

    if (!el) el = containerRef.value;

    const parentElement = el.parentElement;

    if (!parentElement) return null;

    if (parentElement.classList.contains(selector)) {
        return parentElement;
    }
    return getWrapper(parentElement, selector);
};

const changeTableMaxHeight = () => {
    // // console.log('changeTableMaxHeight');
    // nextTick(() => {
    //     try {
    //         // if (!props.fixedMaxHeight || tableAttrs.value.maxHeight) {
    //         //     throw new Error('Not Fluid Table');
    //         // }
    //         console.log('%c tableAttrs.value', 'font-size:18px;color:purple;font-weight:700;', JSON.parse(JSON.stringify(tableAttrs.value)));
    //         if (tableAttrs.value.maxHeight) {
    //             maxHeight.value = tableAttrs.value.maxHeight;
    //             return;
    //         }
    //         const rootEl = $rootApp?.getRootEl();

    //         let visibleContentHeight =
    //             (rootEl && rootEl.querySelector('.app-layout-content')?.offsetHeight) ||
    //             0;
    //         if (!visibleContentHeight) {
    //             throw new Error('Visible Content Height Invalid');
    //         }

    //         const paginationWrapper = getWrapper('ecpp-layout-pagination-content');

    //         // top._paginationWrapper = paginationWrapper;

    //         const paginationComponent = [...(paginationWrapper?.children || [])].find(el => el?.classList?.contains('ecpp-layout-pagination-content-pagination'));

    //         const paginationComponentHeight = (paginationComponent?.offsetHeight || 0) + (paginationComponent?.computedStyleMap?.()?.get?.('margin-top')?.value || 0);

    //         const paginationHeight =
    //             paginationWrapper?.offsetHeight
    //                 ? paginationWrapper?.offsetHeight - paginationComponentHeight
    //                 : 0;

    //         const matchedHeaderElem = getMatchedRelativeElem(
    //             containerRef.value,
    //             '.ecpp-panel-header'
    //         );
    //         const panelHeaderHeight =
    //             (matchedHeaderElem ? matchedHeaderElem.offsetHeight : 40) || 0;
    //         visibleContentHeight =
    //             visibleContentHeight -
    //             paginationHeight -
    //             panelHeaderHeight - // panelHeader
    //             32 - // panelContentPadding
    //             48; // paginationLayoutPadding
    //         console.log('%c visibleContentHeight', 'font-size:18px;color:blue;font-weight:700;', visibleContentHeight);
    //         maxHeight.value = visibleContentHeight;
    //     } catch (error) {
    //         console.log('changeTableMaxHeight Caught Error', error);
    //     }
    // });
};

watch(
    () => props.pageConfig,
    () => {
        nextTick(() => {
            if (tableInstance.value?.bodyWrapper) {
                tableInstance.value.bodyWrapper.scrollTo({
                    top: 0,
                    left: 0,
                    behavior: 'smooth'
                });
            }
        });
    },
    {
        immediate: true,
        deep: true
    }
);

watch(
    () => props.tableData,
    () => {
        changeTableMaxHeight();
    },
    {
        immediate: true,
        deep: true
    }
);

defineExpose({
    getTableInstance
});
</script>

<style lang="scss" scoped>
.app-dynamic-table {
    display: flex;
    flex-direction: column;
    // height: 100%;
    flex: 1 1 auto;
    overflow: hidden;

    &-header {
        margin-bottom: var(--spacer-large);
    }

    :deep(.app-dynamic-table-content) {
        flex: 1 1 auto;
        overflow: hidden;

        .elp-table__header-wrapper,
        .elp-table__body-wrapper,
        .elp-table__footer-wrapper {

            tr td,
            tr th {

                &.elp-table-fixed-column--left,
                &.elp-table-fixed-column--right {

                    &.is-last-column,
                    &.is-first-column {
                        &:before {
                            bottom: 0;
                        }
                    }
                }
            }
        }

        .elp-table__body-wrapper {
            > .elp-scrollbar {
                > .elp-scrollbar__wrap {
                    display: flex;
                }
            }
        }
    }
}
</style>
