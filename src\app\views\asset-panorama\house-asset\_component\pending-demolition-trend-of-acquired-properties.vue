<script setup>
import LegendContainer from './legend-container.vue';
import * as Api from '@api/index';

const loading = ref(false);
const option = {
    color: ['#0367FC', '#11B062', '#487690'],
    legend: {
        show: false
    },
    grid: {
        top: '25px'
    },
    xAxis: {
        axisLabel: {
            textStyle: {
                color: 'rgba(0, 0, 0, 0.45)',
                fontFamily: 'D-DIN'
            }
        }
    },
    yAxis: {
        nameTextStyle: {
            padding: [0, -40, 0, 0]
        }
    },
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    series: [
        {
            type: 'line',
            itemStyle: {
                borderColor: '#fff',
                borderWidth: 2
            },
            lineStyle: {
                width: 4
            },
            symbolSize: 8,
            showSymbol: true
        },
        {
            type: 'line',
            itemStyle: {
                borderColor: '#fff',
                borderWidth: 2
            },
            lineStyle: {
                width: 4
            },
            symbolSize: 8,
            showSymbol: true
        }
    ]
};
const data = reactive({
    dimensions: ['日期', '已拆迁', '已征未拆'],
    source: []
});
const unit = ['万平方', '万平方'];
const value = reactive({
    已拆迁: 0,
    已征未拆: 0
});

const DemolitionTrends = async () => {
    try {
        loading.value = true;
        const response = await Api.AssetPanorama.DemolitionTrends();
        response.Data.forEach(item => {
            data.source.push({
                日期: item.Date,
                已拆迁: item.Demolished,
                已征未拆: item.UnDemolish
            });
            value.已拆迁 += item.Demolished;
            value.已征未拆 += item.UnDemolish;
        });
        data.source.reverse();
    } catch (error) {
        console.log('%c DemolitionTrends Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    } finally {
        loading.value = false;
    }
};

onMounted(DemolitionTrends);
</script>

<template>
    <app-business-panel mode="card" title="已征未拆拆迁趋势">
        <template #content>
            <div class="residential-sales-trends" v-loading="loading">
                <legend-container :name="['已拆迁', '已征未拆']" :value="value"></legend-container>
                <ecp-chart-base-line theme="whiteTheme" :data="data" :option="option" yName="单位：万平方" :unit="unit"
                    :lineWidth="4" />
            </div>
        </template>
    </app-business-panel>
</template>

<style scoped lang="scss">
.residential-sales-trends {}
</style>
