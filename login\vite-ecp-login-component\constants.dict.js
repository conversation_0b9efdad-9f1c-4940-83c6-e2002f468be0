/**
 * 登录方式映射
 */
export const LoginWayDict = {
    1: {
        key: 'PASSWORD',
        desc: '账号密码'
    },
    2: {
        key: 'PKI',
        desc: 'PKI'
    }
};

/**
 * 二次登录验证方式映射
 */
export const SecondLoginWayDict = {
    1: {
        key: 'PASSWORD',
        desc: '账号密码认证'
    },
    2: {
        key: 'PKI',
        desc: 'PKI认证'
    },
    3: {
        key: 'FACE',
        desc: '人脸认证'
    }
};

/**
 * 检验规则映射
 */
const NumberPattern = /[0-9]/;
const SpecialCharPattern = /[!@#$%^&*]/;
const LetterUpperPattern = /[A-Z]/;
const LetterLowerPattern = /[a-z]/;

export const PasswordVerifyDict = {
    1: {
        pattern: NumberPattern,
        desc: '数字'
    },
    2: {
        pattern: SpecialCharPattern,
        desc: '特殊字符("!@#$%^&*")'
    },
    3: {
        pattern: LetterUpperPattern,
        desc: '大写字母'
    },
    4: {
        pattern: LetterLowerPattern,
        desc: '小写字母'
    }
};

/* 账号加密密钥 */
export const UserNameSaltKey = 'PCI-202111170000';
/* 密码加密密钥 */
export const PwdSaltKey = '20211117-PCI0000';
