/* eslint-disable no-useless-escape, camelcase */
import { ElMessage, ElMessageBox } from 'element-plus';
import { LoginApi } from '../api';
import JIT_GW_ExtInterface from '../utils/pnxclient';
import * as Utils from '../utils/common';

const initPNXGWParam = `<\?xml version=\"1.0\" encoding=\"utf-8\"\?><authinfo><liblist><lib type=\"CSP\" version=\"1.0\" dllname=\"\" ><algid val=\"SHA1\" sm2_hashalg=\"sm3\"/></lib>
<lib type=\"SKF\" version=\"1.1\" dllname=\"SERfR01DQUlTLmRsbA==\" ><algid val=\"SHA1\" sm2_hashalg=\"sm3\"/></lib>
<lib type=\"SKF\" version=\"1.1\" dllname=\"U2h1dHRsZUNzcDExXzMwMDBHTS5kbGw=\" ><algid val=\"SHA1\" sm2_hashalg=\"sm3\"/></lib>
<lib type=\"SKF\" version=\"1.1\" dllname=\"U0tGQVBJLmRsbA==\" ><algid val=\"SHA1\" sm2_hashalg=\"sm3\"/></lib>
<lib type="CSP" version="1.0.0.1" dllname="ZVNhZmUgQ3J5cHRvZ3JhcGhpYyBTZXJ2aWNlIFByb3ZpZGVyIHYxLjA="><algid val="SHA1" sm2_hashalg="SHA1" /></lib>
<lib type="SKF" version="1.7.8.3" dllname="R0FLRVlfU0tGLmRsbA=="><algid val="SHA1" sm2_hashalg="SM3" /></lib>
<lib type="SKF" version="1.0.0.2" dllname="RlJJRHJpdmVyU0tGLmRsbA=="><algid val="SHA1" sm2_hashalg="SM3" /></lib>
</liblist></authinfo>`;

export const pkiLogin = async (hasSecondLoginFlag = false) => {
    // 检测是否安装插件
    try {
        JIT_GW_ExtInterface.GetVersion();
        console.log('start pki login...');
        const res = await getPkiRandom(hasSecondLoginFlag);
        return res;
    } catch (error) {
        console.log(
            '%c [ECP-LOGIN] pkiLogin Caught error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
        ElMessageBox.confirm('未安装控件，是否下载?', '提示', {
            confirmButtonText: '确 定',
            cancelButtonText: '取 消',
            type: 'warning'
        })
            .then(async () => {
                window.location.href = '/sso/gw/PNXClient.exe';
            })
            .catch(() => {});
    }
    return false;
};

export const getPkiRandom = async hasSecondLoginFlag => {
    const guid = Utils.guid();
    try {
        const result = await LoginApi.pkiRandom({
            clientKey: guid
        });
        if (result.OpCode === 0) {
            return await doDataProcess(
                initPNXGWParam,
                result.Data,
                guid,
                hasSecondLoginFlag
            );
        } else {
            ElMessage.warning(result.OpDesc || '获取认证原文失败');
        }
        return result;
    } catch (error) {
        console.log(
            '%c [ECP-LOGIN] getPkiRandom Caught error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    }
    return false;
};

export const doPkiRequest = async (param, hasSecondLoginFlag) => {
    const isNativeClient = window?._env?.isNativeClient; // 是否托盘客户端
    const targetApi = hasSecondLoginFlag ? 'ssoCheck' : 'pkiLogin';
    try {
        const params = hasSecondLoginFlag
            ? {
                LoginWay: 2,
                Param: param
            }
            : param;
        const options = {
            headers: {
                x_op_client: isNativeClient ? 2 : 1
            }
        };
        const res = await LoginApi[targetApi](params, options);
        const result = res;
        return result;
    } catch (error) {
        console.log(
            `%c [ECP-LOGIN] doPkiRequest ${targetApi} Caught error`,
            'font-size:18px;color:red;font-weight:700;',
            error
        );
    }
    return false;
};

export const doDataProcess = async (
    initPNXGWParam,
    original,
    guid,
    hasSecondLoginFlag
) => {
    try {
        JIT_GW_ExtInterface.GetVersion();
    } catch (error) {
        console.log(
            '%c [ECP-LOGIN] doDataProcess Caught error',
            'font-size:18px;color:red;font-weight:700;',
            error
        );
        ElMessage.warning('未安装控件，请进行安装控件');
    }

    // 认证原文，一串随机数字
    const authContent = original;

    // 证书版本者主题
    // var signSubject = "";

    // 签名结果
    let signResult;

    if (authContent === '') {
        ElMessage.warning('认证原文不能为空!');
    } else {
        JIT_GW_ExtInterface.ClearFilter();

        // 初始化vctk控件
        JIT_GW_ExtInterface.Initialize('', initPNXGWParam);
        JIT_GW_ExtInterface.AddFilter(11, '2'); // 国密证书过滤代码
        // 控制证书为一个时，不弹出证书选择框
        JIT_GW_ExtInterface.SetChooseSingleCert(1);

        // 生成签名信息
        signResult = JIT_GW_ExtInterface.P7SignString(authContent, true, true);
        if (JIT_GW_ExtInterface.GetLastError() !== 0) {
            if (
                JIT_GW_ExtInterface.GetLastError() === 3758096386 ||
                JIT_GW_ExtInterface.GetLastError() === 2148532334
            ) {
                ElMessage.warning('用户取消操作');
                return false;
            } else if (
                JIT_GW_ExtInterface.GetLastError() === -536870815 ||
                JIT_GW_ExtInterface.GetLastError() === 3758096481
            ) {
                ElMessage.warning(
                    '没有找到有效的证书，如果使用的是KEY，请确认已经插入key'
                );
                return false;
            } else {
                ElMessage.warning(JIT_GW_ExtInterface.GetLastErrorMessage());
                return false;
            }
        }

        if (signResult) {
            const time = Date.parse(new Date());
            const serviceParam = Utils.getUrlParam('service');
            const param = {
                clientKey: guid,
                original,
                signedData: signResult,
                sign: Utils.stringsToHex(
                    Utils.Encrypt(guid + original + signResult, 'PCI' + time)
                ),
                time,
                service: serviceParam || Utils.stringsToHex(origin),
                alg: '2'
            };
            const res = await doPkiRequest(param, hasSecondLoginFlag);
            return res;
        } else {
            return false;
        }
    }
};
