@use "./config.scss" as *;

.second-login-face,
.dialog-second-login-face {
    --ecpp-login-face-color: rgba(1, 254, 254, 1);
    --ecpp-login-face-color-hover: rgba(2, 254, 254, 0.75);
    --ecpp-login-face-color-active: rgba(2, 254, 254, 0.5);
    --ecpp-login-face-color-disabled: rgba(2, 254, 254, 0.25);
}

.second-login-face {
    width: 60%;
    height: 100%;
    margin: auto;

    .picture-area {
        position: relative;
        aspect-ratio: 1;
        background: url(../assets/face-dialog-border.png);
        background-size: 100% 100%;
        padding: 8px;

        &-circle {
            padding: 8px;
            aspect-ratio: 1;
            margin: auto;
            background: url(../assets/face-dialog-border-circle.png);
            background-size: 100% 100%;
        }

        &-canvas {
            position: relative;
            aspect-ratio: 1;
            margin: auto;
            background: #fff;
            border-radius: 50%;
            border: 1px solid var(--ecpp-login-face-color);
            overflow: hidden;
        }

        .#{$namespace}-loading-spinner {
            font-size: 24px;
        }
    }

    .button-group {
        margin-top: 24px;
        position: relative;

        .capture-btn,
        .confirm-btn {
            --#{$namespace}-button-text-color: rgba(0, 0, 0, 0.65);
            --#{$namespace}-button-hover-text-color: rgba(0, 0, 0, 0.65);
            --#{$namespace}-button-active-text-color: rgba(0, 0, 0, 0.65);
            --#{$namespace}-button-bg-color: var(--ecpp-login-face-color);
            --#{$namespace}-button-hover-bg-color: var(--ecpp-login-face-color-hover);
            --#{$namespace}-button-active-bg-color: var(--ecpp-login-face-color-active);
            --#{$namespace}-button-border-color: var(--#{$namespace}-button-bg-color);
            --#{$namespace}-button-hover-border-color: var(--#{$namespace}-button-hover-bg-color);
            --#{$namespace}-button-active-border-color: var(--#{$namespace}-button-active-bg-color);
        }

        .capture-btn,
        .confirm-btn {
            width: calc(50% - 8px);
            // cursor: pointer;
            height: 32px;
            border: none;
            // background-color: var(--ecpp-login-face-color);
            border-radius: 2px;
            text-align: center;
            font-size: 14px;
            line-height: 32px;
            // color: var(--#{$namespace}-button-text-color);
            user-select: none;
        }

        .capture-btn.full-width {
            width: 100%;
        }

        .capture-btn.disabled {
            cursor: not-allowed;
            background-color: var(--ecpp-login-face-color-disabled);
        }

        .confirm-btn {
            position: absolute;
            top: 0;
            right: 0;
        }
    }
}

.dialog-second-login-face {
    @media only screen and (max-width: 1280px) {
        .#{$namespace}-dialog {
            width: 384px;
            height: 384px;

            .second-login-face {
                width: 45%;
            }
        }
    }

    @media only screen and (min-width: 1281px) and (max-width: 1440px) {
        .#{$namespace}-dialog {
            width: 432px;
            height: 432px;

            .second-login-face {
                width: 50%;
            }
        }
    }

    &-modal {
        backdrop-filter: blur(2px);
    }

    &.#{$namespace}-dialog {
        width: 575px;
        height: 575px;
        background: url(../assets/face-dialog-bg.png);
        background-size: cover;

        .#{$namespace}-dialog__header {
            padding: 24px 24px 32px;
            border-bottom: none;
            color: var(--ecpp-login-face-color);
            display: flex;
            flex-direction: column;

            .back-icon {
                cursor: pointer;
                font-size: 24px;
                position: absolute;
                top: 24px;
                left: 24px;
            }

            .title-text {
                font-size: 24px;
                line-height: 32px;
                text-align: center;
                user-select: none;
            }

            .title-decoration {
                width: 12%;
                height: 1px;
                background: var(--ecpp-login-face-color);
                margin: 8px auto 0;
            }
        }

        .#{$namespace}-dialog__body {
            height: calc(100% - 111px);
            padding: 0;
        }
    }
}