import { Helper } from '@ecp/ecp-ui-plus';

export const isEmptyValue = Helper.isEmptyValue;

// 递归生成导航菜单
export function reformatMenu (menu, leafOnly, ignoreCase) {
    return (
        (Array.isArray(menu) &&
          menu.reduce((prev, menuItem) => {
              const { Id, Text, Value, ChildNodes } = menuItem;
              let result = prev;
              if (ignoreCase) {
                  const ignoreMatched = Object.keys(ignoreCase).every(key => {
                      const targetMenuItem = (key || '')
                          .split('.')
                          .reduce(
                              (prev, curr) =>
                                  prev === null || prev === undefined
                                      ? prev
                                      : prev[curr],
                              menuItem
                          );
                      return typeof targetMenuItem === 'string'
                          ? targetMenuItem.match(ignoreCase[key])
                          : targetMenuItem === ignoreCase[key];
                  });
                  if (ignoreMatched) {
                      return result;
                  }
              }
              if (
                  (Value.Target &&
                      (!leafOnly || Value.Target.match('/'))) ||
                  Value.Url
              ) {
                  result.push({
                      Id,
                      Text,
                      Value,
                      Url: Value.Url || null,
                      Target: Value.Target || null,
                      FunTag: Value.FunTag || null,
                      ChildNodes: reformatMenu(
                          ChildNodes,
                          leafOnly,
                          ignoreCase
                      )
                  });
              } else if (leafOnly && ChildNodes) {
                  result = [
                      ...result,
                      ...reformatMenu(ChildNodes, leafOnly, ignoreCase)
                  ];
              }
              return result;
          }, [])) ||
      []
    );
}

// 深度搜索权限树
export function findDeep (menu, key, value) {
    if (menu && menu instanceof Array && menu.length > 0) {
        for (const menuItem of menu) {
            if (key) {
                const targetMenuItem = (key || '')
                    .split('.')
                    .reduce(
                        (prev, curr) =>
                            prev === null || prev === undefined
                                ? prev
                                : prev[curr],
                        menuItem
                    );
                if (targetMenuItem === value) {
                    return menuItem;
                }
            }
            if (menuItem.ChildNodes) {
                const childItem = findDeep(menuItem.ChildNodes, key, value);
                if (childItem) {
                    return childItem;
                }
            }
        }
    }
    return null;
}

/**
* @method flattenDeep 深度展开权限树
* @param { Array } menu 权限树
* @param { string } key 需要扁平化的字段 不可为空
* @param { string } filterKey 权限筛选字段，由于菜单列表已经是默认返回用户所能看的全部权限，所以默认与key一个值
* @returns { Array }
*/
export function flattenDeep (menu, key, filterKey = key) {
    return (
        (Array.isArray(menu) &&
          key &&
          menu.reduce(
              (prev, curr) => [
                  ...prev,
                  ...(curr[key] !== undefined &&
                  curr[key] !== null &&
                  curr[filterKey]
                      ? [curr[key]]
                      : []),
                  ...flattenDeep(curr.ChildNodes, key, filterKey)
              ],
              []
          )) ||
      []
    );
}

/**
 * 按字典返回结构格式化自定义枚举项
 * @param {Array} list 枚举项
 * @param {Object} prop 配置项
 * @returns
 */
export const formatDict = (list, prop, nested) => {
    if (!Array.isArray(list) || !prop) return [];

    const mergedProps = {
        label: !isEmptyValue(prop.label) ? prop.label : 'Text',
        value: !isEmptyValue(prop.value) ? prop.value : 'Value',
        children: !isEmptyValue(prop.children) ? prop.children : 'ChildNodes'
    };

    return list.map((item) => {
        const label = !isEmptyValue(item[mergedProps.label]) ? item[mergedProps.label] : item.Text;
        const value = !isEmptyValue(item[mergedProps.value]) ? item[mergedProps.value] : item.Value;
        return {
            ...item,
            Text: label,
            Value: value,

            Name: label,
            Code: value,
            ...(
                Array.isArray(item[mergedProps.children || 'ChildNodes'])
                    ? {
                        ChildNodes: formatDict(item[mergedProps.children], prop, true)
                    }
                    : {}
            )
        };
    }).filter(item => prop.keepEmptyValue || !isEmptyValue(item.Value));
};
