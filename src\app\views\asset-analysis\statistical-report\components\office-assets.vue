<script setup>
import * as Api from '@api/index';
import { downloadBlobData } from '@utils/download';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';

const props = defineProps({
    disabledFutureDates: {
        type: Function,
        default: () => true
    }
});
const _createDate = ref(new Date());
const loading = ref(false);
const tableData = ref([]);
const tableHeader = ref([]);
const tableConfig = computed(() => ({
    cols: [
        { label: '资产类别', prop: 'organization', width: '200px' },
        ...Array.from(tableHeader.value, value => ({ label: value, prop: value, align: value !== '单位' ? 'right' : 'left', width: 'auto' }))
    ]
}));
const ExportEvent = async () => {
    try {
        if (tableData.value.length > 0) {
            const createDate = dayjs(_createDate.value).format('YYYY-MM');
            const response = await Api.AssetAnalysis.ExportOfficeAssetStats({ createDate });
            downloadBlobData(response);
            ElMessage.success('导出成功');
        } else {
            ElMessage.warning('暂无数据，不可导出');
        }
    } catch (e) {
        console.log('%c ExportEvent', 'font-size:18px;color:green;font-weight:700;', e);
        ElMessage.error('导出失败');
    }
};

const GetOfficeAssetsTable = async () => {
    try {
        loading.value = true;
        const createDate = dayjs(_createDate.value).format('YYYY-MM');
        const response = await Api.AssetAnalysis.ShowOfficeAssetStats({ createDate });
        tableHeader.value = response.Data['单位名称'];
        tableData.value = [];
        for (const responseDataKey in response.Data) {
            if (responseDataKey === '单位名称') {
                continue;
            }
            const data = { organization: responseDataKey };
            tableHeader.value.forEach((header, index) => {
                data[header] = response.Data[responseDataKey][index];
            });
            tableData.value.push(data);
        }
    } catch (e) {
        console.log('%c GetOfficeAssetsTable', 'font-size:18px;color:green;font-weight:700;', e);
    } finally {
        loading.value = false;
    }
};

onMounted(GetOfficeAssetsTable);
</script>

<template>
    <div class="real-estate-resources" style="height: 100%;width: 100%;">
        <ecp-layout-pagination>
            <template #head>
                <div class="header-toolbar">
                    <div class="header-toolbar__selector">
                        <el-date-picker v-model="_createDate" type="month" placeholder="选择年月"
                            :disabled-date="disabledFutureDates" style="width: 150px" :clearable="false"
                            @change="GetOfficeAssetsTable" />
                    </div>
                    <ecp-button icon="ecp-icon-export" text="导出" :loading="loading" @click="ExportEvent" />
                </div>
            </template>
            <template #content>
                <app-dynamic-table :loading="loading" :table-data="tableData" :table-config="tableConfig" />
            </template>
        </ecp-layout-pagination>
    </div>
</template>

<style scoped lang="scss"></style>
