<script setup>
import { CaretTop, CaretBottom } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';

const router = useRouter();
const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    },
    split: {
        type: Boolean,
        default: false
    },
    increase: {
        type: Boolean,
        default: false
    },
    icon: {
        type: String,
        default: ''
    },
    title: {
        type: String,
        default: '默认标题'
    },
    value: {
        type: Number,
        default: 0
    },
    unit: {
        type: String,
        default: '单位'
    },
    monthlyChainRatio: {
        type: String,
        default: '0%'
    },
    FunTag: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: ''
    }
});
const tempTitle = computed(() => `跳转到${props.title.match(/累计(.*)(数量|面积)/)[1]}页面`);

const goto = () => {
    if (!tempTitle.value.startsWith('暂未开放')) return router.push({ name: props.name });
    ElMessage.info({ message: '暂未开放，敬请期待~', showClose: true, grouping: true });
};
</script>

<template>
    <div class="statistic-card" :title="tempTitle" :class="{'split-line': split}" v-loading="loading" @click="goto">
        <div class="statistic-card-header">
            <img class="statistic-card-header__icon" :src="icon" alt="">
            <span class="statistic-card-header__title">{{ title }}</span>
        </div>
        <div class="statistic-card-content">
            <span class="statistic-card-content__value font-number-bold">{{ value.toLocaleString() }}</span>
            <span class="statistic-card-content__unit font-source-medium">{{ unit }}</span>
        </div>
        <div class="statistic-card-footer">
            <span class="statistic-card-footer__title">月环比</span>
            <span class="monthly-chain-ratio font-number" :class="increase ? 'increase' : 'decrease'">
                <el-icon>
                    <CaretTop v-if="increase" />
                    <CaretBottom v-else />
                </el-icon>
                {{ monthlyChainRatio }}
            </span>
        </div>
    </div>
</template>

<style scoped lang="scss">
.statistic-card {
    display: block;
    width: 100%;
    padding-left: var(--spacer-large);
    cursor: pointer;

    &.split-line {
        padding-left: var(--spacer-extra-large);
        border-left: 1px solid rgba(29, 33, 55, 0.1);
    }

    &-header {
        display: flex;
        align-items: center;
        gap: var(--spacer);

        &__icon {
            width: 24px;
            height: 24px;
        }

        &__title {
            color: var(--color-black-opacity-8-5);
        }
    }

    &-content {
        margin: 15px 0 12px 0;

        &__value {
            font-size: 40px;
            font-weight: bold;
            color: #1D2137;
            margin-right: 6px;
        }

        &__unit {
            font-size: var(--font-size-medium);
            color: #1D2137;
        }
    }

    &-footer {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: var(--font-size-small);

        &__title {
            font-size: var(--font-size-small);
            color: var(--color-black-opacity-6-5);
        }

        .monthly-chain-ratio {
            font-size: var(--font-size-base);
            display: flex;
            align-items: center;
            gap: 5px;

            &.increase {
                color: #E5484D;

                i {
                    transform: translateY(1.5px);
                }
            }

            &.decrease {
                color: #30A46C;
            }
        }
    }
}

@media screen and (max-width: 1400px) {
    .statistic-card {

        &.split-line {
            padding-left: var(--spacer-large);
        }
    }
}
</style>
