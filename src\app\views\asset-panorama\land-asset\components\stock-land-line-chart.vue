<template>
    <app-business-panel class="stock-land-line-chart" :interval="false" :intervalHeader="false"
        v-loading="loading">
        <template #default>
            <div class="stock-land-line-chart__content">
                <template v-if="graphData?.source?.length">
                    <ecp-chart-base-line v-bind="chartProps" :key="`${graphTimestamp}_line`" />
                </template>
                <ecp-empty v-else />
            </div>
        </template>
    </app-business-panel>
</template>

<script setup>
import { LandAssetApi } from '@api/asset-panorama/land-asset';
import dayjs from 'dayjs';
import { echarts } from 'ecp-chart';
import { CHART_COLOR_LIST } from '@constants/enum-config';

const loading = ref(false);

const legend = ref({
    show: false
});

const graphData = ref({
    dimensions: [],
    source: []
});

const graphTimestamp = ref(Date.now());

const defaultOptions = {
    color: CHART_COLOR_LIST,
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    grid: {
        top: 30,
        right: 0,
        bottom: 0
    },
    xAxis: {
        axisLabel: {
            rotate: 60,
            textStyle: {
                color: 'rgba(0, 0, 0, 0.45)',
                fontFamily: 'D-DIN'
            }
        }
    },
    yAxis: {
        nameTextStyle: {
            padding: [0, 0, 0, -20],
            align: 'left'
        }
    },
    series: [
        {
            type: 'line',
            itemStyle: {
                borderColor: '#fff',
                borderWidth: 2
            },
            smooth: 0.5,
            lineStyle: {
                width: 4
            },
            symbolSize: 8,
            showSymbol: false,
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(3,103,252,0.2)' },
                    { offset: 1, color: 'rgba(3,103,252,0)' }
                ])
            }
        }
    ]
};

const chartProps = computed(() => {
    const defaultProps = {
        theme: 'whiteTheme',
        data: graphData.value,
        legend: legend.value,
        yName: '单位：亩',
        unit: '亩'
    };
    return {
        ...defaultProps,
        option: {
            ...defaultOptions
        }
    };
});

const getChartData = async () => {
    console.log('%c getChartData', 'font-size:18px;color:gold;font-weight:700;');

    loading.value = true;
    graphData.value.source = [];

    try {
        const res = await LandAssetApi.getStockLand();
        const dimensions = ['时间', '存量土地面积'];
        const source = (res || []).map(item => ({
            时间: item.DataName,
            存量土地面积: item.Percentage
        }));

        graphData.value = {
            dimensions,
            source
        };
    } catch (error) {
        console.log('%c getTransferStatDataTrend Caught Error', 'font-size:18px;color:red;font-weight:700;', error);
    }

    loading.value = false;
};

onActivated(() => {
    getChartData();
});

</script>

<style scoped lang="scss">
.stock-land-line-chart {
    overflow: hidden;

    &__content {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
}
</style>
