<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <template #head-right>
            <el-select class="mr-2" v-model="selectedLine.line" placeholder="所属线路" style="width: 150px">
                <el-option v-for="(item, index) in selectedLine.lineArray" :key="index" :label="item.ShowName"
                    :value="item.Code" />
            </el-select>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template>
        <template #content>
            <div class="asset-analysis-layout">
                <div class="row">
                    <!-- 资产使用情况 -->
                    <AssetUsagePieChart class="chart left-top" :selected-line="selectedLine"
                        :is-initial-load="isInitialLoad" />
                    <!-- 资产状态vs客流分析 -->
                    <FlowAnalysisBarLineChart class="chart middle-top" :selected-line="selectedLine"
                        :is-initial-load="isInitialLoad" />
                    <!-- 各类资产状态分析 -->
                    <StatusAnalysisBarLineChart class="chart right-top" :selected-line="selectedLine"
                        :is-initial-load="isInitialLoad" />
                </div>
                <div class="row">
                    <!-- 资产类型结构分布 -->
                    <app-business-panel mode="card" v-loading="loading1" class="chart left-b" title="资产类型结构分布">
                        <AssetTypeStructureDistribution v-if="chartData && chartData.length > 0" :key="chartData.length"
                            :chartData="chartData" />
                        <ecp-empty v-else description="暂无数据"></ecp-empty>
                    </app-business-panel>
                    <!-- 资产列表 -->
                    <asset-type-table class="chart right-b" :selected-line="selectedLine"
                        :is-initial-load="isInitialLoad" />
                </div>
            </div>
        </template>
    </app-form-page>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onActivated } from 'vue';
import FlowAnalysisBarLineChart from './components/flow-analysis-bar-line-chart.vue';
import StatusAnalysisBarLineChart from './components/status-analysis-bar-line-chart.vue';
import AssetTypeTable from './components/asset-type-table.vue';
import AssetTypeStructureDistribution from '@views/asset-analysis/office-asset-analysis/components/asset-type-structure-distribution.vue';
import AssetUsagePieChart from './components/asset-usage-pie-chart.vue';
import { OperatingAsset } from '@api/asset-panorama/operating-asset';
import { LineAssetAnalysis } from '@api/asset-analysis/line-asset-analysis';
import { STACK_COLOR_LIAT } from '@constants/enum-config';

import dayjs from 'dayjs';

const name = 'asset-analysis-office-asset-analysis';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '线路资产分析'
        }
    ];
});

// 在页面被激活时重新调用
onActivated(() => {
    fetchAssetRateData();
});

const selectedLine = reactive({
    line: '',
    lineArray: []
});

const isInitialLoad = ref(true);

onMounted(() => {
    OperatingAsset.getLineCode().then(response => {
        if (response.Data && response.Data.length) {
            const lines = response.Data.map(item => ({
                Code: item.LineCode,
                ShowName: item.LineName
            }));
            // 添加“全部路线”选项
            lines.unshift({ Code: 'all', ShowName: '全部路线' });
            selectedLine.lineArray = lines;
            // 设置默认值为“全部路线”
            selectedLine.line = 'all';
            isInitialLoad.value = false;
        } else {
            console.log('没有线路数据返回');
        }
    }).catch(error => {
        console.error('获取线路代码集合失败:', error);
    });
}
);
const test = ref(false);
const loading1 = ref(false);
const chartData = ref([]);

const fetchAssetRateData = async () => {
    if (isInitialLoad.value) {
        return;
    }
    loading1.value = true; // 开始加载
    const params = {
        udLine: parseInt(selectedLine.line)
    };
    if (selectedLine.line !== 'all') {
        params.udLine = selectedLine.line;
    }
    try {
        const response = await LineAssetAnalysis.getassetRate(params);
        console.log('堆叠图数据', response);
        if (Array.isArray(response.Data) && response.Data.length > 0) {
            // empty.chart1 = false;
            // 对数据进行排序，按照AssetValue降序排列
            const sortedData = response.Data.sort((a, b) => {
                return b.AssetValue - a.AssetValue;
            });
            chartData.value = response.Data.map((item, index) => ({
                value: (item.AssetValue * 100).toFixed(2), // 转换为百分比
                name: item.AssetName,
                color: getColor(index)
            }));
        } else {
            chartData.value = [];
            test.value = true;
        }
        await nextTick();
        if (chartData.value.length === 0) {
            test.value = true;
        } else {
            test.value = false;
        }
    } catch (error) {
        console.error('获取资产类型结构分布数据失败', error);
        chartData.value = [];
    } finally {
        loading1.value = false; // 开始加载
    }
};

const getColor = (index) => {
    return STACK_COLOR_LIAT[index % STACK_COLOR_LIAT.length];
};

watch(() => selectedLine.line, (newLine) => {
    fetchAssetRateData();
});
</script>

<style lang="scss" scoped>
$page-name: asset-analysis-office-asset-analysis;

.#{$page-name} {
    .asset-analysis-layout {
        height: 100vh;
        width: 100%;
        display: grid;
        grid-template-columns: 1fr 1.25fr 1.25fr;
        grid-template-rows: 1fr 1.25fr;
        gap: 16px;

        .row {
            display: contents;

            .chart {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                overflow: auto;
                /* 保证内容上下对齐 */

                // 第一行
                &.left-top {
                    grid-column: 1;
                }

                &.middle-top {
                    grid-column: 2;
                }

                &.right-top {
                    grid-column: 3;
                }

                // 第二行
                &.left-b {
                    grid-column: 1;

                    :deep(.app-business-panel-content) {
                        overflow: hidden;
                    }
                }

                &.right-b {
                    grid-column: 2 / span 2;
                }

                /* 确保模块高度相同 */
                min-height: 300px;
                min-width: 360px
            }
        }
    }
}
</style>
