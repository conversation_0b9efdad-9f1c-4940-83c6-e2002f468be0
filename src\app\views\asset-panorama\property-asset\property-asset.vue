<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <template #head-right>
            <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span>
        </template>
        <template #content>
            <div class="property-asset-layout">
                <app-business-panel mode="card" class="box usage-status" title="写字楼租赁情况" v-loading="loading.chart1">
                    <ChartComponent :chartData="chartData2" :options="chartOptions" :theme="theme">
                    </ChartComponent>
                </app-business-panel>
                <app-business-panel mode="card" class="box usage-status" title="站厅商城铺物业租赁情况" v-loading="loading.chart2">
                    <ChartComponent :chartData="chartData3" :options="chartOptions" :theme="theme">
                    </ChartComponent>
                </app-business-panel>
                <app-business-panel mode="card" class="box usage-status" title="地下商业街租赁情况" v-loading="loading.chart3">
                    <ChartComponent :chartData="chartData4" :options="chartOptions" :theme="theme">
                    </ChartComponent>
                </app-business-panel>
                <app-business-panel mode="card" class="box usage-status" title="公共空间利用临时商亭租赁情况"
                    v-loading="loading.chart4">
                    <ChartComponent :chartData="chartData5" :options="chartOptions" :theme="theme">
                    </ChartComponent>
                </app-business-panel>
                <app-business-panel mode="card" class="box usage-status" title="停车场租赁情况" v-loading="loading.chart5">
                    <Parking :parkingData="parkingData"></Parking>
                </app-business-panel>
                <app-business-panel mode="card" class="box usage-status" title="商铺租赁情况" v-loading="loading.chart6">
                    <ChartComponent :chartData="chartData6" :options="chartOptions" :theme="theme">
                    </ChartComponent>
                </app-business-panel>
            </div>
        </template>
    </app-form-page>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Propertyasset } from '@api/asset-panorama/property-asset';
import ChartComponent from './components/chart-component.vue';
import Parking from './components/parking.vue';
import dayjs from 'dayjs';

const name = 'asset-panorama-property-asset';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '物业'
        }
    ];
});

const loading = reactive({
    chart1: false,
    chart2: false,
    chart3: false,
    chart4: false,
    chart5: false,
    chart6: false,
});

const theme = ref('whiteTheme');

const _color = ['#0367FC', '#11b062', '#487690'];

const chartOptions = reactive({
    color: _color,
    option: {
        color: _color,
        legend: {
            data: ['可租赁面积', '已租赁面积', '出租率']
        },
        xAxis: {
            axisLabel: {
                rotate: 60,
                color: 'rgba(0, 0, 0, 0.45)'
            }
        },
        yAxis: [
            {
                name: '万平方',
                nameTextStyle: {
                    padding: [0, -10, 0, 0]
                },
                axisLabel: {
                    color: 'rgba(0, 0, 0, 0.45)'
                }
            },
            {
                name: '出租率',
                nameTextStyle: {
                    padding: [0, -20, 0, 0]
                },
                axisLabel: {
                    color: 'rgba(0, 0, 0, 0.45)',
                    formatter: '{value}%'
                },
                min: 0, // 设置最小值为0
                max: 100 // 设置最大值为100
                // interval: 10 // 设置刻度间隔为1
            }
        ],
        series: [
            {
                type: 'bar',
                yAxisIndex: 0,
                itemStyle: {
                    borderRadius: 20 // 设置圆角大小
                },
                barWidth: '10'
            },
            {
                type: 'bar',
                yAxisIndex: 0,
                itemStyle: {
                    borderRadius: 20 // 设置圆角大小
                },
                barWidth: '10'
            },
            {
                type: 'line',
                colorBy: 'series',
                yAxisIndex: 1,
                showSymbol: true, // 折线图中的圆点
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    type: 'dashed' // 线条类型，设置为虚线
                },
                smooth: 0.5
            }
        ],
        grid: {
            top: 40,
            bottom: 0,
            left: 0,
            right: 0
        }
    }
});
const yName = ref(['重量/kg', '通过率/%']);
const unit = ref(['万平方', '%']);
const chartData1 = reactive({
    dimensions: ['类目名称', '可租赁面积', '已租赁面积', '出租率'],
    unit: (['万平方', '%']),
    source: []
});

const chartData2 = reactive({
    dimensions: ['类目名称', '可租赁面积', '已租赁面积', '出租率'],
    unit: (['万平方', '%']),
    source: []
});

const chartData3 = reactive({
    dimensions: ['类目名称', '可租赁面积', '已租赁面积', '出租率'],
    unit: (['万平方', '%']),
    source: []
});

const chartData4 = reactive({
    dimensions: ['类目名称', '可租赁面积', '已租赁面积', '出租率'],
    unit: (['万平方', '%']),
    source: []
});

const chartData5 = reactive({
    dimensions: ['类目名称', '可租赁面积', '已租赁面积', '出租率'],
    unit: (['万平方', '%']),
    source: []
});

const chartData6 = reactive({
    dimensions: ['类目名称', '可租赁面积', '已租赁面积', '出租率'],
    unit: (['万平方', '%']),
    source: []
});

const fetchChartData2 = async () => {
    loading.chart1 = true; // 开始加载
    try {
        const response = await Propertyasset.getofficeBuilding();
        if (response.Data && Array.isArray(response.Data)) {
            const data = response.Data.map(item => {
                return {
                    类目名称: item.LineDesc, // 路线描述
                    可租赁面积: item.RentableArea, // 可租赁面积
                    已租赁面积: item.LeasedArea, // 已租赁面积
                    出租率: (item.RentalRate * 100).toFixed(2) // 出租率
                };
            });
            chartData2.source = data;
        }
    } catch (error) {
        console.error('获取写字楼租赁情况:', error);
    } finally {
        loading.chart1 = false; // 结束加载
    }
};

const fetchChartData3 = async () => {
    loading.chart2 = true; // 开始加载
    try {
        const response = await Propertyasset.getconcourseRetailProperty();
        if (response.Data && Array.isArray(response.Data)) {
            const data = response.Data.map(item => {
                return {
                    类目名称: item.LineDesc, // 路线描述
                    可租赁面积: item.RentableArea, // 可租赁面积
                    已租赁面积: item.LeasedArea, // 已租赁面积
                    出租率: (item.RentalRate * 100).toFixed(2) // 出租率
                };
            });
            chartData3.source = data;
        }
    } catch (error) {
        console.error('获取站厅商铺物业租赁情况:', error);
    } finally {
        loading.chart2 = false; // 结束加载
    }
};

const fetchChartData4 = async () => {
    loading.chart3 = true; // 开始加载
    try {
        const response = await Propertyasset.getundergroundCommercialStreet();
        if (response.Data && Array.isArray(response.Data)) {
            const data = response.Data.map(item => {
                return {
                    类目名称: item.LineDesc, // 路线描述
                    可租赁面积: item.RentableArea, // 可租赁面积
                    已租赁面积: item.LeasedArea, // 已租赁面积
                    出租率: (item.RentalRate * 100).toFixed(2) // 出租率
                };
            });
            chartData4.source = data;
        }
    } catch (error) {
        console.error('获取地下商业街租赁情况:', error);
    } finally {
        loading.chart3 = false; // 结束加载
    }
};

const fetchChartData5 = async () => {
    loading.chart4 = true; // 开始加载
    try {
        const response = await Propertyasset.getpublicSpaceRetailKiosk();
        if (response.Data && Array.isArray(response.Data)) {
            const data = response.Data.map(item => {
                return {
                    类目名称: item.LineDesc, // 路线描述
                    可租赁面积: item.RentableArea, // 可租赁面积
                    已租赁面积: item.LeasedArea, // 已租赁面积
                    出租率: (item.RentalRate * 100).toFixed(2) // 出租率
                };
            });
            chartData5.source = data;
        }
    } catch (error) {
        console.error('获取公共空间利用临时商亭租赁情况:', error);
    } finally {
        loading.chart4 = false; // 结束加载
    }
};

const fetchChartData6 = async () => {
    loading.chart6 = true; // 开始加载
    try {
        const response = await Propertyasset.getretailUnit();
        if (response.Data && Array.isArray(response.Data)) {
            const data = response.Data.map(item => {
                return {
                    类目名称: item.LineDesc, // 路线描述
                    可租赁面积: item.RentableArea, // 可租赁面积
                    已租赁面积: item.LeasedArea, // 已租赁面积
                    出租率: (item.RentalRate * 100).toFixed(2) // 出租率
                };
            });
            chartData6.source = data;
        }
    } catch (error) {
        console.error('获取商铺租赁情况:', error);
    } finally {
        loading.chart6 = false; // 结束加载
    }
};

const parkingData = ref({
});
const fetchParkingLotManageData = async () => {
    try {
        loading.chart5 = true; // 开始加载
        const response = await Propertyasset.getparkingLotManage();
        if (response.Data && Array.isArray(response.Data)) {
            parkingData.value = response.Data.map(item => ({
                name: item.OwnerShip,
                value: item.ParkingLotAmount
            }));
        }
    } catch (error) {
        console.error('获取停车场数量管理情况数据失败:', error);
    } finally {
        loading.chart5 = false;
    }
};

onActivated(() => {
    fetchChartData2();
    fetchChartData3();
    fetchChartData4();
    fetchChartData5();
    fetchChartData6();
    fetchParkingLotManageData();
});

</script>

<style lang="scss" scoped>
$page-name: asset-panorama-property-asset;

.#{$page-name} {
    .property-asset-layout {

        width: 100%;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-template-rows: 1fr 1fr;
        gap: var(--spacer-large);
    }

    :deep(.app-business-panel.card-panel) {
        height: calc(50vh - 48px);
        min-height: 270px;
    }
}
</style>
