const pureAxios = axios.create();

const useGlobalStore = defineStore('global-store', {
    state: () => ({
        globalConfigs: {}
    }),
    actions: {
        async getGlobalConfigs () {
            try {
                if (Reflect.ownKeys(this.globalConfigs).length) return this.globalConfigs;
                const name = `${import.meta.env.BASE_URL === '/' ? '' : import.meta.env.BASE_URL}/config.json`;
                const result = await pureAxios({
                    method: 'get',
                    url: name
                }).then((res) => res.data);
                this.globalConfigs = result;
                this.$patch({
                    globalConfigs: result
                });
                return result;
            } catch (error) {
                console.log(
                    '%c [PORTAL] getGlobalConfigs Caught Error',
                    'font-size:18px;color:red;font-weight:700;',
                    error
                );
            }
        }
    }
});

export default useGlobalStore;
