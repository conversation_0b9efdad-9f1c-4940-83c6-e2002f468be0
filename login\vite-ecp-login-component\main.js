import EcpLoginComponent from './App.vue';

import EcpLoginForm from './components/form-login.vue';
import EcpLoginPasswordMod from './components/form-password-mod.vue';
import EcpLoginSecondPassword from './components/second-login-password.vue';
import EcpLoginSecondFace from './components/second-login-face.vue';

import * as PKI from './components/pkiLogin.js';

import { LoginApi as EcpLoginApi } from './api';

import * as LoginUtils from './utils';
import * as EcpLoginConstants from './constants.dict';

const EcpLoginUtils = LoginUtils;

const exportModules = {
    LoginUtils,
    EcpLoginUtils,
    EcpLoginConstants,
    EcpLoginApi,
    PKI,

    EcpLoginComponent,

    EcpLoginForm,
    EcpLoginPasswordMod,
    EcpLoginSecondPassword,
    EcpLoginSecondFace
};

export default exportModules;

export {
    LoginUtils,
    EcpLoginUtils,
    EcpLoginConstants,
    EcpLoginApi,
    PKI,

    EcpLoginComponent,

    EcpLoginForm,
    EcpLoginPasswordMod,
    EcpLoginSecondPassword,
    EcpLoginSecondFace
};
