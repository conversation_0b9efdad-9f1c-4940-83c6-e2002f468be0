<template>
    <el-config-provider v-bind="ElementDefaultConfig">
        <div class="portal-content" ref="portalContentRef">
            <app-layout :name="title" @select="goto" :menu="menu" :menuProps="menuProps"
                :defaultPath="config.defaultPath">
                <template #content>
                    <transition name="fade-transform" mode="out-in">
                        <keep-alive :include="['RouterView', 'router-view', ...cachedViews]">
                            <div class="sub-wrapper" id="sub-wrapper" v-if="isSubApp"></div>

                            <router-view v-slot="{ Component }" v-else-if="initComplete">
                                <div class="sub-wrapper">
                                    <transition name="fade-transform" mode="out-in">
                                        <keep-alive :include="cachedViews">
                                            <component :is="Component" :key="routeKey" />
                                        </keep-alive>
                                    </transition>
                                </div>
                            </router-view>
                        </keep-alive>
                    </transition>
                </template>
            </app-layout>
        </div>
    </el-config-provider>
</template>

<script setup>
import { nextTick, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useTheme, ElementDefaultConfig, useHistoryApp, MicroUtils } from '@ecp/ecp-ui-plus';

import { MENU_PROPS } from '@constants/menu-config';
import useGlobalStore from '@store/global-config';
import usePermissionStore from '@store/permission';
import { ElMessage } from 'element-plus';

// 主应用组件参数
const props = defineProps({
    config: {
        type: Object,
        require: true
    },
    menu: {
        type: Array
    },
    menuProps: {
        type: Object,
        default: () => MENU_PROPS
    }
});

const globalStore = useGlobalStore();
const title = computed(() => {
    return globalStore?.globalConfigs?.IMPORT_CONFIGS?.title || PACKAGE_NAME;
});

const route = useRoute();
const router = useRouter();

// 微前端参数
const { keyOptions, microApp } = useHistoryApp();
const isSubApp = computed(() => {
    return !!(keyOptions?.name && keyOptions?.url);
});

const permissionStore = usePermissionStore();
// 获取缓存页面组件 name
const cachedViews = computed(() => (permissionStore.cachedViews || []).map((view) => view.name));

// 用 fullPath 做页面组件缓存标识
const routeKey = computed(() => {
    return route.fullPath;
});

// 路由式加载是否加载完成，加载完成后才能添加 router-view，否则可能会进主应用 router 的 404 页
const initComplete = ref(false);
watch(
    () => microApp,
    (newVal) => {
        nextTick(() => {
            if (!initComplete.value) {
                initComplete.value = !!newVal;
            }
        });
    },
    {
        // deep: true,
        immediate: true,
        flush: 'post'
    }
);

// 跳转处理
let opener = null;
const goto = (data) => {
    console.log('%c data', 'font-size:18px;color:purple;font-weight:700;', data);
    const force = data?.force === true;
    let href = data?.[props.menuProps?.route || 'route'] || data?.[props.menuProps?.url || 'url'];
    const startsWith = props.config?.startsWith || '/s-';

    // 系统管理直接打开公共服务新窗口
    if (data.FunTag === 'SYSTEM_MANAGEMENT') {
        href = MicroUtils.getUri(MicroUtils.getAppOriginHref(href, startsWith));
        window.open(href);
        return;
    }
    if (data.type === 'open') {
        if (opener && !opener.closed) {
            opener.close();
        }

        opener = window.open(href);
        return;
    } else if (data.type === 'reload') {
        // 刷掉当前页面
        window.location.href = href;
        return;
    }

    if (href.match(PACKAGE_NAME)) { // 当前应用通过 router 跳转
        href = MicroUtils.getUri(MicroUtils.getAppOriginHref(href, startsWith));

        // 目标路径上下文
        const targetName = MicroUtils.getAppNameFromHref(href, startsWith);
        // 当前链接上下文
        const currentName = MicroUtils.getAppNameFromHref(location.href, startsWith);

        // 可用于 router 解析的全路径
        const fullPath = MicroUtils.getAppFullPath(href);

        if (targetName !== currentName) {
            // url 上下文更改了，用 pushState 调整为 router 的上下文
            window.history.pushState({}, '', href);

            // 可能 vue-router 有点 bug，push/replace 带 query 的全路径会丢掉 query 参数并重定向，所以要用 router 把参数解析出来先
            const matchedRoute = router.resolve(fullPath);
            const matchedRouteQuery = matchedRoute?.query || {};

            // 因为已经调用过 pushState 了，页面组件用 replace 渲染就好了
            router.replace({
                path: matchedRoute.path,
                query: matchedRouteQuery,
                force: true
            });
        } else {
            // url 上下文没更改的，直接 push 就行
            router.push(force ? `/redirect${fullPath}` : fullPath);
        }
    } else { // 子应用通过 history 跳转
        window.history[force ? 'replaceState' : 'pushState']({
            force
        }, '', href);
    }
};

const portalContentRef = ref(null);
provide('$app', {
    getRootEl: () => portalContentRef.value
});
</script>

<style lang="scss" scoped>
.portal-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    &-tabs {
        flex: 0 0 auto;
    }
}

.sub-wrapper {
    width: 100%;
    height: 100%;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    transition-duration: var(--custom-transition-duration, 150ms);
    position: relative;
}

:deep(.sub-wrapper) {
    &>wujie-app {
        width: 100%;
        height: 100%;
        flex: 1 1 auto;
        display: flex;
        flex-direction: column;
    }
}
</style>
