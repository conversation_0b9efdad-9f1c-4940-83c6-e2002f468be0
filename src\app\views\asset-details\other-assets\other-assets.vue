<template>
    <app-form-page :class="[`${name}`]" v-bind="{ title }" showNav noBorder>
        <template #head-right>
            <!-- <span class="header-date">{{ dayjs().format('YYYY-MM-DD') }}</span> -->
        </template>
        <template #content>
            <el-card class="container">
                <!-- <el-radio-group v-model="current">
                <el-radio-button v-for="i in radioButtonList" :key="i.value" :value="i.value" :label="i.label" />
            </el-radio-group> -->
                <div class="tab-container">
                    <el-tabs class="app-detail-tabs tabs-only" v-model="current" style="margin-bottom: 20px;">
                        <el-tab-pane label="广告资源明细" name="tab1"></el-tab-pane>
                        <el-tab-pane label="通信资源明细" name="tab2"></el-tab-pane>
                        <el-tab-pane label="商铺明细" name="tab3"></el-tab-pane>
                        <el-tab-pane label="停车场明细" name="tab4"></el-tab-pane>
                    </el-tabs>
                </div>
                <div class="content-area">
                    <transition name="fade-transform" mode="out-in">
                        <component :is="_components"></component>
                    </transition>
                </div>
            </el-card>
        </template>
    </app-form-page>
</template>

<script setup>
import dayjs from 'dayjs';
import AdvertisementResourcesDetails from './components/advertisement-resources-details.vue';
import CommunicationResourcesDetails from './components/communication-resources-details.vue';
import ShopDetails from './components/shop-details.vue';
import ParkingLotDetails from './components/parking-lot-details.vue';

const name = 'asset-details-other-assets';

defineOptions({
    name
});

const title = computed(() => {
    return [
        {
            label: '其他经营资产'
        }
    ];
});
const radioButtonList = ref([
    {
        label: '广告资源明细',
        value: 'tab1'
    },
    {
        label: '通信资源明细',
        value: 'tab2'
    },
    {
        label: '商铺明细',
        value: 'tab3'
    },
    {
        label: '停车场明细',
        value: 'tab4'
    }
]);
onMounted(() => {

});

const current = ref('tab1');
const _components = computed(() => {
    return {
        tab1: AdvertisementResourcesDetails,
        tab2: CommunicationResourcesDetails,
        tab3: ShopDetails,
        tab4: ParkingLotDetails
    }[current.value];
});
</script>

<style lang="scss" scoped>
$page-name: asset-details-other-assets;

.#{$page-name} {
    :deep(.elp-select) {
        width: 120px;
    }

    .container {
        flex: 1 1 auto;
        display: flex;
        overflow: auto;
        height: 1vh;

        :deep {

            .elp-tabs__nav-prev,
            .elp-tabs__nav-next {
                height: 100%;
                display: flex;

                i {
                    margin: auto;
                }
            }
        }

        :deep(.elp-card__body) {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            padding-top: 0;
            width: 100%;
        }

        .content-area {
            flex: 1 1 auto;
            overflow: hidden;

            :deep {
                .header-toolbar {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;

                    &__selector {
                        display: flex;
                        gap: 8px;
                    }
                }
            }
        }
    }
}
</style>
