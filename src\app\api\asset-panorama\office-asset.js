import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';

export const OfficeAsset = {
    /**
     * 办公资产
     * @method getOfficeAssetBig 办公资产大类变化情况
     */
    // getOfficeAssetBig (params) {
    //     return request.get(`${AssetsAnalysisService}/getOfficeAssetBig`, { params }).then(
    //         ({ data }) => data
    //     );
    // }


    /**
     * 办公资产
     * @method getOfficeAssetBig 办公资产大类变化情况
     */
    getOfficeAssetBig(params) {
        return request({
            url: `${AssetsAnalysisService}/getOfficeAssetBig`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    },

   /**
     * 办公资产中类变化情况的下拉框数据
     */
   getOfficeAssetBigQueryData(params) {
    return request({
        url: `${AssetsAnalysisService}/GetOfficeAssetBigQueryData`,
        method: "get",
        params: params
    }).then(({ data }) => data);
},

    /**
     * 办公资产中类变化情况
     */
    getOfficeAssetMedium(params) {
        return request({
            url: `${AssetsAnalysisService}/GetOfficeAssetMedium`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    },

       
     /**
     * 办公资产归属部门变化情况的下拉框数据
     */
     getOfficeAssetUnitQueryData(params) {
        return request({
            url: `${AssetsAnalysisService}/GetOfficeAssetUnitQueryData`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    },

    
     /**
     * 办公资产归属单位变化情况
     */
     getOfficeAssetUnit(params) {
        return request({
            url: `${AssetsAnalysisService}/GetOfficeAssetUnit`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    },

      /**
     * 办公资产归属部门变化情况
     */
      getOfficeAssetUnitDept(params) {
        return request({
            url: `${AssetsAnalysisService}/GetOfficeAssetUnitDept`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    },

     /**
     * 办公资产中类增长top10
     */
     getOfficeAssetMiddleTop10(params) {
        return request({
            url: `${AssetsAnalysisService}/GetOfficeAssetMiddleTop10`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    },

     /**
     * 办公资产归属部门增长top10
     */
     getOfficeAssetUnitDeptTop10(params) {
        return request({
            url: `${AssetsAnalysisService}/GetOfficeAssetUnitDeptTop10`,
            method: "get",
            params: params
        }).then(({ data }) => data);
    }




    
    

};
