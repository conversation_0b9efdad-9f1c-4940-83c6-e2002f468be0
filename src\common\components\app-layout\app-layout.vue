<template>
    <el-container class="app-layout">
        <LayoutSide class="app-layout-aside" v-bind="{ name, menu, menuProps }" @select="onSelect"
            v-if="!isSub && direction === 'vertical'" />

        <el-container class="app-layout-container">
            <el-main class="app-layout-container-main">
                <div class="app-layout-content" id="app-layout-content">
                    <slot name="content"></slot>
                </div>
            </el-main>
        </el-container>
    </el-container>
</template>

<script setup>
import { MENU_PROPS } from '@constants/menu-config';

import LayoutSide from './components/layout-side.vue';

defineComponent({
    name: 'app-layout'
});

const props = defineProps({
    name: {
        type: String
    },
    menu: {
        type: Array,
        default: () => ([])
    },
    menuProps: {
        type: Object,
        default: () => MENU_PROPS
    },
    direction: {
        type: String,
        default: 'vertical'
    }
});

const emits = defineEmits(['select']);

const usePortal = USE_PORTAL;

const isSub = window.__POWERED_BY_WUJIE__ || window.__POWERED_BY_QIANKUN__;

const onSelect = (options) => {
    const { menuItem, ...res } = options;
    emits('select', menuItem);
};
</script>

<style lang="scss" scoped>
.app-layout {
    width: 100%;
    height: 100%;

    &-header {
        --elp-header-padding: 0;
        --elp-header-height: auto;
    }

    &-header {
        width: 100%;
        background-color: var(--background-color-dialog);

        &.inner {
            height: 48px;
            // border-bottom: var(--border-base);
            // border-bottom-color: var(--border-color-light);

            .app-layout-header--tabs {
                position: relative;

                &::after {
                    content: '';
                    height: 1px;
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    background-color: var(--border-color-light);
                }
            }
        }

        &--nav {
            width: 100%;
            height: 64px;
            padding: 0 8px 0 20px;
        }
    }

    &-container {
        width: 100%;
        height: auto;
        flex: 1 1 auto;
        overflow: hidden;

        &-main {
            --elp-main-padding: 0;
            display: flex;
            flex-direction: column;
        }
    }

    &-content {
        width: 100%;
        height: 100%;
        flex: 1 1 auto;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: relative;
    }
}
</style>
