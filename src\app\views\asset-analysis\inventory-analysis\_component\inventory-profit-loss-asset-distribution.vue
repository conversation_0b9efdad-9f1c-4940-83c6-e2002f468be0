<script setup>
import * as Api from '@api/index';
import { cloneDeep } from 'lodash-es';
import * as echarts from 'echarts';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    }
});
const loading = reactive({
    surplus: false,
    loss: false
});
const empty = reactive({
    surplus: false,
    loss: false
});

let chartsData = {};

const blueColor = Array.from({ length: 5 }, (_, i) => `rgba(3,103,252,${(1 - i * 0.2).toFixed(1)})`);
const greenColor = Array.from({ length: 5 }, (_, i) => `rgba(17,176,98,${(1 - i * 0.2).toFixed(1)})`);
const surplusRef = ref(null);
let surplusChart = null;
const lossRef = ref(null);
let lossChart = null;

const tooltipFrame = (html, title, theme = 'light') => {
    return `
        <div class="ecp-chart-tooltip-wrapper ${theme === 'light' ? 'is-white' : 'is-black'}">
             <div class="ecp-chart-tooltip-head">${title}</div>
             ${html}
        </div>
        `;
};
const tooltipItem = (params, name, value, unit) => {
    return `
        <div class="ecp-chart-tooltip-item">
            <span class="ecp-chart-tooltip-label" style="--color: ${params.color}">${name}</span>
            <span class="ecp-chart-tooltip-value">
                <i class="ecp-chart-tooltip-value-num">${value || params.value}</i>
                <i class="ecp-chart-tooltip-value-unit">${unit || ''}</i>
            </span>
       </div>
   `;
};

const commonOptions = {
    title: {
        left: '64%',
        top: '40%',
        textAlign: 'center',
        textStyle: {
            fontFamily: 'D-DIN-BOLD',
            fontSize: 32,
            color: '#1D2137'
        },
        subtextStyle: {
            fontFamily: '苹方-简',
            fontSize: 14,
            color: 'rgba(29,33,55,0.65)'
        },
        padding: [0, 0, 0, 0],
        itemGap: 2
    },
    tooltip: {
        textStyle: {
            fontSize: 10
        },
        extraCssText:
                    'padding: 0; border-radius: 0.4em; border: 0; overflow: hidden;',
        backgroundColor: '#fff',
        formatter (params) {
            const key = params.seriesName.slice(0, 2);
            const keyMap = { 盘盈: 'surplus', 盘亏: 'loss' };
            let html = tooltipItem(params, '数量');
            html += tooltipItem(params, '占比', chartsData[`${keyMap[key]}-${params.name}`].Percent.slice(0, -1), '%');
            return tooltipFrame(html, params.name);
        }
    },
    legend: {
        orient: 'vertical',
        x: 'left',
        y: '10%',
        icon: 'circle',
        itemGap: 20,
        textStyle: {
            color: 'rgba(0, 0, 0, 0.45)',
            fontSize: 12,
            fontFamily: '苹方-简',
            padding: [25, 0, 0, 0],
            rich: {
                value: {
                    color: 'rgba(0, 0, 0, 0.85)',
                    padding: [5, 0, 0, 0],
                    fontWeight: 'bold',
                    fontSize: 18,
                    fontFamily: 'D-DIN-BOLD'
                }
            }
        }
    },
    series: []
};

const onResize = () => {
    nextTick(() => {
        if (surplusChart) surplusChart.resize();
        if (lossChart) lossChart.resize();
    });
};

const initCharts = () => {
    if (!props.data?.checkMonth && !props.data?.groupType) return;
    if (surplusRef.value && !surplusChart) {
        surplusChart = echarts.init(surplusRef.value);
    }
    if (lossRef.value && !lossChart) {
        lossChart = echarts.init(lossRef.value);
    }
    chartsData = {};
    getCheckSurplusData();
    getCheckLossData();
};

const isEmpty = (data) => Array.isArray(data) && data.length === 0;

// type : 1.surplus 2.loss
const getOptions = (type, data) => {
    const text = { surplus: '盘盈', loss: '盘亏' };
    const color = { surplus: blueColor, loss: greenColor };
    const Options = { ...cloneDeep(commonOptions), color: data.length === 2 ? [color[type][1], color[type][2]] : color[type] };
    let value = 0;
    Options.series = [
        {
            name: `${text[type]}资产分布`,
            type: 'pie',
            radius: ['45%', '65%'],
            center: ['65%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderRadius: 0,
                borderColor: '#fff',
                borderWidth: 5
            },
            label: {
                show: false
            },
            labelLine: {
                show: false
            },
            data: data.map(item => {
                value += item.DataCount;
                chartsData[`${type}-${item.Name}`] = { ...item, Percent: Math.round(item.Percent * 100) + '%' };
                return { name: item.Name, value: item.DataCount };
            })
        }
    ];
    Options.title.subtext = text[type];
    Options.title.text = value.toLocaleString();
    Options.legend.formatter = (name) => `${name.length > 6 ? name.slice(0, 6) + '...' : name}\n{value|${chartsData[`${type}-${name}`]?.Percent || '0%'}}`;
    return Options;
};

const getCheckSurplusData = async () => {
    try {
        loading.surplus = true;
        const response = await Api.AssetAnalysis.getCheckSurplusData(props.data);
        empty.surplus = isEmpty(response.Data);
        const options = getOptions('surplus', response.Data);
        surplusChart.setOption(options, true);
    } catch (e) {
        console.log('%c getCheckSurplusData error', 'color: red', e);
        empty.surplus = true;
    } finally {
        loading.surplus = false;
    }
};

const getCheckLossData = async () => {
    try {
        loading.loss = true;
        const response = await Api.AssetAnalysis.getCheckLossData(props.data);
        empty.loss = isEmpty(response.Data);
        const options = getOptions('loss', response.Data);
        lossChart.setOption(options, true);
    } catch (e) {
        console.log('%c getCheckLossData error', 'color: red', e);
        empty.loss = true;
    } finally {
        loading.loss = false;
    }
};

watch(() => props.data, initCharts, { immediate: true, deep: true });
</script>

<template>
    <div class="inventory-profit-loss-asset-distribution" v-resize="onResize">
        <div class="left-charts" v-show="!empty.surplus" v-resize="onResize" ref="surplusRef"
            v-loading="loading.surplus"></div>
        <ecp-empty v-show="empty.surplus"></ecp-empty>
        <div class="dividing_line"></div>
        <div class="right-charts" v-show="!empty.loss" v-resize="onResize" ref="lossRef" v-loading="loading.loss"></div>
        <ecp-empty v-show="empty.loss"></ecp-empty>
    </div>
</template>

<style scoped lang="scss">
.inventory-profit-loss-asset-distribution {
    width: 100%;
    height: 100%;
    display: flex;

    .dividing_line {
        height: 100%;
        width: 1px;
        background: #E8EAF0;
        margin: 0 40px;
    }

    .left-charts,
    .right-charts,
    .ecpp-empty {
        flex: 1;
        width: 100%;
        height: 100%;
    }
}
</style>
