<script setup>
import * as Api from '@api/index';

const _color = ['#0367FC', '#11B062', '#487690'];
const loading = ref(false);
const value = reactive({
    盘盈: 0,
    盘亏: 0
});

const empty = ref(false);
const data = reactive({
    dimensions: [],
    source: []
});
const option = {
    color: _color,
    legend: {
        show: false
    },
    xAxis: {
        axisLabel: {
            color: 'rgba(29,33,55,0.45)'
        }
    },
    dataZoom: [
        {
            type: 'inside'
        }
    ]
};
const EcpChartBaseLineRef = ref(null);
const EcpChartBaseLineInstance = computed(() => EcpChartBaseLineRef.value?.getChartInstance());

const getCheckTrendData = async () => {
    try {
        loading.value = true;
        value.盘盈 = 0;
        value.盘亏 = 0;
        const response = await Api.AssetAnalysis.getCheckTrendData();
        const { LegendData, SeriesData, XAxis } = response.Data;
        empty.value = false;
        data.dimensions = ['month', ...LegendData];
        data.source = XAxis.map((item, index) => {
            const data = { month: item };
            for (const seriesDatum of SeriesData) {
                const { Data, Name } = seriesDatum;
                data[Name] = Data[index];
                value[Name] += Data[index];
            }
            return data;
        });
        option.series = SeriesData.map(item => ({
            type: 'line',
            name: item.Name,
            symbolSize: 8,
            showSymbol: true,
            lineStyle: {
                width: 4
            },
            itemStyle: {
                borderColor: '#fff',
                borderWidth: 2
            }
        }));
    } catch (error) {
        console.log('%c getCheckTrendData error', 'color: red', error);
        empty.value = true;
    } finally {
        loading.value = false;
    }
};

const clickLegendEvent = (name) => {
    const Instance = EcpChartBaseLineInstance.value;
    if (Instance) Instance.dispatchAction({ type: 'legendToggleSelect', name });
};

onMounted(() => getCheckTrendData());
</script>

<template>
    <div class="inventory-profit-loss-trend" v-loading="loading">
        <div class="legend-container">
            <div class="legend-container-surplus" @click="clickLegendEvent('盘盈')">
                <img class="legend-container-surplus__icon" alt="" src="../../../../../assets/trend/surplus.png">
                <div class="legend-container-surplus-content">
                    <div class="legend-container-surplus-content__label">盘盈</div>
                    <div class="legend-container-surplus-content__value">{{ value.盘盈.toLocaleString() }}</div>
                </div>
            </div>
            <div class="legend-container-loss" @click="clickLegendEvent('盘亏')">
                <img class="legend-container-loss__icon" alt="" src="../../../../../assets/trend/loss.png">
                <div class="legend-container-loss-content">
                    <div class="legend-container-loss-content__label">盘亏</div>
                    <div class="legend-container-loss-content__value">{{ value.盘亏.toLocaleString() }}</div>
                </div>
            </div>
        </div>
        <ecp-chart-base-line v-if="!empty" ref="EcpChartBaseLineRef" theme="whiteTheme" :option="option" :data="data"
            class="inventory-profit-loss-trend__charts" />
        <ecp-empty v-else></ecp-empty>
    </div>
</template>

<style scoped lang="scss">
.inventory-profit-loss-trend {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .legend-container {
        display: flex;
        padding: 1px var(--spacer);
        gap: var(--spacer-extra-large);
        align-items: center;

        &-surplus,
        &-loss {
            display: flex;
            cursor: pointer;
            gap: var(--spacer-large-3);

            &__icon {
                width: 40px;
                height: 40px;
                margin: auto 0;
            }

            &-content {
                user-select: none;

                &__label {
                    font-size: var(--font-size-small);
                    color: var(--color-text-secondary);
                }

                &__value {
                    font-family: D-DIN-BOLD;
                    font-size: var(--font-size-larger-2);
                    font-weight: bold;
                    line-height: 30px;
                    letter-spacing: 0.8px;
                    color: var(--color-black-opacity-8-5);
                }
            }
        }
    }

    &__charts {
        flex: 1;
    }
}
</style>
