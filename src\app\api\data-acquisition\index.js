import request from '@api/request';
import { AssetsAnalysisService } from '@api/prefix.config';
import qs from 'qs';

const prefix = `${AssetsAnalysisService}/uploadRecord`;
const headers = {
    'content-type': 'application/x-www-form-urlencoded'
};

export const DataAcquisition = {
    /**
     * 数据采集
     * @method downloadErrorDetail 数据采集-下载错误详情
     */
    downloadErrorDetail (params) {
        return request.post(`${prefix}/downloadErrorDetail`, qs.stringify(params), {
            headers,
            responseType: 'blob'
        });
    },
    /**
     * 数据采集
     * @method downloadTemplate 数据采集-下载数据表导入模板
     */
    downloadTemplate (params) {
        return request.post(`${prefix}/downloadTemplate`, qs.stringify(params), {
            headers,
            responseType: 'blob'
        });
    },
    /**
     * 数据采集
     * @method getUploadRecordList 数据采集-历史上传记录
     */
    getUploadRecordList (params) {
        return request.post(`${prefix}/getUploadRecordList`, params).then(({ data }) => data);
    },
    /**
     * 数据采集
     * @method getUploadTypeList 数据采集-上传数据表类型
     */
    getUploadTypeList () {
        return request.post(`${prefix}/getUploadTypeList`).then(({ data }) => data);
    }
};
