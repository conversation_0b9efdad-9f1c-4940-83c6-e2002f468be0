export const MENU_PROPS = {
    id: 'Id',
    label: 'Text',
    route: 'Target', // 'Value.Target',
    url: 'Url',
    symbol: 'Symbol',
    children: 'ChildN<PERSON>',
    funTag: 'FunTag'
};

export const MENU_ICON = {
    ASSET_FULL_VIEW: {
        iconClass: 'iconfont',
        icon: 'ecp-icon-homepage'
    },
    ASSET_ANALYSIS: {
        iconClass: 'iconfont',
        icon: 'ecp-icon-frequency-analysis'
    },
    ASSET_DETAILS: {
        iconClass: 'iconfont',
        icon: 'ecp-icon-document-search'
    },
    DATA_COLLECTION: {
        iconClass: 'iconfont',
        icon: 'ecp-icon-audio'
    },
    SYSTEM_MANAGEMENT: {
        iconClass: 'iconfont',
        icon: 'ecp-icon-setting'
    }

};
