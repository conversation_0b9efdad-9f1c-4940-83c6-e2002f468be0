<template>
    <app-business-panel mode="card" v-loading="loading.chart2" title="各类资产状态分析">
        <template v-if="chartData2?.source?.length">
            <ecp-chart-bar-line theme="whiteTheme" :unit="['台', '%']" :data="chartData2" :option="chartData2.option"
                class="demo-container" />
        </template>
        <template v-else>
            <ecp-empty />
        </template>
    </app-business-panel>
</template>

<script setup>
import { defineProps, reactive, watch, nextTick, ref, shallowRef, onActivated } from 'vue';
import { LineAssetAnalysis } from '@api/asset-analysis/line-asset-analysis';
import { formatXAxis } from '@utils/format';

const props = defineProps({
    selectedLine: String, // 选择的线路
    isInitialLoad: Boolean
});

const loading = reactive({
    chart2: false
});

onActivated(() => {
    fetchAssetAnalyseData();
});

const _color = ['#0367FC', '#487690'];

const chartData2 = ref({
    dimensions: ['类目名称', '资产总数量', '报废率'],
    source: [],
    option: {
        color: _color,
        xAxis: {
            axisLabel: {
                rotate: 60,
                interval: 'auto', // 自动调整标签间隔
                color: 'rgba(0, 0, 0, 0.45)' // 设置标签颜色
            }
        },
        yAxis: [
            {
                type: 'value', // 左边的 yAxis 保持数值形式
                name: '单位：台',
                nameTextStyle: {
                    padding: [0, -20, 0, 0]
                },
                axisLabel: {
                    color: 'rgba(0, 0, 0, 0.45)'
                }
            },
            {
                type: 'value', // 右边的 yAxis 用于百分比
                name: '报废率%',
                nameTextStyle: {
                    padding: [0, -20, 0, 0]
                },
                axisLabel: {
                    color: 'rgba(0, 0, 0, 0.45)',
                    formatter: function (value) {
                        return Math.round(value) + '%'; // 格式化为百分比形式
                    }
                }
            }
        ],
        series: [
            {
                type: 'bar',
                yAxisIndex: 0,
                itemStyle: {
                    borderRadius: 20 // 设置圆角大小
                },
                barWidth: 10
            },
            {
                type: 'line',
                yAxisIndex: 1,
                showSymbol: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    type: 'dashed' // 线条类型，设置为虚线
                },
                smooth: 0.5
            }
        ],
        dataZoom: [
            {
                type: 'inside',
                xAxisIndex: [0],
                start: 0,
                end: 100,
                zoomOnMouseWheel: true,
                moveOnMouseMove: true
            }
        ],
        grid: {
            bottom: 0,
            top:40,
            left: 20,
            right: 0
        },
        legend: {
            top:10,
        }
    }
});

const fetchAssetAnalyseData = async () => {
    if (props.isInitialLoad) {
        return;
    }
    loading.chart2 = true;
    const params = {};
    if (props.selectedLine.line !== 'all') {
        params.udLine = props.selectedLine.line;
    }
    try {
        const response = await LineAssetAnalysis.getAssetAnalyse(params);
        if (response && response.Data && response.Data.length) {
            const source = response.Data.map((item) => ({
                类目名称: item.AssetsName,
                资产总数量: item.AssetsAmount,
                // 报废率: (item.AssetsUesLessRate * 100).toFixed(0)
                报废率: Math.round(item.AssetsUesLessRate * 100)
            }));

            // 计算报废率的最大值和最小值
            const maxScrapRate = Math.max(...source.map(item => item.报废率));
            const minScrapRate = Math.min(...source.map(item => item.报废率));

            // 动态设置右边的 yAxis 的 min, max 和 interval
            if (minScrapRate === maxScrapRate && minScrapRate === 0) {
                chartData2.value.option.yAxis[1].min = 0;
                chartData2.value.option.yAxis[1].max = 5;
                chartData2.value.option.yAxis[1].interval = 1;
            } else {
                chartData2.value.option.yAxis[1].min = undefined;
                chartData2.value.option.yAxis[1].max = undefined;
                chartData2.value.option.yAxis[1].interval = undefined;
            }

            chartData2.value.source = source;
            // 更新图表的 xAxis 配置
            chartData2.value.option.xAxis.axisLabel.formatter = (value) => formatXAxis(value,6);
        } 
    } catch (error) {
        console.error('获取第二个柱线图数据失败:', error);
    } finally {
        loading.chart2 = false;
    }
};

// 监听属性变化
watch(() => props.selectedLine.line, (newLine) => {
    fetchAssetAnalyseData();
});


</script>

<style scoped lang="scss"></style>
