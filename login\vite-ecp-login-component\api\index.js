import $axios from 'axios';

const AxiosService = $axios.create();
AxiosService.defaults.withCredentials = true; // 让ajax携带cookie

export const LoginApi = {
    /**
     * @method getLoginConfigGlobal 获取公服登录配置
     * @returns
     */
    getLoginConfigGlobal () {
        return AxiosService.get('/sysmanager/api/sysLoginConfig/getSysLoginConfig').then(
            ({ data }) => data
        );
    },
    /**
     * @method ssoCheck sso 校验
     * @returns
     */
    ssoCheck (params, options) {
        return AxiosService.post('/sso/check', params, options).then(({ data }) => data);
    },
    /**
     * @method ssoDoLogin sso 账号密码登录
     * @returns
     */
    ssoDoLogin (params, options) {
        return AxiosService.post('/sso/doLogin', params, options).then(({ data }) => data);
    },
    /**
     * @method modifyPassword 重置/修改密码 (put 请求)
     * @returns
     */
    modifyPassword (params) {
        return AxiosService.put('/sso/sysUser/modifyPassword', params).then(
            ({ data }) => data
        );
    },
    /**
     * @method modifyPasswordPost 重置/修改密码 (post 请求)
     * @returns
     */
    modifyPasswordPost (params) {
        return AxiosService.post('/sso/sysUser/modifyPassword', params).then(
            ({ data }) => data
        );
    },
    /**
     * @method pkiRandom 获取 PKI 认证随机值
     * @returns
     */
    pkiRandom (params) {
        return AxiosService.get('/sso/pkiRandom', { params }).then(({ data }) => data);
    },
    /**
     * @method pkiLogin PKI 登录
     * @returns
     */
    pkiLogin (params, options) {
        return AxiosService.post('/sso/pkiLogin', params, options).then(({ data }) => data);
    },
    /**
     * @method faceLogin 人脸认证
     * @returns
     */
    faceLogin (params, options) {
        return AxiosService.post('/sso/faceLogin', params, options).then(
            ({ data }) => data
        );
    }
};
