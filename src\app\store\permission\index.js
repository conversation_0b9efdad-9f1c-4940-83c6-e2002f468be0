import SystemService from '@api/system-service';
import menuConfig from '@constants/mock/menu-config';

import useGlobalStore from '@store/global-config';
import useUserStore from '@store/user';

// 递归生成导航菜单
function reformatMenu (menu, leafOnly, ignoreCase) {
    return (
        (Array.isArray(menu) &&
            menu.reduce((prev, menuItem) => {
                const { Id, Text, Value, ChildNodes } = menuItem;
                let result = prev;
                if (ignoreCase) {
                    const ignoreMatched = Object.keys(ignoreCase).every(key => {
                        const targetMenuItem = (key || '')
                            .split('.')
                            .reduce(
                                (prev, curr) =>
                                    prev === null || prev === undefined
                                        ? prev
                                        : prev[curr],
                                menuItem
                            );
                        return typeof targetMenuItem === 'string'
                            ? targetMenuItem.match(ignoreCase[key])
                            : targetMenuItem === ignoreCase[key];
                    });
                    if (ignoreMatched) {
                        return result;
                    }
                }
                if (
                    (Value.Target &&
                        (!leafOnly || Value.Target.match('/'))) ||
                    Value.Url
                ) {
                    result.push({
                        Id,
                        Text,
                        Value,
                        Url: Value.Url || null,
                        Target: Value.Target || null,
                        FunTag: Value.FunTag || null,
                        ChildNodes: reformatMenu(
                            ChildNodes,
                            leafOnly,
                            ignoreCase
                        )
                    });
                } else if (leafOnly && ChildNodes) {
                    result = [
                        ...result,
                        ...reformatMenu(ChildNodes, leafOnly, ignoreCase)
                    ];
                }
                return result;
            }, [])) ||
        []
    );
}

// 深度搜索权限树
function findDeep (menu, key, value) {
    if (menu && menu instanceof Array && menu.length > 0) {
        for (const menuItem of menu) {
            if (key) {
                const targetMenuItem = (key || '')
                    .split('.')
                    .reduce(
                        (prev, curr) =>
                            prev === null || prev === undefined
                                ? prev
                                : prev[curr],
                        menuItem
                    );
                if (targetMenuItem === value) {
                    return menuItem;
                }
            }
            if (menuItem.ChildNodes) {
                const childItem = findDeep(menuItem.ChildNodes, key, value);
                if (childItem) {
                    return childItem;
                }
            }
        }
    }
    return null;
}

/**
 * @method flattenDeep 深度展开权限树
 * @param { Array } menu 权限树
 * @param { string } key 需要扁平化的字段 不可为空
 * @param { string } filterKey 权限筛选字段，由于菜单列表已经是默认返回用户所能看的全部权限，所以默认与key一个值
 * @returns { Array }
 */
function flattenDeep (menu, key, filterKey = key) {
    return (
        (Array.isArray(menu) &&
            key &&
            menu.reduce(
                (prev, curr) => [
                    ...prev,
                    ...(curr[key] !== undefined &&
                    curr[key] !== null &&
                    curr[filterKey]
                        ? [curr[key]]
                        : []),
                    ...flattenDeep(curr.ChildNodes, key, filterKey)
                ],
                []
            )) ||
        []
    );
}

const usePermissionStore = defineStore('permission-store', {
    state: () => {
        return {
            navMenu: [],
            systemMenu: [],
            funTagList: [],
            platformBtnList: [],
            urlList: [],

            // 缓存视图
            cachedViews: []
        };
    },
    actions: {
        addCachedView (view) {
            return new Promise(resolve => {
                if (!Array.isArray(this.cachedViews)) {
                    return resolve();
                }
                if (this.cachedViews.some(
                    (cachedView) => cachedView.name === view.name
                )) {
                    return resolve();
                };
                if (view && !view.meta?.noCache) {
                    // 将需要缓存的视图添加到缓存视图中
                    this.cachedViews.push(view);

                    // 将驼峰命名转换为短横线命名也加到缓存视图中，以做兼容处理
                    this.cachedViews.push({
                        ...view,
                        name: view.name.replace(/([A-Z])/g, '-$1').replace(/^-/, '').toLowerCase()
                    });
                }
                resolve();
            });
        },
        delCachedView (view) {
            return new Promise(resolve => {
                if (!Array.isArray(this.cachedViews)) {
                    return resolve();
                }

                // 将指定页面从缓存视图中删除
                const index = this.cachedViews.findIndex(
                    (cachedView) => cachedView.name === view.name
                );
                index > -1 && this.cachedViews.splice(index, 1);

                // 将驼峰命名转换为短横线命名的页面也从缓存视图中删除
                const kababName = view.name.replace(/([A-Z])/g, '-$1').replace(/^-/, '').toLowerCase();
                const kababIndex = this.cachedViews.findIndex((cachedView) => cachedView.name === kababName);
                kababIndex > -1 && this.cachedViews.splice(kababIndex, 1);
                resolve();
            });
        },

        // 获取权限树
        async getPermission () {
            // /**
            //  * 如不使用公服管理菜单权限，请使用这段 Start
            //  */
            // const menuList = []; // 获取菜单

            // const btnList = []; // 获取按钮权限
            // /**
            //  * 如不使用公服管理菜单权限，请使用这段 End
            //  */

            /**
             * 如使用公服管理菜单权限，请使用这段 Start
             */
            const globalStore = useGlobalStore();

            const { globalConfigs } = globalStore;
            const menuId = globalConfigs?.IMPORT_CONFIGS?.menuId || ''; // 菜单ID
            const buttonId = globalConfigs?.IMPORT_CONFIGS?.buttonId || ''; // 按钮ID

            const userStore = useUserStore();

            const menuList = await SystemService.getUserMenuTree({
                UserCode: userStore.userInfo.UserCode
            }); // 获取菜单

            const btnList = await SystemService.getBtnAuth({
                UserCode: userStore.userInfo.UserCode
            }); // 获取按钮权限
            /**
             * 如使用公服管理菜单权限，请使用这段 End
             */

            let params = [menuList, 'Id', menuId]; /* 匹配标识 单个标识 String */
            const navMenuList =
                findDeep.apply(this, params)?.ChildNodes || []; // 业务菜单权限

            const platformBtnList = findDeep(btnList, 'Id', buttonId)?.ChildNodes || []; // 平台按钮权限

            params = [
                menuList,
                'Value.FunTag',
                'SYSTEM_SERVICE'
            ]; /* 匹配标识 单个标识 String */
            const systemMenuList =
                findDeep.apply(this, params)?.ChildNodes || []; // 用户管理后台权限

            let navMenu =
                reformatMenu(navMenuList, false, {
                    'Value.FunTag': 'SYSTEM_SERVICE' // 排除的标识 Regx String
                }) || []; // 根据排除的标识去除排除的菜单
            const systemMenu = reformatMenu(systemMenuList) || []; // 根据排除的标识去除排除的菜单

            // 开发环境例子
            if (process.env.NODE_ENV === 'development') {
                navMenu = [...(menuConfig?.menu || []), ...(navMenu || [])];
            }

            const funTagList = [
                ...new Set([
                    ...flattenDeep(navMenu, 'FunTag'),
                    ...flattenDeep(systemMenu, 'FunTag'),
                    ...flattenDeep(btnList, 'FunId', 'Auth')
                ])
            ];

            const urlList = [
                ...new Set([
                    ...flattenDeep(navMenu, 'Target'),
                    ...flattenDeep(systemMenu, 'Target'),
                    ...flattenDeep(btnList, 'Url', 'Auth')
                ])
            ];

            this.navMenu = navMenu;
            this.systemMenu = systemMenu;
            this.funTagList = funTagList;
            this.platformBtnList = platformBtnList;
            this.urlList = urlList;
            // commit('SET_NAVMENU', navMenu);
            // commit('SET_SYSTEMMENU', systemMenu);
            // commit('SET_FUNTAGLIST', funTagList);
            // commit('SET_URLLIST', urlList);
            return navMenu;
        }
    }
});

export default usePermissionStore;
