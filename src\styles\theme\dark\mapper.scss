/* Color Related Variables */
@use "sass:map";

// types
$types: primary, success, warning, danger, error, info;

// Color
$theme-colors: (
    "white": #ffffff,
    "black": #000000,
    "primary": (
        "base": #1890ff,
    ),
    "success": (
        "base": #49aa19,
    ),
    "warning": (
        "base": #d89614,
    ),
    "danger": (
        "base": #a61d24,
    ),
    "error": (
        "base": #a61d24,
    ),
    "info": (
        "base": rgba(#ffffff, 45%),
    ),
);

$text-color: (
    "light": var(--color-white),
    "primary": var(--color-white-opacity-8-5),
    "regular": var(--color-white-opacity-6-5),
    "secondary": var(--color-white-opacity-4-5),
    "placeholder": var(--color-white-opacity-3),
    "mark": var(--color-white-opacity-1-5),
    "shadow": var(--color-white-opacity-0-9),
    "light-darken": var(--color-white-opacity-6-5),
    "disabled": var(--color-white-opacity-3),
);
$text-color-reverse: (
    "light-reverse": var(--color-black),
    "primary-reverse": var(--color-black-opacity-8-5),
    "regular-reverse": var(--color-black-opacity-6-5),
    "secondary-reverse": var(--color-black-opacity-4-5),
    "placeholder-reverse": var(--color-black-opacity-2-5),
    "mark-reverse": var(--color-black-opacity-1-5),
    "shadow-reverse": var(--color-black-opacity-0-9),
    "light-darken-reverse": var(--color-black-opacity-5),
    "disabled-reverse": var(--color-black-opacity-2-5),
);

$border-color: (
    "": var(--color-white-opacity-2),
    "base": var(--color-white-opacity-2),
    "light": rgba(var(--color-white-rgb), 12%),
    "lighter": var(--color-white-opacity-0-4),
    "extra-light": var(--color-white-opacity-0-2),
    "dark": rgba(var(--color-white-rgb), 14%),
    "darker": var(--color-white-opacity-2),
    "none": var(--color-white-opacity-0),
    "dash": rgba(var(--color-white-rgb), 12%),
    "disabled": var(--color-white-opacity-2),
    "hover": var(--color-primary),
);

$fill-color: (
    "": var(--color-white-opacity-1),
    "base": var(--color-white),
    "light": #39455e,
    "lighter": var(--color-white-opacity-0-4),
    "extra-light": var(--color-white-opacity-0-2),
    "dark": var(--color-white-opacity-1-5),
    "darker": var(--color-white-opacity-2),
    "blank": transparent,
    "primary": var(--color-black-opacity-6-5),
    "secondary": var(--color-black-opacity-4-5),
    "placeholder": var(--color-black-opacity-2-5),
);

$bg-color: (
    "": #0A1938,
    // "page": var(--color-primary-dark-8),
    "page": #030625,
    "base": var(--color-white-opacity-0-8),
    "light": var(--color-white-opacity-0-4),
    "primary": #0A1938,
    "dialog": var(--background-color-primary),
    "overlay": var(--background-color-primary),
);

// Border
$border-width: 1px;
$border-style: solid;
$border: (
    "width": $border-width,
    "width-base": $border-width,
    "style": $border-style,
    "style-base": $border-style,
    "": $border-width $border-style var(--border-color-base),
    "base": $border-width $border-style var(--border-color-base),
    "dash": $border-width dashed var(--border-color-base),
    "hover": $border-width $border-style var(--border-color-hover),
);

// Box-shadow
$box-shadow-color-base: var(--color-black-opacity-2);
$box-shadow-color-light: rgba(var(--color-black-rgb), 12%);
$box-shadow: (
    "": (
        0px 12px 32px 4px rgba(var(--color-black-rgb), 0.04),
        0px 8px 20px rgba(var(--color-black-rgb), 0.08),
    ),
    "base": 0px 2px 8px 0px $box-shadow-color-base,
    "bottom-select": 0px 2px 8px 0px var(--box-shadow-color-base),
    "bottom": 0px 4px 12px 0px var(--box-shadow-color-base),
    "left": -2px 0px 8px 0px var(--box-shadow-color-base),
    "right": 2px 0px 8px 0px var(--box-shadow-color-base),
    "top": 0px -2px 8px 0px var(--box-shadow-color-base),
    "input-focus": 0px 0px 0px 2px var(--color-primary-opacity-2-5),
    "light": 0px 0px 12px $box-shadow-color-light,
    "lighter": 0px 0px 6px $box-shadow-color-light,
    "dark": (
        0px 16px 48px 16px var(--color-black-opacity-0-8),
        0px 12px 32px $box-shadow-color-light,
        0px 8px 16px -8px rgba(var(--color-black-rgb), 16%),
    ),
);

// Disable default
$disabled: (
    "bg-color": var(--background-color-base),
    "text-color": var(--color-text-placeholder),
    "border-color": var(--border-color-light),
    "fill-color": var(--background-color-base),
    "color-base": var(--color-text-placeholder),
);

// overlay
$overlay-color: (
    "": var(--color-black-opacity-8),
    "light": var(--color-black-opacity-7),
    "lighter": var(--color-black-opacity-5),
);

// mask
$mask-color: (
    "": var(--color-white-opacity-9),
    "extra-light": var(--color-white-opacity-3),
);

// Scrollbar
$scrollbar: (
    "opacity": 0,
    "bg-color": transparent,
    "color": transparent,
    "hover-opacity": 0.45,
    "hover-bg-color": map.get($theme-colors, "white"),
    "color-hover": var(--color-white-opacity-2-5),
);

// Table
$table: (
    // "header-bg-color": var(--color-primary-dark-7),
    // "row-hover-bg-color": var(--color-primary-dark-7),
    // "row-current-bg-color": var(--color-primary-dark-7),
    // "row-striped": var(--color-primary-dark-7),

    "header-bg-color": #202e4a,
    "row-hover-bg-color": var(--color-primary-dark-7),
    "row-striped": #142240,
    "tr-bg-color": var(--background-color-primary),
);
