<template>
    <ecp-left-right-panel class="app-left-right-panel"
        :class="[{ collapsable, 'compact-content': compactContent }, type]"
        v-bind="{ width: collapsable ? 'auto' : width, rightScroll, containerId, dragAvailable, rightMinWidth }"
        :page-scroller="false">
        <template #left>
            <div class="app-left-right-panel--left" :class="{ collapse: collapseLeft }"
                :style="{ width: collapsable ? width : '' }">
                <div class="app-left-right-panel--left-content">
                    <slot name="left"></slot>
                </div>
            </div>
        </template>
        <template #default>
            <div class="app-left-right-panel--right">
                <div class="app-left-right-panel--collapse-trigger" @click="toggleCollapse()"
                    v-if="collapsable && !dragAvailable">
                    <ecp-icon :icon="collapseIcon"></ecp-icon>
                </div>
                <div class="app-left-right-panel--right-content">
                    <slot name="default"></slot>
                </div>
            </div>
        </template>
    </ecp-left-right-panel>
</template>

<script setup>
import { generateUUID } from '@ecp/ecp-ui-plus';

const props = defineProps({
    width: {
        type: [Number, String],
        default: 'auto'
    },
    rightScroll: {
        type: Boolean,
        default: false
    },
    // 容器id
    containerId: {
        type: String,
        default: generateUUID()
    },
    // 左栏是否允许拖拽改变宽度
    dragAvailable: {
        type: Boolean,
        default: false
    },
    // 左栏允许改变宽度时右栏的最小宽度
    rightMinWidth: {
        type: [Number, String],
        default: 200
    },

    type: {
        type: String,
        default: ''
    },

    compactContent: Boolean,

    collapsable: Boolean,
    collapse: Boolean
});

const emits = defineEmits(['update:collapse']);

const collapseLeft = computed({
    set (value) {
        emits('update:collapse', !!value);
    },
    get () {
        return props.collapse;
    }
});
const collapseIcon = computed(() => collapseLeft.value ? 'ecp-icon-arrow-right' : 'ecp-icon-arrow-left');

const toggleCollapse = (value) => {
    if (value !== undefined && value !== null) {
        collapseLeft.value = value;
        return;
    }
    collapseLeft.value = !collapseLeft.value;
};

</script>

<style lang="scss" scoped>
.app-left-right-panel {
    flex: 1 1 auto;

    &--collapse-trigger {
        width: 12px;
        height: 80px;
        // background-color: var(--color-primary);
        background-color: rgba(2, 16, 28, 0.45);
        // border-top-right-radius: var(--border-radius-base);
        // border-bottom-right-radius: var(--border-radius-base);
        font-size: var(--font-size-small);
        line-height: 80px;
        color: var(--color-white);
        text-align: center;
        cursor: pointer;
        position: absolute;
        top: 50%;
        left: 0;
        z-index: 2;
        transform: translateY(-50%);
        clip-path: polygon(0 0, calc(100% - 12px) 0, 100% 10px, 100% calc(100% - 10px), calc(100% - 12px) 100%, 0 100%);
    }

    &--left {
        transition-duration: 150ms;
        transition-property: width;

        &,
        &-content {
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            flex: 1 1 auto;
            display: flex;
            overflow: hidden;
            overflow-x: visible;
            position: relative;
        }

        &.collapse {
            width: 0 !important;
        }
    }

    &--right {
        position: relative;

        &,
        &-content {
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            flex: 1 1 auto;
            display: flex;
            overflow: hidden;
            overflow-x: visible;
            position: relative;
        }

        &-content {
            z-index: 1;
        }
    }

    &.compact-content {
        .app-left-right-panel--left {
            background-color: var(--background-color-dialog);
        }
    }
}

:deep(.app-left-right-panel.collapsable) {
    > .ecpp-left-right-panel-left {
        overflow: hidden;
        overflow-x: visible;
    }
}
</style>
